const { Person, School } = require("../models");
const path = require("path");
const fs = require("fs");
const { jsPDF } = require("jspdf");

const cardColors = {
  student: "#52C41A", // Green
  staff: "#4A90E2", // Blue
  non_teaching: "#FF6B35", // Orange
};

const generateCardHTML = async (person, school) => {
  const cardColor = cardColors[person.type];

  // Get photo path
  const photoPath = person.photo_path
    ? path.join(__dirname, "../../uploads/photos", person.photo_path)
    : null;

  const photoBase64 =
    photoPath && fs.existsSync(photoPath)
      ? `data:image/jpeg;base64,${fs
          .readFileSync(photoPath)
          .toString("base64")}`
      : "";

  // Get school logo
  const logoPath = school.logo_path
    ? path.join(__dirname, "../../uploads/school-assets", school.logo_path)
    : null;

  const logoBase64 =
    logoPath && fs.existsSync(logoPath)
      ? `data:image/jpeg;base64,${fs.readFileSync(logoPath).toString("base64")}`
      : "";

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <style>
        body {
          margin: 0;
          padding: 20px;
          font-family: Arial, sans-serif;
          background: #f0f0f0;
        }
        .card {
          width: 350px;
          height: 220px;
          background: white;
          border-radius: 15px;
          box-shadow: 0 4px 8px rgba(0,0,0,0.1);
          overflow: hidden;
          position: relative;
        }
        .header {
          background: ${cardColor};
          height: 80px;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .header::after {
          content: '';
          position: absolute;
          bottom: -20px;
          right: -20px;
          width: 100px;
          height: 100px;
          background: rgba(255,255,255,0.2);
          border-radius: 50%;
        }
        .school-logo {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: white;
          padding: 5px;
          margin-right: 10px;
        }
        .school-name {
          color: white;
          font-size: 14px;
          font-weight: bold;
          text-align: center;
        }
        .content {
          padding: 15px;
          display: flex;
          gap: 15px;
        }
        .photo-section {
          flex-shrink: 0;
        }
        .photo {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          border: 3px solid ${cardColor};
          object-fit: cover;
          background: #f5f5f5;
        }
        .info-section {
          flex: 1;
          font-size: 12px;
          line-height: 1.4;
        }
        .person-name {
          font-size: 14px;
          font-weight: bold;
          color: #333;
          margin-bottom: 5px;
          text-transform: uppercase;
        }
        .person-type {
          font-size: 12px;
          color: ${cardColor};
          font-weight: bold;
          margin-bottom: 8px;
          text-transform: capitalize;
        }
        .detail-row {
          display: flex;
          margin-bottom: 3px;
        }
        .detail-label {
          width: 80px;
          font-weight: bold;
          color: #666;
        }
        .detail-value {
          color: #333;
          flex: 1;
        }
        .footer {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: ${cardColor};
          color: white;
          padding: 8px 15px;
          font-size: 10px;
          text-align: center;
        }
        .school-address {
          font-size: 9px;
          margin-bottom: 2px;
        }
        .school-contact {
          font-size: 9px;
        }
      </style>
    </head>
    <body>
      <div class="card">
        <div class="header">
          ${
            logoBase64
              ? `<img src="${logoBase64}" alt="School Logo" class="school-logo">`
              : ""
          }
          <div class="school-name">
            ${school.name}
          </div>
        </div>

        <div class="content">
          <div class="photo-section">
            <img src="${
              photoBase64 ||
              "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDAiIGN5PSI0MCIgcj0iNDAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iNDAiIGN5PSIzMCIgcj0iMTIiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTYwIDYwQzYwIDUyLjI2ODYgNTMuNzMxNCA0NiA0NiA0NkgzNEMyNi4yNjg2IDQ2IDIwIDUyLjI2ODYgMjAgNjBWODBINjBWNjBaIiBmaWxsPSIjRDlEOUQ5Ii8+Cjwvc3ZnPgo="
            }" alt="Photo" class="photo">
          </div>

          <div class="info-section">
            <div class="person-name">${person.name}</div>
            <div class="person-type">${person.type.replace("_", " ")}</div>

            ${
              person.type === "student"
                ? `
              <div class="detail-row">
                <span class="detail-label">Class</span>
                <span class="detail-value">: ${person.class || "N/A"}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Roll No.</span>
                <span class="detail-value">: ${
                  person.roll_number || "N/A"
                }</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Parents Name</span>
                <span class="detail-value">: ${
                  person.parents_name || "N/A"
                }</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Contact No.</span>
                <span class="detail-value">: ${
                  person.contact_no || "N/A"
                }</span>
              </div>
            `
                : `
              <div class="detail-row">
                <span class="detail-label">ID</span>
                <span class="detail-value">: ${person.person_id}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Contact No.</span>
                <span class="detail-value">: ${
                  person.contact_no || "N/A"
                }</span>
              </div>
            `
            }
          </div>
        </div>

        <div class="footer">
          <div class="school-address">${school.address}</div>
          <div class="school-contact">Contact No.: ${school.phone}</div>
        </div>
      </div>
    </body>
    </html>
  `;

  return html;
};

const generateMultipleCardsHTML = async (persons, school) => {
  const cards = await Promise.all(
    persons.map((person) => generateCardHTML(person, school))
  );

  const multiCardHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <style>
        body {
          margin: 0;
          padding: 20px;
          font-family: Arial, sans-serif;
          background: #f0f0f0;
        }
        .cards-container {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 20px;
          max-width: 1200px;
          margin: 0 auto;
        }
        @media print {
          .cards-container {
            grid-template-columns: repeat(2, 1fr);
          }
          body {
            background: white;
          }
        }
      </style>
    </head>
    <body>
      <div class="cards-container">
        ${cards
          .map((card) =>
            card
              .replace(/<!DOCTYPE html>[\s\S]*?<body>/, "")
              .replace(/<\/body>[\s\S]*?<\/html>/, "")
          )
          .join("")}
      </div>
    </body>
    </html>
  `;

  return multiCardHTML;
};

// Generate PDF for a single card
const generateCardPDF = async (person, school) => {
  try {
    const cardColor = cardColors[person.type];

    // Create new PDF document - CR80 Standard ID card size in VERTICAL orientation to match frontend
    // Frontend: 325px x 500px (vertical) -> Print: 2.125" x 3.375" (54mm x 85.6mm) vertical
    const CARD_WIDTH_MM = 54; // 2.125 inches (width)
    const CARD_HEIGHT_MM = 85.6; // 3.375 inches (height) - VERTICAL like frontend

    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: [CARD_WIDTH_MM, CARD_HEIGHT_MM], // [54, 85.6] for vertical portrait
    });

    // Set background color
    doc.setFillColor(255, 255, 255);
    doc.rect(0, 0, CARD_WIDTH_MM, CARD_HEIGHT_MM, "F");

    // Header section with curved bottom (matching frontend design)
    const headerColor = hexToRgb(cardColor);
    const headerHeight = 30; // Proportional to frontend 120px on 500px height
    doc.setFillColor(headerColor.r, headerColor.g, headerColor.b);

    // Create curved header (approximated with rectangle and rounded corners)
    doc.rect(
      0,
      CARD_HEIGHT_MM - headerHeight,
      CARD_WIDTH_MM,
      headerHeight,
      "F"
    );

    // Add curved bottom effect (simplified)
    doc.setFillColor(headerColor.r, headerColor.g, headerColor.b);
    doc.ellipse(
      CARD_WIDTH_MM / 2,
      CARD_HEIGHT_MM - headerHeight,
      CARD_WIDTH_MM / 2,
      8,
      "F"
    );

    // School logo placeholder in header (positioned in top-left like frontend)
    doc.setFillColor(255, 255, 255);
    doc.circle(8, CARD_HEIGHT_MM - 8, 4, "F");

    // Person photo placeholder (circular, centered horizontally, overlapping header like frontend)
    const photoX = CARD_WIDTH_MM / 2;
    const photoY = CARD_HEIGHT_MM - headerHeight - 8; // Overlapping header
    const photoRadius = 10; // Larger photo like frontend
    doc.setFillColor(255, 255, 255);
    doc.circle(photoX, photoY, photoRadius, "F");
    doc.setDrawColor(headerColor.r, headerColor.g, headerColor.b);
    doc.setLineWidth(1);
    doc.circle(photoX, photoY, photoRadius, "S");

    // Person name (centered below photo, like frontend)
    doc.setTextColor(51, 51, 51);
    doc.setFontSize(10);
    doc.setFont("helvetica", "bold");
    const nameY = CARD_HEIGHT_MM - headerHeight - 25; // Below photo
    doc.text(person.name.toUpperCase(), CARD_WIDTH_MM / 2, nameY, {
      align: "center",
    });

    // Person type (centered below name)
    doc.setTextColor(102, 102, 102);
    doc.setFontSize(7);
    doc.setFont("helvetica", "bold");
    const typeText =
      person.type === "student"
        ? "Student"
        : person.type === "staff"
        ? "Teaching Staff"
        : "Non-Teaching Staff";
    doc.text(typeText, CARD_WIDTH_MM / 2, nameY - 5, { align: "center" });

    // Person details (centered layout like frontend)
    doc.setTextColor(102, 102, 102);
    doc.setFontSize(6);
    doc.setFont("helvetica", "normal");

    let yPos = nameY - 12;
    const leftMargin = 5;
    const labelWidth = 15; // Fixed width for labels to ensure alignment
    const colonPos = leftMargin + labelWidth;
    const valuePos = colonPos + 2;

    if (person.type === "student") {
      if (person.class) {
        doc.text(`Class`, leftMargin, yPos);
        doc.text(`:`, colonPos, yPos);
        doc.text(`${person.class}`, valuePos, yPos);
        yPos += 4;
      }
      if (person.roll_number) {
        doc.text(`Roll No.`, leftMargin, yPos);
        doc.text(`:`, colonPos, yPos);
        doc.text(`${person.roll_number}`, valuePos, yPos);
        yPos += 4;
      }
      if (person.parents_name) {
        doc.text(`Parents Name`, leftMargin, yPos);
        doc.text(`:`, colonPos, yPos);
        doc.text(`${person.parents_name}`, valuePos, yPos);
        yPos += 4;
      }
    } else {
      doc.text(`Employee ID`, leftMargin, yPos);
      doc.text(`:`, colonPos, yPos);
      doc.text(`${person.person_id}`, valuePos, yPos);
      yPos += 4;
      if (person.class) {
        doc.text(`Department`, leftMargin, yPos);
        doc.text(`:`, colonPos, yPos);
        doc.text(`${person.class}`, valuePos, yPos);
        yPos += 4;
      }
    }

    if (person.contact_no) {
      doc.text(`Contact No.`, leftMargin, yPos);
      doc.text(`:`, colonPos, yPos);
      doc.text(`${person.contact_no}`, valuePos, yPos);
    }

    // Footer with school details (at bottom like frontend)
    const footerHeight = 18;
    doc.setFillColor(headerColor.r, headerColor.g, headerColor.b);
    doc.rect(0, 0, CARD_WIDTH_MM, footerHeight, "F");

    doc.setTextColor(255, 255, 255);
    doc.setFontSize(7);
    doc.setFont("helvetica", "bold");
    doc.text(school.name.toUpperCase(), CARD_WIDTH_MM / 2, 15, {
      align: "center",
    });

    doc.setFontSize(5);
    doc.setFont("helvetica", "normal");
    doc.text(school.address, CARD_WIDTH_MM / 2, 10, { align: "center" });
    doc.text(`Contact No. : ${school.phone}`, CARD_WIDTH_MM / 2, 6, {
      align: "center",
    });

    return doc;
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw error;
  }
};

// Generate PDF for multiple cards
const generateMultipleCardsPDF = async (persons, school) => {
  try {
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // CR80 Standard card dimensions (VERTICAL orientation to match frontend)
    const cardWidth = 54; // 2.125 inches (width)
    const cardHeight = 85.6; // 3.375 inches (height) - VERTICAL
    const cardsPerRow = 3; // 3 vertical cards per row
    const cardsPerPage = 9; // 3 rows x 3 cards
    const marginX = (210 - cardsPerRow * cardWidth) / (cardsPerRow + 1);
    const marginY = 10;
    const spacingY = 8;

    let cardCount = 0;
    let currentPage = 0;

    for (let i = 0; i < persons.length; i++) {
      const person = persons[i];

      // Add new page if needed
      if (cardCount % cardsPerPage === 0 && cardCount > 0) {
        doc.addPage();
        currentPage++;
      }

      const cardIndex = cardCount % cardsPerPage;
      const row = Math.floor(cardIndex / cardsPerRow);
      const col = cardIndex % cardsPerRow;

      const x = marginX + col * (cardWidth + marginX);
      const y = marginY + row * (cardHeight + spacingY);

      // Generate individual card content
      await generateSingleCardInPDF(doc, person, school, x, y);

      cardCount++;
    }

    return doc;
  } catch (error) {
    console.error("Error generating multiple cards PDF:", error);
    throw error;
  }
};

// Helper function to generate a single card within a PDF
const generateSingleCardInPDF = async (doc, person, school, x, y) => {
  const cardColor = cardColors[person.type];
  const headerColor = hexToRgb(cardColor);

  // Card dimensions (VERTICAL to match frontend)
  const CARD_WIDTH_MM = 54; // 2.125 inches (width)
  const CARD_HEIGHT_MM = 85.6; // 3.375 inches (height) - VERTICAL

  // Card background
  doc.setFillColor(255, 255, 255);
  doc.rect(x, y, CARD_WIDTH_MM, CARD_HEIGHT_MM, "F");

  // Card border
  doc.setDrawColor(200, 200, 200);
  doc.rect(x, y, CARD_WIDTH_MM, CARD_HEIGHT_MM, "S");

  // Header section with curved bottom (matching frontend design)
  const headerHeight = 30;
  doc.setFillColor(headerColor.r, headerColor.g, headerColor.b);
  doc.rect(
    x,
    y + CARD_HEIGHT_MM - headerHeight,
    CARD_WIDTH_MM,
    headerHeight,
    "F"
  );

  // Add curved bottom effect
  doc.setFillColor(headerColor.r, headerColor.g, headerColor.b);
  doc.ellipse(
    x + CARD_WIDTH_MM / 2,
    y + CARD_HEIGHT_MM - headerHeight,
    CARD_WIDTH_MM / 2,
    8,
    "F"
  );

  // School logo placeholder in header (positioned in top-left like frontend)
  doc.setFillColor(255, 255, 255);
  doc.circle(x + 8, y + CARD_HEIGHT_MM - 8, 4, "F");

  // Photo placeholder (circular, centered horizontally, overlapping header like frontend)
  const photoX = x + CARD_WIDTH_MM / 2;
  const photoY = y + CARD_HEIGHT_MM - headerHeight - 8;
  const photoRadius = 10;
  doc.setFillColor(255, 255, 255);
  doc.circle(photoX, photoY, photoRadius, "F");
  doc.setDrawColor(headerColor.r, headerColor.g, headerColor.b);
  doc.setLineWidth(1);
  doc.circle(photoX, photoY, photoRadius, "S");

  // Person name (centered below photo, like frontend)
  doc.setTextColor(51, 51, 51);
  doc.setFontSize(10);
  doc.setFont("helvetica", "bold");
  const nameY = y + CARD_HEIGHT_MM - headerHeight - 25; // Below photo
  doc.text(person.name.toUpperCase(), x + CARD_WIDTH_MM / 2, nameY, {
    align: "center",
  });

  // Person type (centered below name)
  doc.setTextColor(102, 102, 102);
  doc.setFontSize(7);
  doc.setFont("helvetica", "bold");
  const typeText =
    person.type === "student"
      ? "Student"
      : person.type === "staff"
      ? "Teaching Staff"
      : "Non-Teaching Staff";
  doc.text(typeText, x + CARD_WIDTH_MM / 2, nameY - 5, { align: "center" });

  // Person details (centered layout like frontend)
  doc.setTextColor(102, 102, 102);
  doc.setFontSize(6);
  doc.setFont("helvetica", "normal");

  let yPos = nameY - 12;
  const leftMargin = x + 5;
  const labelWidth = 15; // Fixed width for labels to ensure alignment
  const colonPos = leftMargin + labelWidth;
  const valuePos = colonPos + 2;

  if (person.type === "student") {
    if (person.class) {
      doc.text(`Class`, leftMargin, yPos);
      doc.text(`:`, colonPos, yPos);
      doc.text(`${person.class}`, valuePos, yPos);
      yPos += 4;
    }
    if (person.roll_number) {
      doc.text(`Roll No.`, leftMargin, yPos);
      doc.text(`:`, colonPos, yPos);
      doc.text(`${person.roll_number}`, valuePos, yPos);
      yPos += 4;
    }
    if (person.parents_name) {
      doc.text(`Parents Name`, leftMargin, yPos);
      doc.text(`:`, colonPos, yPos);
      doc.text(`${person.parents_name}`, valuePos, yPos);
      yPos += 4;
    }
  } else {
    doc.text(`Employee ID`, leftMargin, yPos);
    doc.text(`:`, colonPos, yPos);
    doc.text(`${person.person_id}`, valuePos, yPos);
    yPos += 4;
    if (person.class) {
      doc.text(`Department`, leftMargin, yPos);
      doc.text(`:`, colonPos, yPos);
      doc.text(`${person.class}`, valuePos, yPos);
      yPos += 4;
    }
  }

  if (person.contact_no) {
    doc.text(`Contact No.`, leftMargin, yPos);
    doc.text(`:`, colonPos, yPos);
    doc.text(`${person.contact_no}`, valuePos, yPos);
  }

  // Footer with school details (at bottom like frontend)
  const footerHeight = 18;
  doc.setFillColor(headerColor.r, headerColor.g, headerColor.b);
  doc.rect(x, y, CARD_WIDTH_MM, footerHeight, "F");

  doc.setTextColor(255, 255, 255);
  doc.setFontSize(7);
  doc.setFont("helvetica", "bold");
  doc.text(school.name.toUpperCase(), x + CARD_WIDTH_MM / 2, y + 15, {
    align: "center",
  });

  doc.setFontSize(5);
  doc.setFont("helvetica", "normal");
  doc.text(school.address, x + CARD_WIDTH_MM / 2, y + 10, { align: "center" });
  doc.text(`Contact No. : ${school.phone}`, x + CARD_WIDTH_MM / 2, y + 6, {
    align: "center",
  });
};

// Helper function to convert hex color to RGB
const hexToRgb = (hex) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : { r: 0, g: 0, b: 0 };
};

module.exports = {
  generateCardPDF,
  generateMultipleCardsPDF,
};
