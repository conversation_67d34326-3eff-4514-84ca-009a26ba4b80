/**
 * Utility functions for image handling and placeholder generation
 */

export interface ImageCompressionOptions {
  maxSizeMB?: number;
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
}

export interface ImageValidationResult {
  isValid: boolean;
  error?: string;
  file?: File;
}
/**
 * Generate initials from a person's name
 * @param name - Full name of the person
 * @returns Initials (up to 2 characters)
 */
export const generateInitials = (name: string): string => {
  if (!name || typeof name !== "string") {
    return "NA";
  }

  const words = name.trim().split(/\s+/);

  if (words.length === 1) {
    // Single word - take first 2 characters
    return words[0].substring(0, 2).toUpperCase();
  } else {
    // Multiple words - take first character of first two words
    return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
  }
};

/**
 * Generate a colored background based on person type/role
 * @param type - Person type (student, staff, non_teaching, teacher)
 * @returns CSS color class
 */
export const getInitialsBackgroundColor = (type: string): string => {
  switch (type.toLowerCase()) {
    case "student":
      return "bg-green-500";
    case "staff":
    case "teacher":
    case "teaching":
      return "bg-blue-500";
    case "non_teaching":
    case "non-teaching":
      return "bg-orange-500";
    default:
      return "bg-gray-500";
  }
};

/**
 * Check if an image URL is valid/accessible
 * @param imageUrl - URL of the image
 * @returns Promise<boolean> - true if image is accessible
 */
export const isImageAccessible = (imageUrl: string): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!imageUrl || imageUrl.trim() === "") {
      resolve(false);
      return;
    }

    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = imageUrl;
  });
};

/**
 * Generate SVG initials placeholder
 * @param initials - Initials to display
 * @param backgroundColor - Background color (hex or CSS color)
 * @param textColor - Text color (default: white)
 * @param size - Size of the SVG (default: 80)
 * @returns Base64 encoded SVG data URL
 */
export const generateInitialsSvg = (
  initials: string,
  backgroundColor: string = "#6B7280",
  textColor: string = "#FFFFFF",
  size: number = 80
): string => {
  const svg = `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="${size}" height="${size}" fill="${backgroundColor}"/>
      <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="${textColor}" font-family="Arial, sans-serif" font-size="${
    size * 0.4
  }" font-weight="600">
        ${initials}
      </text>
    </svg>
  `;

  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

/**
 * Get role-based color for modern design
 * @param designation - Person's designation/role
 * @returns Color object with background and border colors
 */
export const getRoleBasedColors = (
  designation: string
): { bgColor: string; borderColor: string; initialsColor: string } => {
  const role = designation.toLowerCase();

  if (role.includes("student")) {
    return {
      bgColor: "#22C55E", // green-500
      borderColor: "#16A34A", // green-600
      initialsColor: "#22C55E",
    };
  } else if (
    role.includes("teacher") ||
    role.includes("instructor") ||
    role.includes("professor") ||
    role.includes("staff")
  ) {
    return {
      bgColor: "#3B82F6", // blue-500
      borderColor: "#2563EB", // blue-600
      initialsColor: "#3B82F6",
    };
  } else {
    // Non-teaching staff
    return {
      bgColor: "#F97316", // orange-500
      borderColor: "#EA580C", // orange-600
      initialsColor: "#F97316",
    };
  }
};

/**
 * Validates an image file
 */
export const validateImageFile = (file: File): ImageValidationResult => {
  // Check if it's an image
  if (!file.type.startsWith("image/")) {
    return {
      isValid: false,
      error: "Please select an image file (PNG, JPG, JPEG, GIF)",
    };
  }

  // Check file size (100MB absolute limit)
  const maxAbsoluteSize = 100 * 1024 * 1024; // 100MB
  if (file.size > maxAbsoluteSize) {
    return {
      isValid: false,
      error: "File size must be less than 100MB",
    };
  }

  return {
    isValid: true,
    file,
  };
};

/**
 * Compresses an image file
 */
export const compressImage = (
  file: File,
  options: ImageCompressionOptions = {}
): Promise<File> => {
  const {
    maxSizeMB = 2,
    maxWidth = 1024,
    maxHeight = 1024,
    quality = 0.8,
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions while maintaining aspect ratio
      let { width, height } = img;

      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error("Failed to compress image"));
            return;
          }

          const compressedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: Date.now(),
          });

          // Check if compression was successful
          if (compressedFile.size <= maxSizeMB * 1024 * 1024) {
            resolve(compressedFile);
          } else if (quality > 0.1) {
            // Try with lower quality
            compressImage(file, { ...options, quality: quality - 0.1 })
              .then(resolve)
              .catch(reject);
          } else {
            reject(new Error(`Unable to compress image below ${maxSizeMB}MB`));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error("Failed to load image"));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Processes an image file with validation and optional compression
 */
export const processImageFile = async (
  file: File,
  options: ImageCompressionOptions & { autoCompress?: boolean } = {}
): Promise<ImageValidationResult> => {
  // First validate the file
  const validation = validateImageFile(file);
  if (!validation.isValid) {
    return validation;
  }

  const { autoCompress = true, maxSizeMB = 2 } = options;

  try {
    let processedFile = file;

    // Auto-compress if file is larger than the target size
    if (autoCompress && file.size > maxSizeMB * 1024 * 1024) {
      processedFile = await compressImage(file, options);
      console.log(
        `Image compressed from ${(file.size / 1024 / 1024).toFixed(2)}MB to ${(
          processedFile.size /
          1024 /
          1024
        ).toFixed(2)}MB`
      );
    }

    return {
      isValid: true,
      file: processedFile,
    };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : "Failed to process image",
    };
  }
};

/**
 * Formats file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};
