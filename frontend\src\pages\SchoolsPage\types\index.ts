export interface SchoolFormData {
  name: string;
  address: string;
  phone: string;
  email: string;
  validity_date?: string;
}

export interface School {
  id: number;
  name: string;
  address: string;
  phone: string;
  email?: string;
  logo_path?: string;
  stamp_path?: string;
  signature_path?: string;
  validity_date?: string;
}

export interface SchoolFormProps {
  isVisible: boolean;
  editingSchool: School | null;
  formData: SchoolFormData;
  loading: boolean;
  onSubmit: (e: React.FormEvent, logoFile?: File) => void;
  onCancel: () => void;
  onFormDataChange: (data: SchoolFormData) => void;
}

export interface SchoolCardProps {
  school: School;
  uploadingLogo: boolean;
  onEdit: (school: School) => void;
  onDelete: (school: School) => void;
  onLogoUpload: (schoolId: number, file: File) => void;
  onSignatureUpload?: (school: School) => void;
  onValidityDateUpdate?: (school: School) => void;
}

export interface SchoolsListProps {
  schools: School[];
  uploadingLogo: number | null;
  onEdit: (school: School) => void;
  onDelete: (school: School) => void;
  onLogoUpload: (schoolId: number, file: File) => void;
  onSignatureUpload?: (school: School) => void;
  onValidityDateUpdate?: (school: School) => void;
  onCreateFirst: () => void;
}
