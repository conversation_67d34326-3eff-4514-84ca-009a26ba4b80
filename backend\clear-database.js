const sequelize = require('./src/config/database');
const { School, Person, CsvBatch } = require('./src/models');
const fs = require('fs');
const path = require('path');

async function clearDatabase() {
  try {
    console.log('🗑️  Starting database cleanup...');
    
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Clear uploaded files
    console.log('🧹 Clearing uploaded files...');
    await clearUploadedFiles();

    // Clear all tables in the correct order (respecting foreign key constraints)
    console.log('🗃️  Clearing database tables...');
    
    // Delete all persons first (they reference schools and csv_batches)
    const personCount = await Person.count();
    if (personCount > 0) {
      await Person.destroy({ where: {}, truncate: true });
      console.log(`   ✅ Deleted ${personCount} persons`);
    }

    // Delete all CSV batches
    const csvBatchCount = await CsvBatch.count();
    if (csvBatchCount > 0) {
      await CsvBatch.destroy({ where: {}, truncate: true });
      console.log(`   ✅ Deleted ${csvBatchCount} CSV batches`);
    }

    // Delete all schools
    const schoolCount = await School.count();
    if (schoolCount > 0) {
      await School.destroy({ where: {}, truncate: true });
      console.log(`   ✅ Deleted ${schoolCount} schools`);
    }

    // Reset SQLite sequences (auto-increment counters)
    console.log('🔄 Resetting auto-increment sequences...');
    await sequelize.query("DELETE FROM sqlite_sequence WHERE name IN ('persons', 'schools', 'csv_batches')");
    console.log('   ✅ Auto-increment sequences reset');

    // Verify tables are empty
    const finalPersonCount = await Person.count();
    const finalSchoolCount = await School.count();
    const finalCsvBatchCount = await CsvBatch.count();

    console.log('\n📊 Final verification:');
    console.log(`   Persons: ${finalPersonCount}`);
    console.log(`   Schools: ${finalSchoolCount}`);
    console.log(`   CSV Batches: ${finalCsvBatchCount}`);

    if (finalPersonCount === 0 && finalSchoolCount === 0 && finalCsvBatchCount === 0) {
      console.log('\n🎉 Database cleared successfully!');
    } else {
      console.log('\n⚠️  Warning: Some records may still exist');
    }

    await sequelize.close();
    console.log('✅ Database connection closed');

  } catch (error) {
    console.error('❌ Error clearing database:', error);
    process.exit(1);
  }
}

async function clearUploadedFiles() {
  const uploadDirs = [
    path.join(__dirname, 'uploads/photos'),
    path.join(__dirname, 'uploads/csv'),
    path.join(__dirname, 'uploads/previews'),
    path.join(__dirname, 'uploads/school-assets')
  ];

  for (const dir of uploadDirs) {
    if (fs.existsSync(dir)) {
      const files = fs.readdirSync(dir);
      let deletedCount = 0;
      
      for (const file of files) {
        // Skip .gitkeep files and directories
        if (file === '.gitkeep' || file.startsWith('.')) continue;
        
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isFile()) {
          fs.unlinkSync(filePath);
          deletedCount++;
        }
      }
      
      if (deletedCount > 0) {
        console.log(`   ✅ Deleted ${deletedCount} files from ${path.basename(dir)}/`);
      }
    }
  }
}

// Add confirmation prompt
function askForConfirmation() {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('⚠️  This will permanently delete ALL data from the database and uploaded files. Are you sure? (yes/no): ', (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
    });
  });
}

// Main execution
async function main() {
  console.log('🚨 DATABASE CLEAR UTILITY 🚨');
  console.log('This will delete:');
  console.log('  - All persons (students, staff, non-teaching)');
  console.log('  - All schools');
  console.log('  - All CSV batch records');
  console.log('  - All uploaded files (photos, CSVs, previews, school assets)');
  console.log('  - Reset auto-increment counters');
  console.log('');

  const confirmed = await askForConfirmation();
  
  if (confirmed) {
    await clearDatabase();
  } else {
    console.log('❌ Operation cancelled');
    process.exit(0);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { clearDatabase, clearUploadedFiles };
