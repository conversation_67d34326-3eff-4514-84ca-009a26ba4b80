const sequelize = require("./src/config/database");
const migration = require("./src/migrations/add-date-of-birth-to-persons.js");

async function runDateOfBirthMigration() {
  try {
    console.log("Starting date_of_birth migration...");

    // Authenticate database connection
    await sequelize.authenticate();
    console.log("Database connection established successfully.");

    // Run the Sequelize migration
    console.log("Running add-date-of-birth-to-persons migration...");
    await migration.up(sequelize.getQueryInterface(), sequelize);

    console.log("Date of birth migration completed successfully.");

    // Verify the migration by checking table structure
    console.log("Verifying migration...");
    const tableDescription = await sequelize
      .getQueryInterface()
      .describeTable("persons");

    if (tableDescription.date_of_birth) {
      console.log("✓ Date of birth column verified in persons table");
      console.log("Date of birth column details:", {
        type: tableDescription.date_of_birth.type,
        allowNull: tableDescription.date_of_birth.allowNull,
      });
    } else {
      console.log("✗ Date of birth column not found after migration");
    }

    await sequelize.close();
    console.log("Migration process completed.");
  } catch (error) {
    console.error("Migration failed:", error);
    console.error("Error details:", error.message);
    if (error.sql) {
      console.error("SQL:", error.sql);
    }

    try {
      await sequelize.close();
    } catch (closeError) {
      console.error("Error closing database connection:", closeError);
    }

    process.exit(1);
  }
}

runDateOfBirthMigration();
