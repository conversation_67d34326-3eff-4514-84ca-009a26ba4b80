const fs = require("fs");
const csv = require("csv-parser");
const { Person, CsvBatch, School } = require("../models");

// Function to generate next sequential person ID for a school
const generateNextPersonId = async (schoolId) => {
  // Find the highest person_id for this school
  const maxPersonId = await Person.max("person_id", {
    where: {
      school_id: schoolId,
    },
  });

  // Return next sequential number (starting from 1 if no persons exist)
  return (maxPersonId || 0) + 1;
};

const processCsvFile = async (filePath, filename, schoolId = null) => {
  const results = [];
  const errors = [];

  console.log(`Starting CSV processing: ${filename}, schoolId: ${schoolId}`);

  return new Promise((resolve) => {
    fs.createReadStream(filePath)
      .pipe(csv())
      .on("data", (data) => {
        try {
          console.log(`Processing row:`, data);

          // Validate required fields (person_id is now optional)
          if (!data.name || !data.type) {
            errors.push(
              `Missing required fields (name, type) in row: ${JSON.stringify(
                data
              )}`
            );
            return;
          }

          // Validate type
          if (!["student", "staff", "non_teaching"].includes(data.type)) {
            errors.push(
              `Invalid type "${data.type}" in row: ${JSON.stringify(data)}`
            );
            return;
          }

          // For students, class is required but roll_number is optional
          if (data.type === "student" && !data.class) {
            errors.push(
              `Students must have class in row: ${JSON.stringify(data)}`
            );
            return;
          }

          // Use provided person_id or mark for generation
          const personId =
            data.person_id && data.person_id.toString().trim()
              ? data.person_id.toString().trim()
              : null; // Will be generated later

          // Validate date_of_birth if provided
          let dateOfBirth = null;
          if (data.date_of_birth) {
            const dobString = data.date_of_birth.toString().trim();
            if (dobString) {
              const dobDate = new Date(dobString);
              const today = new Date();
              const maxAge = new Date();
              maxAge.setFullYear(today.getFullYear() - 100);

              if (isNaN(dobDate.getTime())) {
                errors.push(
                  `Invalid date of birth format "${dobString}" in row: ${JSON.stringify(
                    data
                  )}`
                );
                return;
              }
              if (dobDate > today) {
                errors.push(
                  `Date of birth cannot be in the future in row: ${JSON.stringify(
                    data
                  )}`
                );
                return;
              }
              if (dobDate < maxAge) {
                errors.push(
                  `Date of birth cannot be more than 100 years ago in row: ${JSON.stringify(
                    data
                  )}`
                );
                return;
              }
              dateOfBirth = dobDate.toISOString().split("T")[0]; // Format as YYYY-MM-DD
            }
          }

          results.push({
            person_id: personId,
            name: data.name.toString().trim(),
            type: data.type,
            class: data.class?.toString().trim(),
            section: data.section?.toString().trim(),
            roll_number: data.roll_number?.toString().trim(),
            parents_name: data.parents_name?.toString().trim(),
            contact_no: data.contact_no?.toString().trim(),
            department: data.department?.toString().trim(),
            designation: data.designation?.toString().trim(),
            date_of_birth: dateOfBirth,
          });
        } catch (error) {
          errors.push(
            `Error processing row: ${JSON.stringify(data)} - ${error}`
          );
        }
      })
      .on("end", async () => {
        try {
          console.log(
            `CSV parsing completed. Found ${results.length} valid rows, ${errors.length} errors`
          );

          // Validate school_id if provided
          if (schoolId) {
            console.log(`Validating school with ID: ${schoolId}`);
            const school = await School.findByPk(schoolId);
            if (!school) {
              const error = `School with ID ${schoolId} not found`;
              console.log(`School validation error: ${error}`);
              resolve({
                success: false,
                processed: 0,
                errors: [...errors, error],
              });
              return;
            }
            console.log(`School found: ${school.name}`);
          } else {
            // If no school_id provided, get the first school or create default
            const defaultSchool = await School.findOne();
            if (!defaultSchool) {
              resolve({
                success: false,
                processed: 0,
                errors: [
                  ...errors,
                  "No school found. Please create a school first.",
                ],
              });
              return;
            }
            schoolId = defaultSchool.id;
          }

          // Create CSV batch record
          const csvBatch = await CsvBatch.create({
            filename,
            upload_date: new Date(),
            total_count: results.length,
            processed_count: 0,
            status: "processing",
            error_log: errors.length > 0 ? errors.join("\n") : null,
          });

          let processed = 0;
          console.log(
            `Starting database insertion for ${results.length} records`
          );

          // Process each row
          for (const row of results) {
            try {
              // Generate person_id if not provided or invalid
              if (!row.person_id || isNaN(parseInt(row.person_id))) {
                row.person_id = await generateNextPersonId(schoolId);
                console.log(
                  `Generated person_id: ${row.person_id} for ${row.name}`
                );
              } else {
                // Convert to integer if provided
                row.person_id = parseInt(row.person_id);
              }

              console.log(`Processing person: ${row.person_id} - ${row.name}`);

              // Check if person already exists in this specific school
              const existingPerson = await Person.findOne({
                where: {
                  person_id: row.person_id,
                  school_id: schoolId,
                },
              });

              if (existingPerson) {
                console.log(`Updating existing person: ${row.person_id}`);
                // Update existing person
                await existingPerson.update({
                  ...row,
                  csv_batch_id: csvBatch.id,
                  school_id: schoolId,
                });
              } else {
                console.log(`Creating new person: ${row.person_id}`);
                // Create new person
                await Person.create({
                  ...row,
                  csv_batch_id: csvBatch.id,
                  school_id: schoolId,
                });
              }
              processed++;
              console.log(`Successfully processed person: ${row.person_id}`);
            } catch (error) {
              const errorMsg = `Error saving person ${
                row.person_id || "unknown"
              }: ${error.message}`;
              console.error(errorMsg);
              console.error("Full error:", error);
              errors.push(errorMsg);
            }
          }

          // Update batch status
          await csvBatch.update({
            processed_count: processed,
            status: errors.length > 0 ? "completed" : "completed",
            error_log: errors.length > 0 ? errors.join("\n") : null,
          });

          console.log(
            `CSV processing completed. Processed: ${processed}, Errors: ${errors.length}`
          );
          if (errors.length > 0) {
            console.log("Errors encountered:", errors);
          }

          resolve({
            success: true,
            processed,
            errors,
            batchId: csvBatch.id,
          });
        } catch (error) {
          resolve({
            success: false,
            processed: 0,
            errors: [...errors, `Database error: ${error}`],
          });
        }
      })
      .on("error", (error) => {
        resolve({
          success: false,
          processed: 0,
          errors: [...errors, `File reading error: ${error.message}`],
        });
      });
  });
};

module.exports = { processCsvFile };
