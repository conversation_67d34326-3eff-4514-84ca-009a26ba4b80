// Card component exports
export { default as <PERSON><PERSON><PERSON><PERSON> } from './CardRenderer';
export { default as VerticalCardRenderer } from './VerticalCardRenderer';
export { default as MaiValleyCardRenderer } from './MaiValleyCardRenderer';
export { default as CardHeader } from './CardHeader';
export { default as Card<PERSON>ody } from './CardBody';
export { default as CardFooter } from './CardFooter';
export { default as CardPhoto } from './CardPhoto';

// Type exports
export type {
  PersonData,
  SchoolData,
  CardTheme,
  CardDimensions,
  CardTemplate,
  CardConfig,
  CardRendererProps,
  CardHeaderProps,
  CardBodyProps,
  CardFooterProps,
  CardPhotoProps
} from './types';

// Utility exports
export {
  CARD_COLORS,
  CARD_LABELS,
  DEFAULT_DIMENSIONS,
  DEFAULT_THEMES
} from './types';

// Utility function exports
export {
  createCardConfig,
  createDefaultSchool,
  createMaiValleySchool,
  createMaiValleyCardConfig,
  validatePersonForCard,
  getCardTheme,
  transformPersonData,
  transformSchoolData,
  createBatchCardConfigs,
  filterPersonsByType
} from './utils';
