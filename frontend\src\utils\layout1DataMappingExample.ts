// Example usage of Layout1 data mapping utilities
// This file demonstrates how to use the separated Layout1 data mapping logic

import { PersonData, SchoolData } from "../components/Card/types";
import {
  transformToLayout1IdCardProps,
  transformPersonsToLayout1IdCards,
  getLayout1IdCardsByType,
  createLayout1FieldVisibility,
  getOptimalSchoolNameFormat,
  renderSchoolNameForHeader,
  formatLayout1SchoolNameSingleLine,
  DEFAULT_FIELD_VISIBILITY,
} from "./layout1DataMapping";

// Example: Transform a single person for Layout1
export const exampleSinglePersonLayout1 = (
  person: PersonData,
  school: SchoolData
) => {
  // Basic transformation with default field visibility
  const basicCard = transformToLayout1IdCardProps(person, school);

  // Transformation with custom field visibility (hide parent name and roll number)
  const customFieldVisibility = createLayout1FieldVisibility({
    parents_name: false,
    roll_number: false,
  });

  const customCard = transformToLayout1IdCardProps(
    person,
    school,
    customFieldVisibility
  );

  // Transformation with custom values override
  const customValuesCard = transformToLayout1IdCardProps(
    person,
    school,
    DEFAULT_FIELD_VISIBILITY,
    {
      motto: '"Excellence in Education"',
      validity: "2084/12/31",
    }
  );

  return { basicCard, customCard, customValuesCard };
};

// Example: Transform multiple persons for Layout1
export const exampleMultiplePersonsLayout1 = (
  persons: PersonData[],
  school: SchoolData
) => {
  // Transform all persons with default settings
  const allCards = transformPersonsToLayout1IdCards(persons, school);

  // Transform only students with custom field visibility
  const studentFieldVisibility = createLayout1FieldVisibility({
    parents_name: true,
    roll_number: true,
    section: false, // Hide section for students
  });

  const studentCards = getLayout1IdCardsByType(
    persons,
    school,
    "student",
    studentFieldVisibility
  );

  // Transform only staff with custom field visibility
  const staffFieldVisibility = createLayout1FieldVisibility({
    parents_name: false, // Staff don't need parent name
    roll_number: true, // Show as ID for staff
    department: true,
  });

  const staffCards = getLayout1IdCardsByType(
    persons,
    school,
    "staff",
    staffFieldVisibility
  );

  return { allCards, studentCards, staffCards };
};

// Example: Create different field visibility configurations for Layout1
export const exampleFieldVisibilityConfigs = () => {
  // Configuration for students - show all relevant fields
  const studentConfig = createLayout1FieldVisibility({
    name: true,
    class: true,
    section: true,
    roll_number: true,
    parents_name: true,
    contact_no: true,
    department: false, // Not relevant for students
    designation: false, // Not relevant for students
  });

  // Configuration for teaching staff - hide student-specific fields
  const teachingStaffConfig = createLayout1FieldVisibility({
    name: true,
    class: false, // Not relevant for staff
    section: false, // Not relevant for staff
    roll_number: true, // Show as ID
    parents_name: false, // Not relevant for staff
    contact_no: true,
    department: true,
    designation: true,
  });

  // Configuration for non-teaching staff - similar to teaching staff
  const nonTeachingStaffConfig = createLayout1FieldVisibility({
    name: true,
    class: false,
    section: false,
    roll_number: true, // Show as ID
    parents_name: false,
    contact_no: true,
    department: true,
    designation: true,
  });

  // Minimal configuration - only show essential fields
  const minimalConfig = createLayout1FieldVisibility({
    name: true,
    class: true,
    section: false,
    roll_number: false,
    parents_name: false,
    contact_no: true,
    department: true,
    designation: false,
  });

  return {
    studentConfig,
    teachingStaffConfig,
    nonTeachingStaffConfig,
    minimalConfig,
  };
};

// Example: Batch processing for different person types with Layout1
export const exampleBatchProcessingLayout1 = (
  persons: PersonData[],
  school: SchoolData
) => {
  const fieldConfigs = exampleFieldVisibilityConfigs();

  // Process students with student-specific configuration
  const students = getLayout1IdCardsByType(
    persons,
    school,
    "student",
    fieldConfigs.studentConfig
  );

  // Process teaching staff with staff-specific configuration
  const teachingStaff = getLayout1IdCardsByType(
    persons,
    school,
    "staff",
    fieldConfigs.teachingStaffConfig
  );

  // Process non-teaching staff with non-teaching staff configuration
  const nonTeachingStaff = getLayout1IdCardsByType(
    persons,
    school,
    "non_teaching",
    fieldConfigs.nonTeachingStaffConfig
  );

  return {
    students,
    teachingStaff,
    nonTeachingStaff,
    totalCards:
      students.length + teachingStaff.length + nonTeachingStaff.length,
  };
};

// Example: School name header formatting for Layout1
export const exampleSchoolNameFormatting = () => {
  const schoolNames = [
    "Mai Valley Boarding School",
    "Kathmandu International School",
    "St. Mary's High School",
    "Nepal National Secondary School",
    "Bright Future Academy",
    "The Global College of Engineering and Management",
  ];

  return schoolNames.map((name) => {
    // Get optimal formatting
    const optimal = getOptimalSchoolNameFormat(name);

    // Get header rendering info
    const headerInfo = renderSchoolNameForHeader(name);

    // Get single line version
    const singleLine = formatLayout1SchoolNameSingleLine(name);

    return {
      original: name,
      optimal,
      headerInfo,
      singleLine,
      // Example of how to use in React component
      reactExample: {
        lines: headerInfo.lines,
        className: headerInfo.className,
        shouldWrap: headerInfo.shouldWrap,
      },
    };
  });
};

export default {
  exampleSinglePersonLayout1,
  exampleMultiplePersonsLayout1,
  exampleFieldVisibilityConfigs,
  exampleBatchProcessingLayout1,
  exampleSchoolNameFormatting,
};
