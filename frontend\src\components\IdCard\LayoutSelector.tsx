export type LayoutType = "layout1" | "layout2" | "layout3" | "layout4";
export type BgColorType = "green" | "blue" | "orange";

interface LayoutSelectorProps {
  selectedLayout: LayoutType;
  onLayoutChange: (layout: LayoutType) => void;
  selectedBgColor?: BgColorType;
  onBgColorChange?: (color: BgColorType) => void;
}

const layouts = [
  {
    id: "layout1" as LayoutType,
    name: "Template 1",
    description: "Traditional school ID card with decorative elements",
    preview: "🎓",
  },
  {
    id: "layout2" as LayoutType,
    name: "Template 2",
    description: "Professional ID card with automatic color coding",
    preview: "🎨",
  },
  {
    id: "layout3" as LayoutType,
    name: "Template 3",
    description: "Professional ID card without parent name field",
    preview: "📋",
  },
  {
    id: "layout4" as LayoutType,
    name: "Template 4",
    description: "Credit card style with curved blue design",
    preview: "💳",
  },
];

// const bgColorOptions = [
//   {
//     id: "green" as BgColorType,
//     name: "Green",
//     color: "bg-gradient-to-r from-[#5BAE44] to-[#379140]",
//     preview: "🟢",
//   },
//   {
//     id: "blue" as BgColorType,
//     name: "Blue",
//     color: "bg-gradient-to-r from-[#1998BB] to-[#2C6088]",
//     preview: "🔵",
//   },
//   {
//     id: "orange" as BgColorType,
//     name: "Orange",
//     color: "bg-gradient-to-r from-[#F06024] to-[#EF4F26]",
//     preview: "🟠",
//   },
// ];

export default function LayoutSelector({
  selectedLayout,
  onLayoutChange,
  selectedBgColor = "green",
  onBgColorChange,
}: LayoutSelectorProps) {
  return (
    <div className="mb-2">
      <h3 className="text-md font-medium text-gray-800 mb-2">
        Choose Card Layout
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
        {layouts.map((layout) => (
          <div
            key={layout.id}
            onClick={() => onLayoutChange(layout.id)}
            className={`
              cursor-pointer rounded-md border-2 p-1.5 transition-all duration-200 hover:shadow-sm
              ${
                selectedLayout === layout.id
                  ? "border-blue-500 bg-blue-50 shadow-sm"
                  : "border-gray-200 bg-white hover:border-gray-300"
              }
            `}
          >
            <div className="text-center">
              <div className="text-lg mb-1">{layout.preview}</div>
              <div
                className={`text-sm font-medium mb-1 ${
                  selectedLayout === layout.id
                    ? "text-blue-700"
                    : "text-gray-800"
                }`}
              >
                {layout.name}
              </div>
              <div
                className={`text-xs ${
                  selectedLayout === layout.id
                    ? "text-blue-600"
                    : "text-gray-600"
                }`}
              >
                {layout.description}
              </div>
              {selectedLayout === layout.id && (
                <div className="mt-1">
                  <span className="inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    ✓ Selected
                  </span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
