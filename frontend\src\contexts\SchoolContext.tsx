import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from "react";
import axios from "axios";

interface School {
  id: number;
  name: string;
  address: string;
  phone: string;
  email?: string;
  logo_path?: string;
  stamp_path?: string;
  signature_path?: string;
  validity_date?: string;
  created_at?: string;
  updated_at?: string;
}

interface SchoolContextType {
  schools: School[];
  selectedSchool: School | null;
  loading: boolean;
  error: string | null;
  selectSchool: (school: School) => void;
  refreshSchools: () => Promise<void>;
  createSchool: (
    schoolData: Omit<School, "id" | "created_at" | "updated_at">
  ) => Promise<School>;
  updateSchool: (id: number, schoolData: Partial<School>) => Promise<School>;
  deleteSchool: (id: number) => Promise<void>;
}

const SchoolContext = createContext<SchoolContextType | undefined>(undefined);

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

interface SchoolProviderProps {
  children: ReactNode;
}

export const SchoolProvider: React.FC<SchoolProviderProps> = ({ children }) => {
  const [schools, setSchools] = useState<School[]>([]);
  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load schools from API
  const refreshSchools = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get(`${API_BASE_URL}/api/school/all`);
      setSchools(response.data);

      // If no school is selected and schools exist, select the first one
      if (!selectedSchool && response.data.length > 0) {
        const savedSchoolId = localStorage.getItem("selectedSchoolId");
        const schoolToSelect = savedSchoolId
          ? response.data.find(
              (s: School) => s.id.toString() === savedSchoolId
            ) || response.data[0]
          : response.data[0];
        setSelectedSchool(schoolToSelect);
      }
    } catch (err) {
      setError("Failed to load schools");
      console.error("Error loading schools:", err);
    } finally {
      setLoading(false);
    }
  }, [selectedSchool]);

  // Select a school
  const selectSchool = (school: School) => {
    setSelectedSchool(school);
    localStorage.setItem("selectedSchoolId", school.id.toString());
  };

  // Create a new school
  const createSchool = async (
    schoolData: Omit<School, "id" | "created_at" | "updated_at">
  ): Promise<School> => {
    try {
      // Remove empty email field to prevent sending empty string to backend
      const cleanedData = { ...schoolData };
      if (cleanedData.email === "") {
        delete cleanedData.email;
      }

      const response = await axios.post(
        `${API_BASE_URL}/api/school`,
        cleanedData
      );
      const newSchool = response.data;
      setSchools((prev) => [...prev, newSchool]);
      return newSchool;
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || "Failed to create school";
      throw new Error(errorMessage);
    }
  };

  // Update a school
  const updateSchool = async (
    id: number,
    schoolData: Partial<School>
  ): Promise<School> => {
    try {
      // Remove empty email field to prevent sending empty string to backend
      const cleanedData = { ...schoolData };
      if (cleanedData.email === "") {
        delete cleanedData.email;
      }

      const response = await axios.put(
        `${API_BASE_URL}/api/school/${id}`,
        cleanedData
      );
      const updatedSchool = response.data;

      setSchools((prev) =>
        prev.map((school) => (school.id === id ? updatedSchool : school))
      );

      // Update selected school if it's the one being updated
      if (selectedSchool?.id === id) {
        setSelectedSchool(updatedSchool);
      }

      return updatedSchool;
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || "Failed to update school";
      throw new Error(errorMessage);
    }
  };

  // Delete a school
  const deleteSchool = async (id: number): Promise<void> => {
    try {
      await axios.delete(`${API_BASE_URL}/api/school/${id}`);

      setSchools((prev) => prev.filter((school) => school.id !== id));

      // If the deleted school was selected, select another one
      if (selectedSchool?.id === id) {
        const remainingSchools = schools.filter((school) => school.id !== id);
        setSelectedSchool(
          remainingSchools.length > 0 ? remainingSchools[0] : null
        );
        if (remainingSchools.length > 0) {
          localStorage.setItem(
            "selectedSchoolId",
            remainingSchools[0].id.toString()
          );
        } else {
          localStorage.removeItem("selectedSchoolId");
        }
      }
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || "Failed to delete school";
      throw new Error(errorMessage);
    }
  };

  // Load schools on mount
  useEffect(() => {
    refreshSchools();
  }, [refreshSchools]);

  const value: SchoolContextType = {
    schools,
    selectedSchool,
    loading,
    error,
    selectSchool,
    refreshSchools,
    createSchool,
    updateSchool,
    deleteSchool,
  };

  return (
    <SchoolContext.Provider value={value}>{children}</SchoolContext.Provider>
  );
};

// Custom hook to use the school context
export const useSchool = (): SchoolContextType => {
  const context = useContext(SchoolContext);
  if (context === undefined) {
    throw new Error("useSchool must be used within a SchoolProvider");
  }
  return context;
};

export type { School, SchoolContextType };
