/* Card Component Styles - Birta Global School Design */

.card-container {
  font-family: 'Arial', sans-serif;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  width: 325px;
  height: 500px;
  display: flex;
  flex-direction: column;
}

.card-container:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Card type specific colors - matching Birta Global School template */
.card-container.card-student {
  --theme-color: #FF6B35;
  --theme-color-light: #FF8A65;
  --theme-color-dark: #E65100;
}

.card-container.card-staff {
  --theme-color: #4A90E2;
  --theme-color-light: #64B5F6;
  --theme-color-dark: #1976D2;
}

.card-container.card-non_teaching {
  --theme-color: #4CAF50;
  --theme-color-light: #66BB6A;
  --theme-color-dark: #388E3C;
}

/* Additional color variants for demo cards */
.card-container.card-orange {
  --theme-color: #FF6B35;
  --theme-color-light: #FF8A65;
  --theme-color-dark: #E65100;
}

.card-container.card-green {
  --theme-color: #4CAF50;
  --theme-color-light: #81C784;
  --theme-color-dark: #388E3C;
}

.card-container.card-blue {
  --theme-color: #2E7D96;
  --theme-color-light: #4FC3F7;
  --theme-color-dark: #0277BD;
}

/* Header Section - Birta Global School Style */
.card-header {
  background: var(--theme-color);
  height: 80px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: white;
  overflow: hidden;
  padding: 12px 16px;
}

/* Decorative circles matching the template */
.card-header::after {
  content: '';
  position: absolute;
  bottom: -40px;
  right: -40px;
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  pointer-events: none;
}

.card-header::before {
  content: '';
  position: absolute;
  top: -20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  pointer-events: none;
}

.school-logo {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: white;
  padding: 6px;
  margin-right: 12px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.school-name {
  font-size: 14px;
  font-weight: bold;
  text-align: left;
  line-height: 1.2;
  flex: 1;
}

/* Content Section - Birta Global School Layout */
.card-content {
  padding: 16px;
  display: flex;
  gap: 16px;
  height: calc(100% - 80px - 50px); /* Total height - header - footer */
  position: relative;
  background: white;
  flex: 1;
}

.photo-section {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
  padding-top: 8px;
}

.card-photo {
  width: 110px;
  height: 110px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--theme-color);
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-placeholder {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: white;
  border: 3px solid var(--theme-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
  text-align: center;
}

/* Info Section - Matching template layout */
.info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 4px;
}

.person-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.2;
  text-transform: uppercase;
}

.person-type {
  font-size: 12px;
  color: #666;
  font-weight: normal;
  margin-bottom: 12px;
}

.person-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-row {
  display: flex;
  font-size: 11px;
  line-height: 1.3;
  align-items: center;
}

.detail-label {
  color: #333;
  min-width: 85px;
  font-weight: 500;
}

.detail-value {
  color: #333;
  font-weight: normal;
  flex: 1;
}

/* Footer Section - Birta Global School Style */
.card-footer {
  background: var(--theme-color);
  color: white;
  padding: 8px 16px;
  font-size: 10px;
  text-align: center;
  height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.school-footer-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.school-address {
  font-size: 9px;
  margin-bottom: 1px;
  opacity: 0.9;
}

.school-contact {
  font-size: 9px;
  opacity: 0.9;
}

/* Print Mode Styles */
.card-container.print-mode {
  box-shadow: none;
  border: 1px solid #ddd;
}

.card-container.print-mode:hover {
  transform: none;
  box-shadow: none;
}

/* Compact Layout */
.card-container.layout-compact .card-content {
  padding: 15px;
  gap: 15px;
}

.card-container.layout-compact .card-photo,
.card-container.layout-compact .photo-placeholder {
  width: 80px;
  height: 80px;
}

.card-container.layout-compact .person-name {
  font-size: 18px;
}

.card-container.layout-compact .detail-row {
  font-size: 11px;
}

/* Detailed Layout */
.card-container.layout-detailed .card-content {
  padding: 25px;
  gap: 25px;
}

.card-container.layout-detailed .card-photo,
.card-container.layout-detailed .photo-placeholder {
  width: 120px;
  height: 120px;
}

.card-container.layout-detailed .person-name {
  font-size: 22px;
}

.card-container.layout-detailed .detail-row {
  font-size: 13px;
  margin-bottom: 3px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-container {
    border-radius: 10px;
  }
  
  .card-header {
    height: 60px;
  }
  
  .school-name {
    font-size: 14px;
  }
  
  .card-content {
    padding: 12px;
    gap: 12px;
  }
  
  .person-name {
    font-size: 16px;
  }
  
  .detail-row {
    font-size: 10px;
  }
}

/* Animation for loading states */
.card-loading {
  opacity: 0.7;
  pointer-events: none;
}

.card-loading .card-photo,
.card-loading .photo-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
