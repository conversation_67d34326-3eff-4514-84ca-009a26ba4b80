const { School, Person, CsvBatch } = require('./src/models');
const sequelize = require('./src/config/database');

async function testInsert() {
  try {
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Get the test school
    const school = await School.findOne({ where: { name: 'test' } });
    if (!school) {
      console.log('No test school found!');
      return;
    }
    console.log(`Found school: ${school.name} (ID: ${school.id})`);

    // Create a test CSV batch
    const csvBatch = await CsvBatch.create({
      filename: 'test-insert.csv',
      upload_date: new Date(),
      total_count: 1,
      processed_count: 0,
      status: 'processing'
    });
    console.log(`Created CSV batch: ${csvBatch.id}`);

    // Try to create a test person
    const testPerson = await Person.create({
      person_id: 'TEST001',
      name: 'Test Student',
      type: 'student',
      class: 'Grade 1',
      section: 'A',
      roll_number: '01',
      parents_name: 'Test Parent',
      contact_no: '9841234567',
      department: '',
      csv_batch_id: csvBatch.id,
      school_id: school.id
    });

    console.log(`Successfully created person: ${testPerson.person_id} - ${testPerson.name}`);

    // Update batch status
    await csvBatch.update({
      processed_count: 1,
      status: 'completed'
    });

    console.log('Test insertion completed successfully!');

    // Verify the person was created
    const persons = await Person.findAll();
    console.log(`Total persons in database: ${persons.length}`);

  } catch (error) {
    console.error('Test insertion failed:', error);
  } finally {
    await sequelize.close();
  }
}

testInsert();
