name: CI/CD for School Card App Backend (Docker VPS Deployment)
on:
  push:
    branches:
      - main
    paths:
      - "backend/**"
      - ".github/workflows/server.yml"

env:
  NODE_VERSION: 20
  APP_NAME: school-card-backend
  DOCKER_IMAGE: school-card-backend
  CONTAINER_NAME: school-card-backend-container

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: "./backend/package-lock.json"

      - name: Install dependencies
        working-directory: ./backend
        run: |
          rm -rf node_modules
          npm ci

      - name: Create Docker context
        working-directory: ./backend
        run: |
          # Create production bundle for Docker
          mkdir -p docker-context
          cp -r src docker-context/
          cp -r uploads docker-context/ 2>/dev/null || mkdir -p docker-context/uploads
          cp -r database docker-context/ 2>/dev/null || mkdir -p docker-context/database
          cp -r migrations docker-context/ 2>/dev/null || mkdir -p docker-context/migrations
          cp package*.json docker-context/
          cp *.html docker-context/ 2>/dev/null || echo "No HTML files found, skipping..."
          cp *.js docker-context/ 2>/dev/null || echo "No JS files found, skipping..."
          cp .env docker-context/ 2>/dev/null || echo "No .env file found, skipping..."

      - name: Build Docker image
        working-directory: ./backend
        run: |
          cp Dockerfile docker-context/
          docker build -t ${{ env.DOCKER_IMAGE }}:latest docker-context

      - name: Save Docker image
        run: |
          docker save ${{ env.DOCKER_IMAGE }}:latest | gzip > docker-image.tar.gz

      - name: Deploy to VPS
        if: github.ref == 'refs/heads/main'
        env:
          VPS_HOST: ${{ secrets.VPS_HOST }}
          VPS_USER: ${{ secrets.VPS_USER }}
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        run: |
          # Setup SSH
          mkdir -p ~/.ssh
          echo "$SSH_PRIVATE_KEY" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H -p 72 $VPS_HOST >> ~/.ssh/known_hosts

          # Transfer Docker image
          scp -i ~/.ssh/deploy_key -P 72 docker-image.tar.gz $VPS_USER@$VPS_HOST:/tmp/

          # Deploy on VPS
          ssh -i ~/.ssh/deploy_key -p 72 $VPS_USER@$VPS_HOST << 'EOF'
            set -e

            echo "Loading Docker image..."
            cd /tmp
            docker load < docker-image.tar.gz
            rm docker-image.tar.gz

            echo "Stopping existing container..."
            docker stop ${{ env.CONTAINER_NAME }} 2>/dev/null || true
            docker rm ${{ env.CONTAINER_NAME }} 2>/dev/null || true

            echo "Starting new container..."
            docker run -d \
              --name ${{ env.CONTAINER_NAME }} \
              --restart unless-stopped \
              -p 8908:8080 \
              -e TRUST_PROXY=true \
              -e NODE_ENV=production \
              -e CORS_ORIGIN="https://print.webstudiomatrix.com,https://print-api.webstudiomatrix.com,localhost:3000,localhost:3001" \
              -v /var/lib/school-card-data:/app/uploads \
              -v /var/lib/school-card-db:/app/database \
              ${{ env.DOCKER_IMAGE }}:latest

            echo "Cleaning up old Docker images..."
            docker image prune -f

            echo "Container status:"
            docker ps | grep ${{ env.CONTAINER_NAME }} || echo "Container not found"

            echo "Container logs (last 20 lines):"
            docker logs --tail 20 ${{ env.CONTAINER_NAME }} || echo "No logs available"
          EOF

      - name: Cleanup
        if: always()
        run: |
          rm -f ~/.ssh/deploy_key
          rm -f docker-image.tar.gz
