module.exports = {
  extends: [
    'react-app',
    'react-app/jest'
  ],
  rules: {
    // Disable specific rules that are causing issues
    '@typescript-eslint/no-unused-vars': 'off',
    'no-unused-vars': 'off',
    'react-hooks/exhaustive-deps': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    'no-console': 'off',
    'prefer-const': 'off',
    'no-var': 'off',
    '@typescript-eslint/no-inferrable-types': 'off',
    'react/jsx-no-target-blank': 'off',
    'jsx-a11y/anchor-is-valid': 'off',
    'jsx-a11y/alt-text': 'off',
    'jsx-a11y/img-redundant-alt': 'off',
    'no-undef': 'off',
    'no-redeclare': 'off',
    'no-unreachable': 'off',
    'no-empty': 'off',
    'no-constant-condition': 'off',
    'no-useless-escape': 'off',
    'no-mixed-spaces-and-tabs': 'off',
    'no-irregular-whitespace': 'off',
    'no-extra-semi': 'off',
    'no-unexpected-multiline': 'off',
    'no-unsafe-finally': 'off',
    'no-unsafe-negation': 'off',
    'use-isnan': 'off',
    'valid-typeof': 'off',
    'no-dupe-args': 'off',
    'no-dupe-keys': 'off',
    'no-duplicate-case': 'off',
    'no-empty-character-class': 'off',
    'no-ex-assign': 'off',
    'no-extra-boolean-cast': 'off',
    'no-func-assign': 'off',
    'no-inner-declarations': 'off',
    'no-invalid-regexp': 'off',
    'no-obj-calls': 'off',
    'no-regex-spaces': 'off',
    'no-sparse-arrays': 'off',
    'no-template-curly-in-string': 'off',
    'array-callback-return': 'off',
    'block-scoped-var': 'off',
    'complexity': 'off',
    'consistent-return': 'off',
    'curly': 'off',
    'default-case': 'off',
    'dot-location': 'off',
    'dot-notation': 'off',
    'eqeqeq': 'off',
    'guard-for-in': 'off',
    'max-classes-per-file': 'off',
    'no-alert': 'off',
    'no-caller': 'off',
    'no-case-declarations': 'off',
    'no-div-regex': 'off',
    'no-else-return': 'off',
    'no-empty-function': 'off',
    'no-empty-pattern': 'off',
    'no-eq-null': 'off',
    'no-eval': 'off',
    'no-extend-native': 'off',
    'no-extra-bind': 'off',
    'no-extra-label': 'off',
    'no-fallthrough': 'off',
    'no-floating-decimal': 'off',
    'no-global-assign': 'off',
    'no-implicit-coercion': 'off',
    'no-implicit-globals': 'off',
    'no-implied-eval': 'off',
    'no-invalid-this': 'off',
    'no-iterator': 'off',
    'no-labels': 'off',
    'no-lone-blocks': 'off',
    'no-loop-func': 'off',
    'no-magic-numbers': 'off',
    'no-multi-spaces': 'off',
    'no-multi-str': 'off',
    'no-new': 'off',
    'no-new-func': 'off',
    'no-new-wrappers': 'off',
    'no-octal': 'off',
    'no-octal-escape': 'off',
    'no-param-reassign': 'off',
    'no-proto': 'off',
    'no-return-assign': 'off',
    'no-return-await': 'off',
    'no-script-url': 'off',
    'no-self-assign': 'off',
    'no-self-compare': 'off',
    'no-sequences': 'off',
    'no-throw-literal': 'off',
    'no-unmodified-loop-condition': 'off',
    'no-unused-expressions': 'off',
    'no-unused-labels': 'off',
    'no-useless-call': 'off',
    'no-useless-catch': 'off',
    'no-useless-concat': 'off',
    'no-useless-return': 'off',
    'no-void': 'off',
    'no-warning-comments': 'off',
    'no-with': 'off',
    'prefer-named-capture-group': 'off',
    'prefer-promise-reject-errors': 'off',
    'radix': 'off',
    'require-await': 'off',
    'require-unicode-regexp': 'off',
    'vars-on-top': 'off',
    'wrap-iife': 'off',
    'yoda': 'off'
  }
};
