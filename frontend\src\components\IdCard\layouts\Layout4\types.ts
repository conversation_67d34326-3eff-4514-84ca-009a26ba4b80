// Interface for ID Card field visibility configuration
export interface IDCardFieldVisibility {
  name: boolean;
  class: boolean;
  section: boolean;
  roll_number: boolean;
  parents_name: boolean;
  contact_no: boolean;
  department: boolean;
  designation: boolean;
  address: boolean;
  date_of_birth: boolean;
}

// Types for Layout4 components
export type BgColorType = "green" | "orange" | "blue";

export interface StudentData {
  companyLogo: string;
  image: string;
  schoolName: string;
  location: string;
  contact: string;
  studentName: string;
  class: string;
  rollNo: string;
  parentName?: string;
  contactNo: string;
  address?: string;
  dateOfBirth?: string;
  personType: "student" | "staff" | "non_teaching";
  department?: string;
  fieldVisibilityConfig?: IDCardFieldVisibility;
  validity?: string;
  principalSignature?: string;
}

export interface IdCardProps {
  id?: number;
  schoolName: string;
  motto: string;
  subname: string;
  contact: string;
  validity: string;
  logo: string;
  image: string;
  studentName: string;
  studentclass?: string;
  roll: string;
  idNumber: string;
  address: string;
  phone: string;
  principalSignature: string;
  bgColor?: BgColorType;
  personType: "student" | "staff" | "non_teaching";
  parentName?: string;
  department?: string;
  dateOfBirth?: string;
  fieldVisibilityConfig?: IDCardFieldVisibility;
}
