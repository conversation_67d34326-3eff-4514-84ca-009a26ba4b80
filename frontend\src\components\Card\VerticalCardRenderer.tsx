import React, { useState, useEffect } from "react";
import { CardRendererProps } from "./types";
import ProfileImage from "../common/ProfileImage";

const VerticalCardRenderer: React.FC<CardRendererProps> = ({
  config,
  mode = "preview",
  className = "",
  onPhotoError,
  onPhotoLoad,
}) => {
  const [photoBase64, setPhotoBase64] = useState<string>("");
  const [logoBase64, setLogoBase64] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [photoError, setPhotoError] = useState(false);

  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  };

  useEffect(() => {
    const loadAssets = async () => {
      setLoading(true);
      setPhotoError(false);

      try {
        // Load photo
        if (config.person.photo_path && config.template.showElements.photo) {
          const photoResponse = await fetch(
            `${
              process.env.REACT_APP_API_URL ||
              "https://print-api.webstudiomatrix.com"
            }/api/cards/photos/${config.person.photo_path}?t=${Date.now()}`
          ).catch(() => null);
          if (photoResponse && photoResponse.ok) {
            const photoBlob = await photoResponse.blob();
            const photoBase64 = await blobToBase64(photoBlob);
            setPhotoBase64(photoBase64);
            setPhotoError(false);
          } else {
            console.warn("Failed to load photo for:", config.person.name);
            setPhotoError(true);
          }
        }

        // Load logo
        if (config.school.logo_path && config.template.showElements.logo) {
          const logoResponse = await fetch(
            `${
              process.env.REACT_APP_API_URL ||
              "https://print-api.webstudiomatrix.com"
            }/api/cards/logos/${config.school.logo_path}?t=${Date.now()}`
          ).catch(() => null);
          if (logoResponse && logoResponse.ok) {
            const logoBlob = await logoResponse.blob();
            const logoBase64 = await blobToBase64(logoBlob);
            setLogoBase64(logoBase64);
          }
        }
      } catch (error) {
        console.error("Error loading assets:", error);
      } finally {
        setLoading(false);
      }
    };

    loadAssets();
  }, [
    config.person.photo_path,
    config.school.logo_path,
    config.template.showElements,
    config.person.name,
  ]);

  // Get colors based on person type
  const getCardColors = () => {
    switch (config.person.type) {
      case "student":
        return {
          bgColor: "bg-green-600",
          borderColor: "border-green-600",
        };
      case "staff":
        return {
          bgColor: "bg-blue-600",
          borderColor: "border-blue-600",
        };
      case "non_teaching":
        return {
          bgColor: "bg-orange-600",
          borderColor: "border-orange-600",
        };
      default:
        return {
          bgColor: "bg-green-600",
          borderColor: "border-green-600",
        };
    }
  };

  const { bgColor, borderColor } = getCardColors();

  // Default placeholder SVG for when no photo is available
  const defaultPhotoSvg = `data:image/svg+xml;base64,${btoa(`
    <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="40" cy="40" r="40" fill="#F5F5F5"/>
      <circle cx="40" cy="30" r="12" fill="#D9D9D9"/>
      <path d="M60 60C60 52.2686 53.7314 46 46 46H34C26.2686 46 20 52.2686 20 60V80H60V60Z" fill="#D9D9D9"/>
    </svg>
  `)}`;

  // Default placeholder SVG for when no logo is available
  const defaultLogoSvg = `data:image/svg+xml;base64,${btoa(`
    <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="60" height="60" rx="8" fill="#E5E7EB"/>
      <path d="M20 25h20v10H20z" fill="#9CA3AF"/>
      <path d="M20 35h20v5H20z" fill="#9CA3AF"/>
    </svg>
  `)}`;

  const photoSrc = photoBase64 || defaultPhotoSvg;
  const logoSrc = logoBase64 || defaultLogoSvg;

  if (loading) {
    return (
      <div
        className={`w-[325px] h-[500px] bg-white shadow-lg rounded overflow-hidden font-sans border ${className} flex items-center justify-center`}>
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  return (
    <div
      className={`w-[325px] h-[500px] bg-white shadow-lg rounded overflow-hidden font-sans border ${className}`}>
      {/* Top Colored Shape */}
      <div className={`relative h-[120px] ${bgColor} rounded-b-[80px]`}>
        <div className="absolute text-white left-5 top-6">
          <img
            src={logoSrc}
            alt="school logo"
            className="w-12 h-auto lg:w-20 md:w-16"
            onError={() => console.warn("Logo failed to load")}
          />
        </div>
        <div
          className={`absolute left-1/3 bottom-[-40px] w-[100px] h-[100px] bg-white border-4 ${borderColor} rounded-full overflow-hidden`}>
          {photoBase64 && !photoError ? (
            <img
              src={photoSrc}
              alt="person"
              className="object-cover w-full h-full"
              onError={() => {
                console.warn("Image failed to render for:", config.person.name);
                setPhotoError(true);
                onPhotoError?.();
              }}
              onLoad={onPhotoLoad}
            />
          ) : (
            <ProfileImage
              src=""
              name={config.person.name}
              designation={config.person.type}
              className="w-full h-full"
              shape="circle"
              showInitials={true}
            />
          )}
        </div>
      </div>

      {/* Person Info */}
      <div className="px-4 mt-16 text-center">
        <h2 className="text-lg font-semibold text-gray-700 uppercase">
          {config.person.name}
        </h2>
        <p className="mb-2 text-[15px] font-medium text-gray-700">
          {config.person.type === "student"
            ? "Student"
            : config.person.type === "staff"
            ? "Teaching Staff"
            : "Non-Teaching Staff"}
        </p>

        <div className="mt-4 mb-4 text-start text-[12px] leading-[18px] text-gray-800 font-medium space-y-1">
          {config.person.type === "student" ? (
            <>
              {config.person.class && (
                <div className="flex">
                  <span className="w-20 flex-shrink-0">Class</span>
                  <span className="flex-shrink-0 mr-1">:</span>
                  <span className="flex-1">{config.person.class}</span>
                </div>
              )}
              {config.person.roll_number && (
                <div className="flex">
                  <span className="w-20 flex-shrink-0">Roll No.</span>
                  <span className="flex-shrink-0 mr-1">:</span>
                  <span className="flex-1">{config.person.roll_number}</span>
                </div>
              )}
              {config.person.parents_name && (
                <div className="flex">
                  <span className="w-20 flex-shrink-0">Parents Name</span>
                  <span className="flex-shrink-0 mr-1">:</span>
                  <span className="flex-1">{config.person.parents_name}</span>
                </div>
              )}
            </>
          ) : (
            <>
              <div className="flex">
                <span className="w-20 flex-shrink-0">Employee ID</span>
                <span className="flex-shrink-0 mr-1">:</span>
                <span className="flex-1">{config.person.person_id}</span>
              </div>
              {config.person.class && (
                <div className="flex">
                  <span className="w-20 flex-shrink-0">Department</span>
                  <span className="flex-shrink-0 mr-1">:</span>
                  <span className="flex-1">{config.person.class}</span>
                </div>
              )}
            </>
          )}
          {config.person.contact_no && (
            <div className="flex">
              <span className="w-20 flex-shrink-0">Contact No.</span>
              <span className="flex-shrink-0 mr-1">:</span>
              <span className="flex-1">{config.person.contact_no}</span>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div
        className={`w-full ${bgColor} text-white py-3 px-2 text-center text-[11px] font-medium`}>
        <div className="text-xl font-extrabold uppercase">
          {config.school.name}
        </div>
        <div>{config.school.address}</div>
        <div>Contact No. : {config.school.phone}</div>
      </div>
    </div>
  );
};

export default VerticalCardRenderer;
