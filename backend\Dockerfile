# Use Node.js 20 LTS
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/
COPY database/ ./database/
COPY *.html ./
COPY *.js ./
COPY .env ./


# Create necessary directories
RUN mkdir -p uploads/csv uploads/photos uploads/previews uploads/school-assets uploads/templates

# Expose port
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Start the application
CMD ["node", "src/app.js"]
