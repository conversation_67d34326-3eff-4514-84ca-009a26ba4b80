import { StudentData } from "./types";
import DecorativePatterns from "./DecorativePatterns";
import ProfileImage from "../../../common/ProfileImage";
import { hasValidValue } from "../../utils/fieldValidation";

interface StudentInfoProps {
  image: string;
  data: StudentData;
}

const StudentInfo = ({ image, data }: StudentInfoProps) => {
  const isStudent = data.personType === "student";
  const fieldConfig = data.fieldVisibilityConfig;

  return (
    <div className="bg-white px-3 pt-1 pb-0 relative flex-grow overflow-hidden flex flex-col">
      <DecorativePatterns />

      <div className="flex-grow flex flex-col justify-center">
        <div className="mx-auto w-20 h-24 bg-gray-200 border-2 border-[#2d3091] z-40 rounded-lg mt-2 overflow-hidden">
          <ProfileImage
            src={image}
            name={data.studentName}
            designation={data.personType}
            className="w-full h-full"
            shape="rounded"
            showInitials={true}
          />
        </div>

        <div className="text-left mt-3 text-[9px] leading-snug grid grid-cols-[auto_1fr] gap-x-1 gap-y-[1px]">
          {/* Name - Configurable */}
          {fieldConfig?.name !== false && hasValidValue(data.studentName) && (
            <>
              <span className="font-bold">Name :</span>
              <span className="font-semibold">{data.studentName}</span>
            </>
          )}

          {isStudent ? (
            <>
              {/* Class - Configurable */}
              {fieldConfig?.class !== false &&
                hasValidValue(data.studentclass) && (
                  <>
                    <span className="font-bold">Class :</span>
                    <span className="font-semibold">{data.studentclass}</span>
                  </>
                )}

              {/* Roll Number - Configurable */}
              {fieldConfig?.roll_number !== false &&
                hasValidValue(data.roll) && (
                  <>
                    <span className="font-bold">Roll :</span>
                    <span className="font-semibold">{data.roll}</span>
                  </>
                )}

              {/* Parent Name - Configurable */}
              {fieldConfig?.parents_name !== false &&
                hasValidValue(data.parentName) && (
                  <>
                    <span className="font-bold">Parent Name :</span>
                    <span className="font-semibold">{data.parentName}</span>
                  </>
                )}
            </>
          ) : (
            <>
              {/* Roll Number as ID - Configurable */}
              {fieldConfig?.roll_number !== false &&
                hasValidValue(data.roll) && (
                  <>
                    <span className="font-bold">ID:</span>
                    <span className="font-semibold">{data.roll}</span>
                  </>
                )}

              {/* Department - Configurable */}
              {fieldConfig?.department !== false &&
                hasValidValue(data.department) && (
                  <>
                    <span className="font-bold">Department :</span>
                    <span className="font-semibold">
                      {data.department || "General"}
                    </span>
                  </>
                )}
            </>
          )}

          {/* Address - Configurable */}
          {fieldConfig?.address !== false && hasValidValue(data.address) && (
            <>
              <span className="font-bold">Address :</span>
              <span className="font-semibold">{data.address}</span>
            </>
          )}

          {/* Contact Number - Configurable */}
          {fieldConfig?.contact_no !== false && hasValidValue(data.phone) && (
            <>
              <span className="font-bold">Phone :</span>
              <span className="font-semibold">{data.phone}</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default StudentInfo;
