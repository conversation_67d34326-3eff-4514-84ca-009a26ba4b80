const winston = require("winston");
const DailyRotateFile = require("winston-daily-rotate-file");
const path = require("path");

/**
 * Production-grade logging system with multiple transports and log levels
 */
class Logger {
  constructor() {
    this.loggers = new Map();
    this.setupDefaultLogger();
  }

  /**
   * Setup default logger configuration
   */
  setupDefaultLogger() {
    const logDir = path.join(__dirname, "../../logs");
    
    // Custom format for logs
    const logFormat = winston.format.combine(
      winston.format.timestamp({
        format: "YYYY-MM-DD HH:mm:ss.SSS"
      }),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
        const metaStr = Object.keys(meta).length > 0 ? JSON.stringify(meta) : "";
        return `${timestamp} [${level.toUpperCase()}] ${service || "APP"}: ${message} ${metaStr}`;
      })
    );

    // Console format for development
    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({
        format: "HH:mm:ss"
      }),
      winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
        const metaStr = Object.keys(meta).length > 0 ? JSON.stringify(meta, null, 2) : "";
        return `${timestamp} [${level}] ${service || "APP"}: ${message} ${metaStr}`;
      })
    );

    // Create transports
    const transports = [
      // Console transport
      new winston.transports.Console({
        level: process.env.NODE_ENV === "production" ? "info" : "debug",
        format: consoleFormat,
        handleExceptions: true,
        handleRejections: true
      })
    ];

    // File transports for production
    if (process.env.NODE_ENV === "production") {
      // Error log file
      transports.push(
        new DailyRotateFile({
          filename: path.join(logDir, "error-%DATE%.log"),
          datePattern: "YYYY-MM-DD",
          level: "error",
          format: logFormat,
          maxSize: "20m",
          maxFiles: "14d",
          handleExceptions: true,
          handleRejections: true
        })
      );

      // Combined log file
      transports.push(
        new DailyRotateFile({
          filename: path.join(logDir, "combined-%DATE%.log"),
          datePattern: "YYYY-MM-DD",
          format: logFormat,
          maxSize: "20m",
          maxFiles: "7d"
        })
      );

      // Access log file
      transports.push(
        new DailyRotateFile({
          filename: path.join(logDir, "access-%DATE%.log"),
          datePattern: "YYYY-MM-DD",
          level: "info",
          format: logFormat,
          maxSize: "20m",
          maxFiles: "30d"
        })
      );
    }

    // Create default logger
    const defaultLogger = winston.createLogger({
      level: process.env.LOG_LEVEL || (process.env.NODE_ENV === "production" ? "info" : "debug"),
      format: logFormat,
      transports,
      exitOnError: false
    });

    this.loggers.set("default", defaultLogger);
  }

  /**
   * Create or get a logger for a specific service
   */
  createLogger(serviceName) {
    if (this.loggers.has(serviceName)) {
      return this.loggers.get(serviceName);
    }

    const defaultLogger = this.loggers.get("default");
    const serviceLogger = defaultLogger.child({ service: serviceName });
    
    this.loggers.set(serviceName, serviceLogger);
    return serviceLogger;
  }

  /**
   * Get logger instance
   */
  getLogger(serviceName = "default") {
    return this.loggers.get(serviceName) || this.loggers.get("default");
  }

  /**
   * Log performance metrics
   */
  logPerformance(serviceName, operation, duration, metadata = {}) {
    const logger = this.getLogger(serviceName);
    const level = duration > 5000 ? "warn" : duration > 1000 ? "info" : "debug";
    
    logger.log(level, `Performance: ${operation}`, {
      operation,
      duration: `${duration}ms`,
      ...metadata
    });
  }

  /**
   * Log API request/response
   */
  logApiCall(serviceName, method, path, statusCode, duration, metadata = {}) {
    const logger = this.getLogger(serviceName);
    const level = statusCode >= 400 ? "error" : statusCode >= 300 ? "warn" : "info";
    
    logger.log(level, `API Call: ${method} ${path}`, {
      method,
      path,
      statusCode,
      duration: `${duration}ms`,
      ...metadata
    });
  }

  /**
   * Log database operations
   */
  logDatabase(operation, table, duration, metadata = {}) {
    const logger = this.getLogger("database");
    const level = duration > 2000 ? "warn" : "debug";
    
    logger.log(level, `DB Operation: ${operation} on ${table}`, {
      operation,
      table,
      duration: `${duration}ms`,
      ...metadata
    });
  }

  /**
   * Log security events
   */
  logSecurity(event, severity = "info", metadata = {}) {
    const logger = this.getLogger("security");
    
    logger.log(severity, `Security Event: ${event}`, {
      event,
      timestamp: new Date().toISOString(),
      ...metadata
    });
  }

  /**
   * Log business events
   */
  logBusiness(event, metadata = {}) {
    const logger = this.getLogger("business");
    
    logger.info(`Business Event: ${event}`, {
      event,
      timestamp: new Date().toISOString(),
      ...metadata
    });
  }

  /**
   * Log errors with context
   */
  logError(error, context = {}) {
    const logger = this.getLogger("error");
    
    logger.error("Application Error", {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code,
      ...context
    });
  }

  /**
   * Get logging statistics
   */
  getStats() {
    const stats = {
      loggers: Array.from(this.loggers.keys()),
      transports: {},
      levels: {}
    };

    // Get transport information
    const defaultLogger = this.loggers.get("default");
    if (defaultLogger) {
      stats.transports = defaultLogger.transports.map(transport => ({
        name: transport.name,
        level: transport.level,
        filename: transport.filename
      }));
    }

    return stats;
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    const promises = [];
    
    for (const [name, logger] of this.loggers) {
      promises.push(
        new Promise((resolve) => {
          logger.on("finish", resolve);
          logger.end();
        })
      );
    }

    await Promise.all(promises);
  }
}

// Create singleton instance
const loggerInstance = new Logger();

/**
 * Express middleware for request logging
 */
const requestLogger = (serviceName = "api") => {
  return (req, res, next) => {
    const startTime = Date.now();
    const logger = loggerInstance.getLogger(serviceName);

    // Log request
    logger.info(`Request started: ${req.method} ${req.path}`, {
      method: req.method,
      path: req.path,
      ip: req.ip,
      userAgent: req.get("User-Agent"),
      requestId: req.requestId
    });

    // Override res.end to log response
    const originalEnd = res.end;
    res.end = function(...args) {
      const duration = Date.now() - startTime;
      
      loggerInstance.logApiCall(
        serviceName,
        req.method,
        req.path,
        res.statusCode,
        duration,
        {
          ip: req.ip,
          requestId: req.requestId,
          contentLength: res.get("Content-Length")
        }
      );

      originalEnd.apply(this, args);
    };

    next();
  };
};

/**
 * Error logging middleware
 */
const errorLogger = (serviceName = "error") => {
  return (err, req, res, next) => {
    loggerInstance.logError(err, {
      method: req.method,
      path: req.path,
      ip: req.ip,
      requestId: req.requestId,
      body: req.body,
      query: req.query,
      params: req.params
    });

    next(err);
  };
};

module.exports = {
  createLogger: (serviceName) => loggerInstance.createLogger(serviceName),
  getLogger: (serviceName) => loggerInstance.getLogger(serviceName),
  logPerformance: (serviceName, operation, duration, metadata) => 
    loggerInstance.logPerformance(serviceName, operation, duration, metadata),
  logApiCall: (serviceName, method, path, statusCode, duration, metadata) =>
    loggerInstance.logApiCall(serviceName, method, path, statusCode, duration, metadata),
  logDatabase: (operation, table, duration, metadata) =>
    loggerInstance.logDatabase(operation, table, duration, metadata),
  logSecurity: (event, severity, metadata) =>
    loggerInstance.logSecurity(event, severity, metadata),
  logBusiness: (event, metadata) =>
    loggerInstance.logBusiness(event, metadata),
  logError: (error, context) =>
    loggerInstance.logError(error, context),
  requestLogger,
  errorLogger,
  getStats: () => loggerInstance.getStats(),
  shutdown: () => loggerInstance.shutdown()
};
