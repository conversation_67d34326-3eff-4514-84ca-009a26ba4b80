const sequelize = require("./src/config/database");
const { Person, School } = require("./src/models");

async function testAddress() {
  try {
    await sequelize.authenticate();
    console.log("Database connection established successfully.");

    // Find the first person
    const person = await Person.findOne({
      include: [
        {
          model: School,
          attributes: ["id", "name", "address", "phone", "email"],
        },
      ],
    });

    if (person) {
      console.log("Found person:", person.name);
      console.log("Current address:", person.address);
      console.log("School address:", person.School?.address);

      // Update the person with a test address
      await person.update({
        address: "Test Individual Address, Kathmandu, Nepal",
      });

      console.log("Updated person address to:", person.address);

      // Fetch the person again to verify
      const updatedPerson = await Person.findByPk(person.id, {
        include: [
          {
            model: School,
            attributes: ["id", "name", "address", "phone", "email"],
          },
        ],
      });

      console.log("Verified updated address:", updatedPerson.address);
    } else {
      console.log("No persons found in database");
    }

    await sequelize.close();
    console.log("Test completed successfully.");
  } catch (error) {
    console.error("Test failed:", error);
    process.exit(1);
  }
}

testAddress();
