import { useCallback, useEffect, useState } from 'react';
import { 
  personService, 
  schoolService, 
  cardService, 
  ServiceManager,
  Person,
  School,
  CardConfig
} from '../services';

/**
 * Hook for using person service with optimized patterns
 */
export const usePersonService = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getPersons = useCallback(async (params = {}) => {
    try {
      setLoading(true);
      setError(null);
      const response = await personService.getPersons(params);
      return response.data;
    } catch (err: any) {
      setError(err.message || 'Failed to fetch persons');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const searchPersons = useCallback((query: string, params = {}, onResults: (results: any) => void) => {
    personService.searchPersons(query, params, (results) => {
      onResults(results.data);
    });
  }, []);

  const createPerson = useCallback(async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await personService.createPerson(data);
      return response.data;
    } catch (err: any) {
      setError(err.message || 'Failed to create person');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updatePerson = useCallback(async (id: number, data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await personService.updatePerson(id, data);
      return response.data;
    } catch (err: any) {
      setError(err.message || 'Failed to update person');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const deletePerson = useCallback(async (id: number) => {
    try {
      setLoading(true);
      setError(null);
      await personService.deletePerson(id);
    } catch (err: any) {
      setError(err.message || 'Failed to delete person');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const uploadCsv = useCallback(async (file: File, schoolId?: number, onProgress?: (progress: number) => void) => {
    try {
      setLoading(true);
      setError(null);
      const response = await personService.uploadCsv(file, schoolId, onProgress);
      return response.data;
    } catch (err: any) {
      setError(err.message || 'Failed to upload CSV');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    getPersons,
    searchPersons,
    createPerson,
    updatePerson,
    deletePerson,
    uploadCsv,
    clearError: () => setError(null)
  };
};

/**
 * Hook for using school service
 */
export const useSchoolService = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getSchools = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await schoolService.getSchools();
      return response.data;
    } catch (err: any) {
      setError(err.message || 'Failed to fetch schools');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const createSchool = useCallback(async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await schoolService.createSchool(data);
      return response.data;
    } catch (err: any) {
      setError(err.message || 'Failed to create school');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateSchool = useCallback(async (id: number, data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await schoolService.updateSchool(id, data);
      return response.data;
    } catch (err: any) {
      setError(err.message || 'Failed to update school');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const uploadLogo = useCallback(async (schoolId: number, file: File, onProgress?: (progress: number) => void) => {
    try {
      setLoading(true);
      setError(null);
      const response = await schoolService.uploadLogo(schoolId, file, onProgress);
      return response.data;
    } catch (err: any) {
      setError(err.message || 'Failed to upload logo');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    getSchools,
    createSchool,
    updateSchool,
    uploadLogo,
    clearError: () => setError(null)
  };
};

/**
 * Hook for using card service
 */
export const useCardService = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getCardConfig = useCallback(async (personId: number) => {
    try {
      setLoading(true);
      setError(null);
      const response = await cardService.getCardConfig(personId);
      return response.data;
    } catch (err: any) {
      setError(err.message || 'Failed to fetch card config');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const generateSingleCardPdf = useCallback(async (personId: number, options = {}) => {
    try {
      setLoading(true);
      setError(null);
      const blob = await cardService.generateSingleCardPdf(personId, options);
      return blob;
    } catch (err: any) {
      setError(err.message || 'Failed to generate PDF');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const generateBatchCardsPdf = useCallback(async (personIds: number[], options = {}) => {
    try {
      setLoading(true);
      setError(null);
      const blob = await cardService.generateBatchCardsPdf(personIds, options);
      return blob;
    } catch (err: any) {
      setError(err.message || 'Failed to generate batch PDF');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const getCardStats = useCallback(async (schoolId?: number) => {
    try {
      setLoading(true);
      setError(null);
      const response = await cardService.getCardStats(schoolId);
      return response.data;
    } catch (err: any) {
      setError(err.message || 'Failed to fetch card stats');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    getCardConfig,
    generateSingleCardPdf,
    generateBatchCardsPdf,
    getCardStats,
    clearError: () => setError(null)
  };
};

/**
 * Hook for managing all services
 */
export const useServices = () => {
  const [initialized, setInitialized] = useState(false);
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    const initServices = async () => {
      try {
        await ServiceManager.initialize();
        setInitialized(true);
      } catch (error) {
        console.error('Failed to initialize services:', error);
      }
    };

    initServices();

    // Cleanup on unmount
    return () => {
      ServiceManager.destroy();
    };
  }, []);

  const clearAllCaches = useCallback(() => {
    ServiceManager.clearAllCaches();
  }, []);

  const getStats = useCallback(() => {
    const serviceStats = ServiceManager.getStats();
    setStats(serviceStats);
    return serviceStats;
  }, []);

  return {
    initialized,
    stats,
    clearAllCaches,
    getStats
  };
};

/**
 * Hook for optimized data fetching with caching
 */
export const useOptimizedData = <T>(
  fetchFn: () => Promise<T>,
  dependencies: any[] = [],
  options: {
    enabled?: boolean;
    refetchOnWindowFocus?: boolean;
    staleTime?: number;
  } = {}
) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFetch, setLastFetch] = useState<number>(0);

  const { enabled = true, refetchOnWindowFocus = false, staleTime = 300000 } = options;

  const fetchData = useCallback(async (force = false) => {
    if (!enabled) return;

    const now = Date.now();
    const isStale = now - lastFetch > staleTime;

    if (!force && data && !isStale) {
      return data;
    }

    try {
      setLoading(true);
      setError(null);
      const result = await fetchFn();
      setData(result);
      setLastFetch(now);
      return result;
    } catch (err: any) {
      setError(err.message || 'Failed to fetch data');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchFn, enabled, data, lastFetch, staleTime]);

  useEffect(() => {
    fetchData();
  }, dependencies);

  useEffect(() => {
    if (refetchOnWindowFocus) {
      const handleFocus = () => fetchData();
      window.addEventListener('focus', handleFocus);
      return () => window.removeEventListener('focus', handleFocus);
    }
  }, [refetchOnWindowFocus, fetchData]);

  return {
    data,
    loading,
    error,
    refetch: () => fetchData(true),
    clearError: () => setError(null)
  };
};
