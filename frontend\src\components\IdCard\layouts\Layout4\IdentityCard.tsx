import CardHeader from "./CardHeader";
import StudentInfo from "./StudentInfo";
import CardFooter from "./CardFooter";
import { StudentData } from "./types";

interface IdentityCardProps {
  data: StudentData;
}

const IdentityCard = ({ data }: IdentityCardProps) => {
  return (
    <div className="w-[204px] h-[324px] bg-white shadow-lg overflow-hidden font-sans text-black relative">
      {/* Background with blue curved shape and school info */}
      <CardHeader
        logo={data.companyLogo}
        schoolName={data.schoolName}
        location={data.location}
        contact={data.contact}
      />

      {/* Main content area - Student info and footer */}
      <div className="relative z-10 h-full flex flex-col">
        <StudentInfo data={data} />
        <CardFooter data={data} />
      </div>
    </div>
  );
};

export default IdentityCard;
