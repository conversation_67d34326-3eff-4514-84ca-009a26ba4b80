import ProfileImage from "./common/ProfileImage";

interface IdCardProps {
  schoolName?: string;
  motto?: string;
  subname?: string;
  contact?: string;
  validity?: string;
  logo?: string;
  image?: string;
  studentName?: string;
  designation?: string;
  studentclass?: string;
  roll?: string;
  address?: string;
  phone?: string;
  principalSignature?: string;
}

export default function IdCard({
  schoolName = "MAI VALLEY",
  motto = '"Strive for Excellence"',
  subname = "BOARDING SCHOOL",
  contact = "Birtamode-3, Jhapa | 9801112233",
  validity = "2083/01/15",
  logo = "",
  image = "",
  studentName = "Sample Student",
  designation = "Student",
  studentclass = "10",
  roll = "01",
  address = "Birtamode, Jhapa",
  phone = "9801112233",
  principalSignature = "",
}: IdCardProps = {}) {
  return (
    <div className="h-[324px] w-[204px]  bg-white rounded-md overflow-hidden shadow-lg flex flex-col">
      {/* Header Section */}
      <div className="bg-[#2d3091] px-2 py-2 text-white relative flex-shrink-0">
        <div className="flex items-center gap-2">
          {/* School Logo */}
          <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center flex-shrink-0">
            <div className="relative w-full h-full">
              <img
                src={logo}
                alt="School Logo"
                className="absolute inset-0 object-cover rounded-full w-full h-full overflow-hidden"
              />
            </div>
          </div>

          {/* School Info */}
          <div className="flex-1">
            <div className="text-[8px] italic mb-0.5 text-center">{motto}</div>
            <div className="text-xl font-black text-center">{schoolName}</div>
            <div className="text-sm text-center whitespace-normal font-semibold -mt-1">
              {subname}
            </div>
            <div className="text-[#eee231] text-[9px] text-center mt-0.5">
              {contact}
            </div>
          </div>
        </div>
      </div>

      {/* Green Stripe */}
      <div className="h-1 bg-[#00a54f] flex-shrink-0"></div>

      {/* ID Card Label */}
      <div className="bg-[#2d3091] w-fit mx-auto px-3 rounded-b-md text-white text-center py-1 flex-shrink-0">
        <div className="text-[10px] font-bold">ID CARD</div>
      </div>

      {/* Main Content Area */}
      <div className="bg-white px-3 py-1 relative flex-grow overflow-hidden">
        {/* Left Decorative Patterns */}
        <div className="absolute">
          <div className="absolute rotate-90 left-[3px] top-1 w-8 h-8">
            <svg
              width="32"
              height="32"
              viewBox="0 0 100 100"
              xmlns="http://www.w3.org/2000/svg"
            >
              <polygon
                points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
                fill="transparent"
                stroke="green"
                strokeWidth="2"
              />
            </svg>
          </div>
          {/*  */}
          <div className="absolute -rotate-90 -left-2  z-10 top-8 w-14 h-14">
            <svg
              width="70"
              height="70"
              viewBox="0 0 100 100"
              xmlns="http://www.w3.org/2000/svg"
            >
              <polygon
                points="50,10 90,35 90,85 10,85 10,35"
                fill="transparent"
                stroke="#00a54f"
                strokeWidth="1"
              />
            </svg>
          </div>
          {/* red */}
          <div className="absolute rotate-90 -left-10 top-3 w-20 h-20">
            <svg
              width="100"
              height="100"
              viewBox="0 0 100 100"
              xmlns="http://www.w3.org/2000/svg"
            >
              <polygon
                points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
                fill="transparent"
                stroke="red"
                strokeWidth="1"
              />
            </svg>
          </div>
          {/* yellow */}
          <div className="absolute rotate-90 -left-7 top-[72px] w-8 h-8">
            <svg
              width="32"
              height="32"
              viewBox="0 0 100 100"
              xmlns="http://www.w3.org/2000/svg"
            >
              <polygon
                points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
                fill="transparent"
                stroke="#f3ee50"
                strokeWidth="1"
              />
            </svg>
          </div>
        </div>
        {/* Right Decorative Patterns */}
        <div className="absolute right-0">
          {/* green */}
          <div className="absolute -rotate-90 right-[16px] top-1 w-8 h-8">
            <svg
              width="32"
              height="32"
              viewBox="0 0 100 100"
              xmlns="http://www.w3.org/2000/svg"
            >
              <polygon
                points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
                fill="transparent"
                stroke="green"
                strokeWidth="2"
              />
            </svg>
          </div>

          {/* green filled polygon */}
          <div className="absolute rotate-90 right-1 z-10 top-5 w-14 h-14">
            <svg
              width="70"
              height="70"
              viewBox="0 0 100 100"
              xmlns="http://www.w3.org/2000/svg"
            >
              <polygon
                points="50,10 90,35 90,85 10,85 10,35"
                fill="transparent"
                stroke="#00a54f"
                strokeWidth="1"
              />
            </svg>
          </div>

          {/* red */}
          <div className="absolute -rotate-90 -right-7 top-8 w-20 h-20">
            <svg
              width="100"
              height="100"
              viewBox="0 0 100 100"
              xmlns="http://www.w3.org/2000/svg"
            >
              <polygon
                points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
                fill="transparent"
                stroke="red"
                strokeWidth="1"
              />
            </svg>
          </div>

          {/* yellow */}
          <div className="absolute -rotate-90 -right-7 top-[72px] w-8 h-8">
            <svg
              width="32"
              height="32"
              viewBox="0 0 100 100"
              xmlns="http://www.w3.org/2000/svg"
            >
              <polygon
                points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
                fill="transparent"
                stroke="#f3ee50"
                strokeWidth="1"
              />
            </svg>
          </div>
        </div>

        {/* Photo Placeholder */}
        <div className="mx-auto w-20 h-24 border-2 border-[#2d3091] z-40 rounded-lg mt-2 overflow-hidden">
          <ProfileImage
            src={image}
            name={studentName}
            designation={designation}
            className="w-full h-full"
            shape="square"
            showInitials={true}
          />
        </div>

        {/* Student Information */}
        <div className="text-left mt-3 text-[10px] leading-snug grid grid-cols-[auto_1fr] gap-x-1 gap-y-[1px] z-40 relative">
          <span className="font-bold">Name:</span>
          <span className="font-normal">{studentName}</span>

          {designation?.toLowerCase().includes("student") ? (
            <>
              <span className="font-bold">Class:</span>
              <span className="font-normal">{studentclass}</span>

              <span className="font-bold">Roll:</span>
              <span className="font-normal">{roll}</span>
            </>
          ) : (
            <>
              <span className="font-bold">ID:</span>
              <span className="font-normal">{roll}</span>
            </>
          )}

          <span className="font-bold">Address:</span>
          <span className="font-normal">{address}</span>

          <span className="font-bold">Phone:</span>
          <span className="font-normal">{phone}</span>
        </div>
      </div>

      {/* Footer Section */}
      <div className="bg-[#2d3091] relative z-10 text-white flex py-1 flex-shrink-0">
        <div className="flex-1 px-2">
          <div className="text-[9px] font-medium whitespace-nowrap">
            Validity Upto: {validity}
          </div>
        </div>

        <div
          className="w-20 absolute right-0 -top-2 h-[140%] bg-[#00a54f] flex items-center justify-center flex-col"
          style={{ clipPath: "polygon(0 0, 100% 0, 100% 100%, 25% 100%)" }}
        >
          <span className="text-[8px] absolute top-1"></span>
          <div className="-mt-1 border-b border-dotted absolute border-[#2d3091] w-10 mx-auto"></div>
          <div className="text-[8px] text-[#2d3091] absolute font-medium text-center pt-2">
            Principal
          </div>
        </div>
      </div>
    </div>
  );
}
