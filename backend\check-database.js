const { School, Person, CsvBatch } = require('./src/models');
const sequelize = require('./src/config/database');

async function checkDatabase() {
  try {
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Check schools
    const schools = await School.findAll();
    console.log(`\nSchools in database: ${schools.length}`);
    schools.forEach(school => {
      console.log(`  - ID: ${school.id}, Name: ${school.name}`);
    });

    // Check persons
    const persons = await Person.findAll();
    console.log(`\nPersons in database: ${persons.length}`);
    if (persons.length > 0) {
      console.log('First few persons:');
      persons.slice(0, 5).forEach(person => {
        console.log(`  - ${person.person_id}: ${person.name} (${person.type}) - School ID: ${person.school_id}`);
      });
    }

    // Check CSV batches
    const batches = await CsvBatch.findAll();
    console.log(`\nCSV batches in database: ${batches.length}`);
    batches.forEach(batch => {
      console.log(`  - ID: ${batch.id}, File: ${batch.filename}, Status: ${batch.status}, Processed: ${batch.processed_count}/${batch.total_count}`);
      if (batch.error_log) {
        console.log(`    Errors: ${batch.error_log}`);
      }
    });

    // Check database schema
    console.log('\n=== Database Schema Check ===');
    const [personColumns] = await sequelize.query("PRAGMA table_info(persons)");
    console.log('Persons table columns:');
    personColumns.forEach(col => {
      console.log(`  - ${col.name}: ${col.type} (nullable: ${col.notnull === 0})`);
    });

  } catch (error) {
    console.error('Database check failed:', error);
  } finally {
    await sequelize.close();
  }
}

checkDatabase();
