import BackgroundShape from "./BackgroundShape";

interface CardHeaderProps {
  logo: string;
  schoolName: string;
  location: string;
  contact: string;
}

const CardHeader = ({
  logo,
  schoolName,
  location,
  contact,
}: CardHeaderProps) => {
  return (
    <div className="absolute inset-0 w-full h-full">
      {/* Blue curved background */}
      <BackgroundShape className="z-0" />

      {/* School Logo in top-left blue area */}
      <div className="absolute top-0.5 left-0.5 z-10">
        <div className="w-9 h-9  rounded-full flex items-center justify-center overflow-hidden ">
          <img src={logo} alt="School Logo" className="w-9 h-9 object-cover " />
        </div>
      </div>

      {/* School info in top-right white area */}
      <div className="absolute top-2 right-2 z-10 text-center">
        {/* School Name */}
        <h1 className="text-white font-light text-[12px] ml-4 leading-tight font-['Times_New_Roman']">
          {schoolName}
        </h1>

        {/* Location */}
        <p className="text-white text-[7px] font-['Times_New_Roman'] ">
          {location}
        </p>

        {/* Contact */}
        <p className="text-white text-[7px] font-['Times_New_Roman']">
          Ph: {contact}
        </p>
      </div>

      {/* Large "B" in blue curved area - positioned lower */}
      <div className="absolute left-2 top-[25%]  ">
        <div className="w-8 h-8 border-[1px] p-0  border-white rounded-full flex items-center justify-center">
          <span className="text-white font-normal text-xl">B</span>
        </div>
      </div>
    </div>
  );
};

export default CardHeader;
