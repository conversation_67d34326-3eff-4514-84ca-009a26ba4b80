import { StudentData } from "./types";

interface CardFooterProps {
  data: StudentData;
}

const CardFooter = ({ data }: CardFooterProps) => {
  return (
    <div className="relative z-10 px-3 pb-3 -mt-2">
      {/* Validity and Principal Signature */}
      <div className="flex justify-between items-center">
        {/* Principal Signature */}
        <div className="ml-auto text-right">
          {data.principalSignature && (
            <div className="w-16 h-6 mt-1">
              <img
                src={data.principalSignature}
                alt="Principal Signature"
                className="w-full h-full object-contain"
              />
            </div>
          )}
          {/* Signature line */}
          <div className="w-16 h-px bg-black mb-1"></div>
          <div className="text-black font-medium mr-3 text-[9px]">
            Principal
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardFooter;
