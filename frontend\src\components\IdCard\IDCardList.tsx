import { useState, useEffect, useCallback } from "react";
import axios from "axios";
import { PersonData, SchoolData } from "../Card/types";
import {
  transformPersonsToIdCards,
  transformPersonsWithColorCoding,
  IdCardProps,
  getPhotoUrl,
  getLogoUrl,
  getPrincipalSignatureUrl,
} from "../../utils/dataMapping";
import LayoutSelector, { LayoutType, BgColorType } from "./LayoutSelector";
import Layout2 from "./layouts/Layout2/Layout2";
import Layout3 from "./layouts/Layout3/Layout3";
import Layout1 from "./layouts/Layout1/Layout1";
import Layout4 from "./layouts/Layout4/Layout4";

import LoadingCard from "./LoadingCard";
import { useNotification } from "../../contexts/NotificationContext";
import { useSchool, School } from "../../contexts/SchoolContext";
import { imageCache } from "../../utils/imageCache";

// Interface for ID Card field visibility configuration
interface IDCardFieldVisibility {
  name: boolean;
  class: boolean;
  section: boolean;
  roll_number: boolean;
  parents_name: boolean;
  contact_no: boolean;
  department: boolean;
  designation: boolean;
  address: boolean;
  date_of_birth: boolean;
}

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

// Helper function to convert School to SchoolData
const convertSchoolToSchoolData = (school: School): SchoolData => ({
  id: school.id,
  name: school.name,
  address: school.address,
  phone: school.phone,
  email: school.email || "", // Provide default empty string for required email field
  logo_path: school.logo_path,
  stamp_path: school.stamp_path,
  signature_path: school.signature_path,
  validity_date: school.validity_date,
  created_at: school.created_at,
  updated_at: school.updated_at,
});

interface IdCardListProps {
  fieldVisibilityConfig: IDCardFieldVisibility;
}

export default function IdCardList({ fieldVisibilityConfig }: IdCardListProps) {
  const [selectedLayout, setSelectedLayout] = useState<LayoutType>("layout1");
  const [selectedBgColor, setSelectedBgColor] = useState<BgColorType>("green");
  const [printMode, setPrintMode] = useState<
    "single" | "multiple" | "all_single"
  >("single");
  const [selectedCard, setSelectedCard] = useState<number | null>(null);
  const [selectedCards, setSelectedCards] = useState<Set<number>>(new Set());

  // Backend data state
  const [persons, setPersons] = useState<PersonData[]>([]);
  const [idCardData, setIdCardData] = useState<
    (IdCardProps & { bgColor?: BgColorType })[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [imagesLoading, setImagesLoading] = useState(false);
  const [selectedType, setSelectedType] = useState<
    "all" | "student" | "staff" | "non_teaching"
  >("all");

  const { showError } = useNotification();
  const { selectedSchool } = useSchool();

  // Preload images for better performance
  const preloadImages = useCallback(
    async (personsData: PersonData[], schoolData: SchoolData) => {
      setImagesLoading(true);

      try {
        const imageUrls: string[] = [];

        // Add school logo and signature
        if (schoolData.logo_path) {
          imageUrls.push(getLogoUrl(schoolData.logo_path));
        }
        if (schoolData.signature_path) {
          imageUrls.push(getPrincipalSignatureUrl(schoolData.signature_path));
        }

        // Add person photos
        personsData.forEach((person) => {
          if (person.photo_path) {
            imageUrls.push(getPhotoUrl(person.photo_path));
          }
        });

        // Filter out empty URLs
        const validUrls = imageUrls.filter((url) => url && url.trim() !== "");

        if (validUrls.length > 0) {
          console.log(`Preloading ${validUrls.length} images...`);
          const { loaded, failed } = await imageCache.preloadImages(validUrls);
          console.log(
            `Preloaded ${loaded.length} images, ${failed.length} failed`
          );

          if (failed.length > 0) {
            console.warn("Failed to preload images:", failed);
          }
        }
      } finally {
        setImagesLoading(false);
      }
    },
    []
  );

  const updateIdCardData = useCallback(
    async (personsData: PersonData[], schoolData: SchoolData, type: string) => {
      let filteredPersons = personsData;

      if (type !== "all") {
        filteredPersons = personsData.filter((person) => person.type === type);
      }

      // Preload images before rendering cards
      await preloadImages(filteredPersons, schoolData);

      // Transform data based on layout
      if (selectedLayout === "layout2" || selectedLayout === "layout3") {
        const cardsWithColors = transformPersonsWithColorCoding(
          filteredPersons,
          schoolData
        );
        setIdCardData(cardsWithColors);
      } else {
        const cards = transformPersonsToIdCards(filteredPersons, schoolData);
        // Add bgColor property for consistency with the state type
        const cardsWithBgColor = cards.map((card) => ({
          ...card,
          bgColor: undefined as BgColorType | undefined,
        }));
        setIdCardData(cardsWithBgColor);
      }
    },
    [selectedLayout, preloadImages]
  );

  // Load data from backend
  useEffect(() => {
    const loadData = async () => {
      if (!selectedSchool) return;

      setLoading(true);
      try {
        // Load persons for the selected school
        const personsResponse = await axios.get(`${API_BASE_URL}/api/persons`, {
          params: {
            school_id: selectedSchool.id,
            limit: 1000, // Set a high limit to get all students
          },
        });

        const personsData = personsResponse.data.persons || [];
        console.log(
          `IDCardList: Fetched ${personsData.length} persons from API`
        );
        console.log("IDCardList API Response:", personsResponse.data);
        setPersons(personsData);

        // Transform data to ID card format using selected school
        await updateIdCardData(
          personsData,
          convertSchoolToSchoolData(selectedSchool),
          selectedType
        );
      } catch (error) {
        console.error("Error loading data:", error);
        showError("Failed to load data from server");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedSchool, selectedType, showError, updateIdCardData]);

  // Update ID card data when type filter changes
  useEffect(() => {
    const updateData = async () => {
      if (persons.length > 0 && selectedSchool) {
        await updateIdCardData(
          persons,
          convertSchoolToSchoolData(selectedSchool),
          selectedType
        );
      }
    };

    updateData();
  }, [selectedType, persons, selectedSchool, updateIdCardData]);

  const selectCard = (cardId: number) => {
    setSelectedCard(selectedCard === cardId ? null : cardId);
  };

  const clearSelection = () => {
    setSelectedCard(null);
  };

  const toggleCardSelection = (cardId: number) => {
    setSelectedCards((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(cardId)) {
        newSet.delete(cardId);
      } else {
        newSet.add(cardId);
      }
      return newSet;
    });
  };

  const selectAllCards = () => {
    const allCardIds = new Set(
      idCardData
        .map((card) => card.id)
        .filter((id) => id !== undefined) as number[]
    );
    setSelectedCards(allCardIds);
  };

  const clearAllSelections = () => {
    setSelectedCards(new Set());
  };

  // Clear selections when print mode changes
  useEffect(() => {
    if (printMode === "single") {
      setSelectedCards(new Set());
    } else if (printMode === "multiple") {
      setSelectedCard(null);
    } else if (printMode === "all_single") {
      setSelectedCard(null);
      setSelectedCards(new Set());
    }
  }, [printMode]);

  const renderCard = (card: IdCardProps & { bgColor?: BgColorType }) => {
    const commonProps = {
      id: card.id,
      schoolName: card.schoolName,
      motto: card.motto,
      subname: card.subname,
      contact: card.contact,
      validity: card.validity,
      image: card.image,
      logo: card.logo,
      studentName: card.studentName,
      address: card.address,
      studentclass: card.studentclass,
      idNumber: card.idNumber,
      phone: card.phone,
      roll: card.roll,
      principalSignature: card.principalSignature,
      personType: card.personType || "student",
      parentName: card.parentName,
      department: card.department,
      dateOfBirth: card.dateOfBirth,
      fieldVisibilityConfig: fieldVisibilityConfig,
    };

    const isSelected = selectedCard === card.id;
    const cardComponent = (() => {
      switch (selectedLayout) {
        case "layout2":
          // For Layout2, use automatic color coding based on person type instead of selectedBgColor
          // Ensure bgColor is provided, fallback to green if not available
          const bgColor = card.bgColor || "green";
          return <Layout2 key={card.id} {...commonProps} bgColor={bgColor} />;

        case "layout3":
          // For Layout3, use automatic color coding based on person type (similar to Layout2 but without parent name)
          const bgColor3 = card.bgColor || "green";
          return <Layout3 key={card.id} {...commonProps} bgColor={bgColor3} />;

        case "layout4":
          // For Layout4, credit card style layout
          return <Layout4 key={card.id} {...commonProps} />;

        default:
          return <Layout1 key={card.id} {...commonProps} />;
      }
    })();

    const isMultipleSelected =
      printMode === "multiple" && !!card.id && selectedCards.has(card.id);

    return (
      <div
        key={card.id}
        className={`relative ${
          printMode === "single" || printMode === "multiple"
            ? "cursor-pointer"
            : ""
        } ${
          printMode === "single" && isSelected ? "ring-2 ring-blue-500" : ""
        } ${
          printMode === "multiple" && isMultipleSelected
            ? "ring-2 ring-green-500"
            : ""
        }`}
        onClick={() => {
          if (printMode === "single" && card.id) {
            selectCard(card.id);
          } else if (printMode === "multiple" && card.id) {
            toggleCardSelection(card.id);
          }
        }}
      >
        {cardComponent}
        {printMode === "single" && isSelected && (
          <div className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded print:hidden">
            Selected
          </div>
        )}
        {printMode === "multiple" && (
          <div className="absolute -top-2 -left-2 print:hidden">
            <input
              type="checkbox"
              checked={isMultipleSelected}
              onChange={() => card.id && toggleCardSelection(card.id)}
              className="w-5 h-5 text-green-600 bg-white border-2 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        )}
        {printMode === "multiple" && isMultipleSelected && (
          <div className="absolute -top-2 -right-2 bg-green-600 text-white text-xs px-2 py-1 rounded print:hidden">
            Selected
          </div>
        )}
      </div>
    );
  };
  const handlePrint = () => {
    if (printMode === "single") {
      // Check if a card is selected
      if (selectedCard === null) {
        alert("Please select a card to print.");
        return;
      }

      // For single card mode, create a temporary container with the selected card
      const allCards = document.querySelectorAll(".id-cards-container > div");

      // Create a temporary print container
      const printContainer = document.createElement("div");
      printContainer.className = "single-print-container";

      // Only add the selected card
      allCards.forEach((card, index) => {
        const cardId = idCardData[index]?.id;
        if (cardId === selectedCard) {
          const cardClone = card.cloneNode(true) as HTMLElement;
          cardClone.className += " single-card-print";
          printContainer.appendChild(cardClone);
        }
      });

      // Add to body temporarily
      document.body.appendChild(printContainer);

      // Add single card print styles with actual card size
      const printStyles = `
        @media print {
          @page {
            size: 204px 324px;
            margin: 0;
          }

          body {
            margin: 0;
            padding: 0;
            width: 204px;
            height: 324px;
          }

          body * {
            visibility: hidden;
          }

          .single-print-container,
          .single-print-container * {
            visibility: visible;
          }

          .single-print-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 204px;
            height: 324px;
          }

          .single-card-print {
            width: 204px !important;
            height: 324px !important;
            display: flex !important;
            flex-direction: column !important;
            page-break-after: always !important;
            break-after: page !important;
            position: absolute;
            left: 0;
            top: 0;
            margin: 0 !important;
            padding: 0 !important;
            transform: none !important;
            border: none !important;
            outline: none !important;
            overflow: hidden !important;
          }

          .single-card-print > * {
            width: 100% !important;
            box-sizing: border-box !important;
          }

          .single-card-print .bg-\\[\\#00a54f\\] {
            width: 96px !important;
            right: 0 !important;
          }

          .single-card-print:last-child {
            page-break-after: avoid !important;
            break-after: avoid !important;
          }

          .print-button, .print-mode-selector, .id-cards-container {
            display: none !important;
          }
        }
      `;

      const styleElement = document.createElement("style");
      styleElement.textContent = printStyles;
      document.head.appendChild(styleElement);

      window.print();

      // Clean up after printing
      setTimeout(() => {
        if (document.head.contains(styleElement)) {
          document.head.removeChild(styleElement);
        }
        if (document.body.contains(printContainer)) {
          document.body.removeChild(printContainer);
        }
      }, 1000);
    } else if (printMode === "all_single") {
      // All cards mode - each card on its own page at actual size
      const allCards = document.querySelectorAll(".id-cards-container > div");

      // Create a temporary print container
      const printContainer = document.createElement("div");
      printContainer.className = "all-single-print-container";

      // Add all cards
      allCards.forEach((card) => {
        const cardClone = card.cloneNode(true) as HTMLElement;
        cardClone.className += " all-single-card-print";
        printContainer.appendChild(cardClone);
      });

      // Add to body temporarily
      document.body.appendChild(printContainer);

      // Add print styles for all cards - one per page
      const printStyles = `
        @media print {
          @page {
            size: 204px 324px;
            margin: 0;
          }

          body {
            margin: 0;
            padding: 0;
            width: 204px;
            height: 324px;
          }

          body * {
            visibility: hidden;
          }

          .all-single-print-container,
          .all-single-print-container * {
            visibility: visible;
          }

          .all-single-print-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 204px;
            height: 324px;
          }

          .all-single-card-print {
            width: 204px !important;
            height: 324px !important;
            display: flex !important;
            flex-direction: column !important;
            page-break-after: always !important;
            break-after: page !important;
            margin: 0 !important;
            padding: 0 !important;
            transform: none !important;
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
            overflow: hidden !important;
          }

          .all-single-card-print > * {
            width: 100% !important;
            box-sizing: border-box !important;
          }

          .all-single-card-print .bg-\\[\\#00a54f\\] {
            width: 96px !important;
            right: 0 !important;
          }

          .all-single-card-print:last-child {
            page-break-after: avoid !important;
            break-after: avoid !important;
          }

          .print-button, .print-mode-selector, .id-cards-container {
            display: none !important;
          }
        }
      `;

      const styleElement = document.createElement("style");
      styleElement.textContent = printStyles;
      document.head.appendChild(styleElement);

      window.print();

      // Clean up after printing
      setTimeout(() => {
        if (document.head.contains(styleElement)) {
          document.head.removeChild(styleElement);
        }
        if (document.body.contains(printContainer)) {
          document.body.removeChild(printContainer);
        }
      }, 1000);
    } else {
      // Multiple cards mode - print selected cards one by one (each on its own page)
      if (selectedCards.size === 0) {
        alert("Please select at least one card to print.");
        return;
      }

      // Get all cards from the container
      const allCards = document.querySelectorAll(".id-cards-container > div");

      // Create a temporary print container
      const printContainer = document.createElement("div");
      printContainer.className = "multiple-single-print-container";

      // Only add selected cards
      allCards.forEach((card, index) => {
        const cardId = idCardData[index]?.id;
        if (cardId && selectedCards.has(cardId)) {
          const cardClone = card.cloneNode(true) as HTMLElement;
          cardClone.className += " multiple-single-card-print";
          printContainer.appendChild(cardClone);
        }
      });

      // Add to body temporarily
      document.body.appendChild(printContainer);

      // Print styles for multiple cards - each on its own page (like all_single mode)
      const printStyles = `
        @media print {
          @page {
            size: 204px 324px;
            margin: 0;
          }

          body {
            margin: 0;
            padding: 0;
            width: 204px;
            height: 324px;
          }

          body * {
            visibility: hidden;
          }

          .multiple-single-print-container,
          .multiple-single-print-container * {
            visibility: visible;
          }

          .multiple-single-print-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 204px;
            height: 324px;
          }

          .multiple-single-card-print {
            width: 204px !important;
            height: 324px !important;
            transform: scale(1) !important;
            box-shadow: none !important;
            page-break-after: always;
            break-after: page;
          }

          .multiple-single-card-print:last-child {
            page-break-after: avoid !important;
            break-after: avoid !important;
          }

          .print-button, .print-mode-selector, .id-cards-container {
            display: none !important;
          }
        }
      `;

      const styleElement = document.createElement("style");
      styleElement.textContent = printStyles;
      document.head.appendChild(styleElement);

      window.print();

      // Clean up after printing
      setTimeout(() => {
        if (document.head.contains(styleElement)) {
          document.head.removeChild(styleElement);
        }
        if (document.body.contains(printContainer)) {
          document.body.removeChild(printContainer);
        }
      }, 1000);
    }
  };

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading ID cards...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              Card Statistics
            </h2>
            <p className="text-gray-600 mt-1">
              Cards for {selectedSchool?.name || "School"}
            </p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold text-blue-600">
              {idCardData.length}
            </div>
            <div className="text-sm text-gray-500">Available Cards</div>
          </div>
        </div>
      </div>

      {/* Type Filter */}
      <div className="mb-4 p-4 bg-gray-50 rounded-lg border">
        <h3 className="text-sm font-semibold text-gray-700 mb-2">
          Filter by Type:
        </h3>
        <div className="flex gap-4">
          {[
            { value: "all", label: "👥 All Types", count: persons.length },
            {
              value: "student",
              label: "🎓 Students",
              count: persons.filter((p) => p.type === "student").length,
            },
            {
              value: "staff",
              label: "👨‍🏫 Teaching Staff",
              count: persons.filter((p) => p.type === "staff").length,
            },
            {
              value: "non_teaching",
              label: "👷 Non-Teaching Staff",
              count: persons.filter((p) => p.type === "non_teaching").length,
            },
          ].map(({ value, label, count }) => (
            <label key={value} className="flex items-center cursor-pointer">
              <input
                type="radio"
                name="typeFilter"
                value={value}
                checked={selectedType === value}
                onChange={(e) => setSelectedType(e.target.value as any)}
                className="mr-2"
              />
              <span className="text-sm text-gray-600">
                {label} ({count})
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Layout Selector */}
      <LayoutSelector
        selectedLayout={selectedLayout}
        onLayoutChange={async (layout) => {
          setSelectedLayout(layout);
          if (persons.length > 0 && selectedSchool) {
            await updateIdCardData(
              persons,
              convertSchoolToSchoolData(selectedSchool),
              selectedType
            );
          }
        }}
        selectedBgColor={selectedBgColor}
        onBgColorChange={setSelectedBgColor}
      />

      {/* Print Mode Selector */}
      <div className="print-mode-selector mb-4 p-4 bg-gray-50 rounded-lg border">
        <h3 className="text-sm font-semibold text-gray-700 mb-2">
          Print Mode:
        </h3>
        <div className="flex gap-4">
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              name="printMode"
              value="single"
              checked={printMode === "single"}
              onChange={(e) =>
                setPrintMode(
                  e.target.value as "single" | "multiple" | "all_single"
                )
              }
              className="mr-2"
            />
            <span className="text-sm text-gray-600">
              📄 Single Card (Select)
            </span>
          </label>
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              name="printMode"
              value="all_single"
              checked={printMode === "all_single"}
              onChange={(e) =>
                setPrintMode(
                  e.target.value as "single" | "multiple" | "all_single"
                )
              }
              className="mr-2"
            />
            <span className="text-sm text-gray-600">
              📄 All Cards - One per Page
            </span>
          </label>
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              name="printMode"
              value="multiple"
              checked={printMode === "multiple"}
              onChange={(e) =>
                setPrintMode(
                  e.target.value as "single" | "multiple" | "all_single"
                )
              }
              className="mr-2"
            />
            <span className="text-sm text-gray-600">
              📋 Multiple Cards - One per Page (Select)
            </span>
          </label>
        </div>
      </div>

      {/* Selected Card Display for Single Mode */}
      {printMode === "single" && (
        <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-sm font-semibold text-blue-700 mb-2">
                Click on a card to select it for printing
              </h3>
              {selectedCard && (
                <div className="text-sm text-blue-600">
                  <strong>Selected:</strong>{" "}
                  {
                    idCardData.find((card) => card.id === selectedCard)
                      ?.studentName
                  }
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <button
                onClick={clearSelection}
                className="text-xs bg-gray-500 text-white px-3 py-1 rounded"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Multiple Selection Controls */}
      {printMode === "multiple" && (
        <div className="mb-4 p-4 bg-green-50 rounded-lg border border-green-200">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-sm font-semibold text-green-700 mb-2">
                Select multiple cards to print one by one (each on separate
                page)
              </h3>
              <div className="text-sm text-green-600">
                <strong>Selected:</strong> {selectedCards.size} card
                {selectedCards.size !== 1 ? "s" : ""}
              </div>
            </div>
            <div className="flex gap-2">
              <button
                onClick={selectAllCards}
                className="text-xs bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700"
              >
                Select All
              </button>
              <button
                onClick={clearAllSelections}
                className="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600"
              >
                Clear All
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Print Button - Right aligned */}
      <div className="flex justify-end mb-4">
        <button
          onClick={handlePrint}
          className={`print-button font-bold py-2 px-4 rounded shadow-lg flex items-center gap-2 ${
            (printMode === "single" && selectedCard === null) ||
            (printMode === "multiple" && selectedCards.size === 0)
              ? "bg-gray-400 cursor-not-allowed text-gray-200"
              : printMode === "multiple"
              ? "bg-green-600 text-white"
              : "bg-blue-600 text-white"
          }`}
          disabled={
            (printMode === "single" && selectedCard === null) ||
            (printMode === "multiple" && selectedCards.size === 0)
          }
        >
          🖨️ Print Cards (
          {printMode === "single"
            ? selectedCard
              ? "1 Selected"
              : "None Selected"
            : printMode === "all_single"
            ? "All Cards"
            : `${selectedCards.size} Selected`}
          )
        </button>
      </div>

      {/* ID Cards Layout with Selected Design */}
      {idCardData.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <div className="text-gray-400 text-6xl mb-4">📄</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No ID Cards Available
          </h3>
          <p className="text-gray-600">
            {selectedType === "all"
              ? "No persons found in the database. Please add some persons first."
              : `No ${selectedType} records found. Try selecting a different type.`}
          </p>
        </div>
      ) : imagesLoading ? (
        <div className="id-cards-container flex flex-wrap gap-4">
          {/* Show loading cards while images are being preloaded */}
          {Array.from({ length: Math.min(idCardData.length, 8) }).map(
            (_, index) => (
              <LoadingCard key={`loading-${index}`} layout={selectedLayout} />
            )
          )}
        </div>
      ) : (
        <div className="id-cards-container flex flex-wrap gap-4">
          {idCardData.map((card) => renderCard(card))}
        </div>
      )}
    </div>
  );
}
