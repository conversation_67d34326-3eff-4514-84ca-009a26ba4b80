import { useState, useEffect, useCallback } from 'react';
import { imageCache } from '../utils/imageCache';

interface UseImagePreloaderOptions {
  enabled?: boolean;
  timeout?: number;
}

interface UseImagePreloaderReturn {
  isLoading: boolean;
  isLoaded: boolean;
  hasError: boolean;
  preloadImage: (url: string) => Promise<boolean>;
  reset: () => void;
}

/**
 * Hook for preloading and managing image loading state
 */
export const useImagePreloader = (
  url?: string,
  options: UseImagePreloaderOptions = {}
): UseImagePreloaderReturn => {
  const { enabled = true, timeout = 8000 } = options;
  
  const [isLoading, setIsLoading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const reset = useCallback(() => {
    setIsLoading(false);
    setIsLoaded(false);
    setHasError(false);
  }, []);

  const preloadImage = useCallback(async (imageUrl: string): Promise<boolean> => {
    if (!imageUrl || imageUrl.trim() === '') {
      setHasError(true);
      return false;
    }

    // Check cache first
    const cacheStatus = imageCache.getCacheStatus(imageUrl);
    if (cacheStatus.cached) {
      setIsLoaded(cacheStatus.loaded);
      setHasError(cacheStatus.error);
      return cacheStatus.loaded;
    }

    setIsLoading(true);
    setHasError(false);
    setIsLoaded(false);

    try {
      const success = await imageCache.preloadImage(imageUrl);
      setIsLoaded(success);
      setHasError(!success);
      return success;
    } catch (error) {
      console.error('Image preload failed:', error);
      setHasError(true);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Auto-preload when URL changes
  useEffect(() => {
    if (!enabled || !url) {
      reset();
      return;
    }

    preloadImage(url);
  }, [url, enabled, preloadImage, reset]);

  return {
    isLoading,
    isLoaded,
    hasError,
    preloadImage,
    reset,
  };
};

/**
 * Hook for preloading multiple images
 */
export const useMultipleImagePreloader = (
  urls: string[],
  options: UseImagePreloaderOptions = {}
) => {
  const { enabled = true } = options;
  
  const [loadingCount, setLoadingCount] = useState(0);
  const [loadedCount, setLoadedCount] = useState(0);
  const [errorCount, setErrorCount] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  const preloadImages = useCallback(async (imageUrls: string[]) => {
    if (!enabled || imageUrls.length === 0) {
      setIsComplete(true);
      return;
    }

    setLoadingCount(imageUrls.length);
    setLoadedCount(0);
    setErrorCount(0);
    setIsComplete(false);

    try {
      const { loaded, failed } = await imageCache.preloadImages(imageUrls);
      setLoadedCount(loaded.length);
      setErrorCount(failed.length);
    } catch (error) {
      console.error('Multiple image preload failed:', error);
      setErrorCount(imageUrls.length);
    } finally {
      setIsComplete(true);
      setLoadingCount(0);
    }
  }, [enabled]);

  useEffect(() => {
    if (urls.length > 0) {
      preloadImages(urls);
    } else {
      setIsComplete(true);
      setLoadingCount(0);
      setLoadedCount(0);
      setErrorCount(0);
    }
  }, [urls, preloadImages]);

  return {
    isLoading: loadingCount > 0,
    isComplete,
    loadedCount,
    errorCount,
    totalCount: urls.length,
    successRate: urls.length > 0 ? loadedCount / urls.length : 0,
    preloadImages,
  };
};

export default useImagePreloader;
