const sequelize = require('../config/database');
const migration = require('../migrations/add-school-id-to-persons');

async function runMigration() {
  try {
    console.log('Starting migration: add-school-id-to-persons');
    
    // Run the migration
    await migration.up(sequelize.getQueryInterface(), sequelize);
    
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
