// Export all services and their types (avoiding duplicate ValidationResult exports)
export * from "./apiService";
export * from "./cache";
export * from "./requestQueue";

// Export service classes
export { PersonService, personService } from "./personService";
export { SchoolService, schoolService } from "./schoolService";
export { CardService, cardService } from "./cardService";

// Export types separately
export type {
  Person,
  GetPersonsParams,
  GetPersonsResponse,
  CreatePersonData,
  UpdatePersonData,
  BulkDeleteResponse,
  CsvUploadResponse,
  PersonStats,
  ExportPersonsParams,
  PersonServiceStats,
} from "./personService";

export type {
  School,
  CreateSchoolData,
  UpdateSchoolData,
  SchoolStats,
  SchoolServiceStats,
} from "./schoolService";

export type {
  PersonType,
  CardConfig,
  CardTemplate,
  CardTheme,
  CardDimensions,
  CardStats,
  CardPreview,
  PdfOptions,
  CardServiceStats,
} from "./cardService";

// Export ValidationResult once to avoid conflicts
export type { ValidationResult } from "./personService";

// Service manager for coordinated operations
export class ServiceManager {
  /**
   * Initialize all services
   */
  static async initialize(): Promise<void> {
    try {
      // Import services dynamically to avoid circular dependencies
      const { apiService } = await import("./apiService");

      // Test API connectivity
      const isHealthy = await apiService.healthCheck();
      if (!isHealthy) {
        console.warn("API health check failed");
      }

      console.log("Services initialized successfully");
    } catch (error) {
      console.error("Failed to initialize services:", error);
    }
  }

  /**
   * Clear all caches
   */
  static clearAllCaches(): void {
    // Import services to avoid circular dependencies
    import("./apiService").then(({ apiService }) => apiService.clearCache());
    import("./personService").then(({ personService }) =>
      personService.clearCache()
    );
    import("./schoolService").then(({ schoolService }) =>
      schoolService.clearCache()
    );
    import("./cardService").then(({ cardService }) => cardService.clearCache());
  }

  /**
   * Get combined statistics from all services
   */
  static async getStats(): Promise<ServiceStats> {
    const [
      { apiService },
      { personService },
      { schoolService },
      { cardService },
    ] = await Promise.all([
      import("./apiService"),
      import("./personService"),
      import("./schoolService"),
      import("./cardService"),
    ]);

    return {
      api: apiService.getCacheStats(),
      person: personService.getStats(),
      school: schoolService.getStats(),
      card: cardService.getStats(),
    };
  }

  /**
   * Cleanup all services
   */
  static async destroy(): Promise<void> {
    const [{ personService }, { cardService }] = await Promise.all([
      import("./personService"),
      import("./cardService"),
    ]);

    personService.destroy();
    cardService.destroy();
  }
}

// Types
export interface ServiceStats {
  api: any;
  person: any;
  school: any;
  card: any;
}
