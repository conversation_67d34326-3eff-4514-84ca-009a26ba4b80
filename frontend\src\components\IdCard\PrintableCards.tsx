import React from "react";
import IdCard from "./IDCard";
import { IdCardProps } from "../../utils/dataMapping";

interface PrintableCardsProps {
  cards: IdCardProps[];
}

const PrintableCards: React.FC<PrintableCardsProps> = ({ cards }) => {
  const handlePrint = () => {
    // Add print styles dynamically
    const isSingleCard = cards.length === 1;

    const printStyles = `
      @media print {
        @page {
          ${
            isSingleCard
              ? "size: 85.6mm 53.98mm; margin: 0;"
              : "size: A4; margin: 10mm;"
          }
        }

        body * {
          visibility: hidden;
        }

        .printable-content,
        .printable-content * {
          visibility: visible;
        }

        .printable-content {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
        }

        ${
          isSingleCard
            ? `
        .print-page {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 85.6mm;
          height: 53.98mm;
          page-break-after: avoid;
        }

        .print-card {
          width: 85.6mm !important;
          height: 53.98mm !important;
          transform: scale(1) !important;
          box-shadow: none !important;
        }
        `
            : `
        .print-page {
          page-break-after: always;
          display: grid;
          grid-template-columns: 1fr 1fr;
          grid-template-rows: 1fr 1fr;
          gap: 5mm;
          height: 277mm;
          width: 190mm;
        }

        .print-page:last-child {
          page-break-after: avoid;
        }

        .print-card {
          width: 85.6mm !important;
          height: 53.98mm !important;
          transform: scale(1) !important;
          box-shadow: none !important;
        }
        `
        }
      }
    `;

    const styleElement = document.createElement("style");
    styleElement.textContent = printStyles;
    document.head.appendChild(styleElement);

    window.print();

    // Remove styles after printing
    setTimeout(() => {
      if (document.head.contains(styleElement)) {
        document.head.removeChild(styleElement);
      }
    }, 1000);
  };

  // Group cards into pages of 4
  const groupedCards: any[][] = [];
  const isSingleCard = cards.length === 1;

  if (isSingleCard) {
    groupedCards.push(cards);
  } else {
    for (let i = 0; i < cards.length; i += 4) {
      groupedCards.push(cards.slice(i, i + 4));
    }
  }

  return (
    <div>
      {/* Print Button */}
      <button
        onClick={handlePrint}
        className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded shadow-lg transition-colors duration-200 flex items-center gap-2"
      >
        🖨️ Print ID Cards (Browser Print)
      </button>

      {/* Screen Preview Styles */}
      <style>{`
        .print-preview {
          background: #f5f5f5;
          padding: 20px;
          margin-top: 20px;
        }

        ${
          isSingleCard
            ? `
        .print-page {
          background: white;
          margin: 20px auto;
          padding: 0;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
          display: flex;
          justify-content: center;
          align-items: center;
          width: 204px;
          height: 324px;
        }

        .print-card {
          transform-origin: center;
          width: 204px;
          height: 324px;
        }
        `
            : `
        .print-page {
          background: white;
          margin: 20px auto;
          padding: 10mm;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
          display: grid;
          grid-template-columns: 1fr 1fr;
          grid-template-rows: 1fr 1fr;
          gap: 5mm;
          width: 210mm;
          min-height: 297mm;
        }

        .print-card {
          transform: scale(0.4);
          transform-origin: top left;
          width: 204px;
          height: 324px;
        }
        `
        }
      `}</style>

      {/* Printable Content */}
      <div className="printable-content print-preview">
        {groupedCards.map((pageCards, pageIndex) => (
          <div key={pageIndex} className="print-page">
            {pageCards.map((card) => (
              <div key={card.id} className="print-card">
                <IdCard
                  id={card.id}
                  schoolName={card.schoolName}
                  motto={card.motto}
                  subname={card.subname}
                  contact={card.contact}
                  validity={card.validity}
                  image={card.image}
                  logo={card.logo}
                  studentName={card.studentName}
                  address={card.address}
                  studentclass={card.studentclass}
                  designation={card.designation}
                  idNumber={card.idNumber}
                  phone={card.phone}
                  roll={card.roll}
                  principalSignature={card.principalSignature}
                />
              </div>
            ))}
            {/* Fill empty slots if less than 4 cards (only for multi-card layout) */}
            {!isSingleCard &&
              Array.from({ length: 4 - pageCards.length }).map((_, index) => (
                <div key={`empty-${index}`} className="print-card"></div>
              ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PrintableCards;
