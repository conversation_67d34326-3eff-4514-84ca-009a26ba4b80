// Example of how to update CardHeader.tsx to use the new Layout1 data mapping functions
// This demonstrates how to fix the school name truncation issue

import React from 'react';
import { renderSchoolNameForHeader } from './layout1DataMapping';

// Example of the UPDATED CardHeader component that would fix the truncation issue
const UpdatedCardHeaderExample = ({
  logo,
  motto,
  schoolName,
  subname,
  contact,
}: {
  logo: string;
  motto: string;
  schoolName: string;
  subname: string;
  contact: string;
}) => {
  // Use the new formatting function to handle long school names
  const { lines, className, shouldWrap } = renderSchoolNameForHeader(schoolName);

  return (
    <>
      {/* Header Section */}
      <div className="bg-[#2d3091] px-2 py-1.5 text-white relative flex-shrink-0">
        <div className="flex items-center gap-1.5">
          <div className="w-9 h-9 bg-white rounded-full flex items-center justify-center flex-shrink-0">
            <div className="relative w-full h-full">
              <img
                src={logo}
                alt="Logo"
                className="absolute inset-0 object-cover rounded-full w-full h-full overflow-hidden"
              />
            </div>
          </div>

          <div className="flex-1">
            <div className="text-[7px] italic mb-0.5 text-center">{motto}</div>
            
            {/* UPDATED: Use the new formatting logic */}
            <div className={className}>
              {lines.length === 1 ? (
                // Single line display
                <span>{lines[0]}</span>
              ) : (
                // Multi-line display
                lines.map((line, index) => (
                  <div key={index}>{line}</div>
                ))
              )}
            </div>
            
            <div className="text-[11px] text-center font-semibold -mt-0.5">
              {subname}
            </div>
            <div className="text-[#eee231] text-[7px] text-center mt-0.5">
              {contact}
            </div>
          </div>
        </div>
      </div>

      <div className="h-0.5 bg-[#00a54f] flex-shrink-0"></div>

      <div className="bg-[#2d3091] w-fit mx-auto px-2.5 rounded-b-md text-white text-center py-0.5 flex-shrink-0">
        <div className="text-[9px] font-bold">ID CARD</div>
      </div>
    </>
  );
};

// Example of how the CURRENT CardHeader looks (with the truncation problem)
const CurrentCardHeaderExample = ({
  logo,
  motto,
  schoolName,
  subname,
  contact,
}: {
  logo: string;
  motto: string;
  schoolName: string;
  subname: string;
  contact: string;
}) => {
  return (
    <>
      {/* Header Section */}
      <div className="bg-[#2d3091] px-2 py-1.5 text-white relative flex-shrink-0">
        <div className="flex items-center gap-1.5">
          <div className="w-9 h-9 bg-white rounded-full flex items-center justify-center flex-shrink-0">
            <div className="relative w-full h-full">
              <img
                src={logo}
                alt="Logo"
                className="absolute inset-0 object-cover rounded-full w-full h-full overflow-hidden"
              />
            </div>
          </div>

          <div className="flex-1">
            <div className="text-[7px] italic mb-0.5 text-center">{motto}</div>
            
            {/* PROBLEM: This causes truncation for long school names */}
            <div className="text-base whitespace-nowrap font-bold text-center -mt-1">
              {schoolName}
            </div>
            
            <div className="text-[11px] text-center font-semibold -mt-0.5">
              {subname}
            </div>
            <div className="text-[#eee231] text-[7px] text-center mt-0.5">
              {contact}
            </div>
          </div>
        </div>
      </div>

      <div className="h-0.5 bg-[#00a54f] flex-shrink-0"></div>

      <div className="bg-[#2d3091] w-fit mx-auto px-2.5 rounded-b-md text-white text-center py-0.5 flex-shrink-0">
        <div className="text-[9px] font-bold">ID CARD</div>
      </div>
    </>
  );
};

// Example usage and comparison
export const CardHeaderComparison = () => {
  const exampleData = {
    logo: "/path/to/logo.png",
    motto: '"Leading towards brightness"',
    schoolName: "MAI VALLEY BOARDING SCHOOL", // This would be truncated in current version
    subname: "KATHMANDU, NEPAL",
    contact: "01-4567890",
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-bold mb-4">Current Implementation (Truncated)</h3>
        <div className="w-[204px]">
          <CurrentCardHeaderExample {...exampleData} />
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-bold mb-4">Updated Implementation (Fixed)</h3>
        <div className="w-[204px]">
          <UpdatedCardHeaderExample {...exampleData} />
        </div>
      </div>
    </div>
  );
};

// Instructions for implementing the fix
export const ImplementationInstructions = `
To fix the school name truncation issue in Layout1:

1. Import the formatting function in CardHeader.tsx:
   import { renderSchoolNameForHeader } from '../../utils/layout1DataMapping';

2. Replace the current school name rendering logic:
   
   // OLD (causes truncation):
   <div className="text-base whitespace-nowrap font-bold text-center -mt-1">
     {schoolName}
   </div>
   
   // NEW (handles long names):
   const { lines, className } = renderSchoolNameForHeader(schoolName);
   <div className={className}>
     {lines.length === 1 ? (
       <span>{lines[0]}</span>
     ) : (
       lines.map((line, index) => (
         <div key={index}>{line}</div>
       ))
     )}
   </div>

3. The formatting function will automatically:
   - Break long school names into multiple lines at natural word boundaries
   - Adjust font size based on name length
   - Handle special cases like "BOARDING SCHOOL" patterns
   - Maintain proper spacing and alignment

4. Test with various school name lengths:
   - Short names: "ABC School"
   - Medium names: "Kathmandu High School"  
   - Long names: "Mai Valley Boarding School"
   - Very long names: "The Global College of Engineering and Management"
`;

export default {
  UpdatedCardHeaderExample,
  CurrentCardHeaderExample,
  CardHeaderComparison,
  ImplementationInstructions,
};
