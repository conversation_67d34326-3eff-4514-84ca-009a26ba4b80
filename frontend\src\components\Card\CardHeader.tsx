import React from 'react';
import { CardHeaderProps } from './types';

const CardHeader: React.FC<CardHeaderProps> = ({
  school,
  template,
  logoBase64
}) => {
  // Birta Global School logo with wheat design
  const defaultLogo = (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="16" cy="16" r="14" fill="white" stroke="none"/>
      {/* Wheat/grain design */}
      <g fill="#FF6B35">
        <circle cx="16" cy="8" r="1.5"/>
        <circle cx="13" cy="10" r="1"/>
        <circle cx="19" cy="10" r="1"/>
        <circle cx="16" cy="12" r="1.5"/>
        <circle cx="14" cy="14" r="1"/>
        <circle cx="18" cy="14" r="1"/>
        <circle cx="16" cy="16" r="1.5"/>
        <circle cx="13" cy="18" r="1"/>
        <circle cx="19" cy="18" r="1"/>
        <circle cx="16" cy="20" r="1.5"/>
        <circle cx="14" cy="22" r="1"/>
        <circle cx="18" cy="22" r="1"/>
        <circle cx="16" cy="24" r="1.5"/>
      </g>
      {/* Stem */}
      <rect x="15.5" y="20" width="1" height="6" fill="#4CAF50"/>
    </svg>
  );

  return (
    <div className="card-header">
      <div className="school-logo">
        {logoBase64 ? (
          <img
            src={logoBase64}
            alt="School Logo"
            style={{ width: '32px', height: '32px', borderRadius: '50%' }}
          />
        ) : (
          defaultLogo
        )}
      </div>
      <div className="school-name">
        Birta Global School
        <br />
        <span style={{ fontSize: '10px', opacity: 0.8, fontWeight: 'normal' }}>BIRTAMODE.COM</span>
      </div>
    </div>
  );
};

export default CardHeader;
