const fs = require("fs");
const csv = require("csv-parser");
const path = require("path");

// Test CSV parsing
const testCsvFile = (filePath) => {
  console.log(`Testing CSV file: ${filePath}`);

  if (!fs.existsSync(filePath)) {
    console.log("File does not exist!");
    return;
  }

  const results = [];
  const errors = [];

  fs.createReadStream(filePath)
    .pipe(csv())
    .on("data", (data) => {
      console.log("Raw row data:", data);

      // Check required fields
      if (!data.person_id || !data.name || !data.type) {
        errors.push(`Missing required fields: ${JSON.stringify(data)}`);
        return;
      }

      // Check type
      if (!["student", "staff", "non_teaching"].includes(data.type)) {
        errors.push(`Invalid type: ${data.type}`);
        return;
      }

      // Check student requirements
      if (data.type === "student" && !data.class) {
        errors.push(`Student missing class: ${data.person_id}`);
        return;
      }

      results.push(data);
    })
    .on("end", () => {
      console.log(`\nParsing completed:`);
      console.log(`- Valid rows: ${results.length}`);
      console.log(`- Errors: ${errors.length}`);

      if (errors.length > 0) {
        console.log("\nErrors found:");
        errors.forEach((error) => console.log(`  - ${error}`));
      }

      if (results.length > 0) {
        console.log("\nFirst few valid records:");
        results.slice(0, 3).forEach((record) => {
          console.log(
            `  - ${record.person_id}: ${record.name} (${record.type})`
          );
        });
      }
    })
    .on("error", (error) => {
      console.error("CSV parsing error:", error);
    });
};

// Test both sample files
const sampleFile1 = path.join(__dirname, "sample-data.csv");
const sampleFile2 = path.join(__dirname, "sample-data-2.csv");

console.log("=== Testing Sample CSV Files ===\n");

testCsvFile(sampleFile1);

setTimeout(() => {
  console.log("\n" + "=".repeat(50) + "\n");
  testCsvFile(sampleFile2);
}, 1000);
