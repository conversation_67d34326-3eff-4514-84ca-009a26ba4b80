{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon src/app.js", "build": "tsc", "start": "node src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@iconify/react": "^6.0.0", "bcrypt": "^6.0.0", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^17.0.1", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "html2canvas": "^1.4.1", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lodash": "^4.17.21", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cache": "^5.1.2", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "pdfjs-dist": "^5.3.31", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "sharp": "^0.34.3", "sqlite3": "^5.1.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/node": "^24.0.10", "autoprefixer": "^10.4.21", "nodemon": "^3.1.10", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}