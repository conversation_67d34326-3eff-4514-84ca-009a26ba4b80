/**
 * Utility function to check if a field has a valid value for ID card display
 * @param value - The field value to check
 * @returns boolean - true if the field has a valid value, false otherwise
 */
export const hasValidValue = (value: string | undefined | null): boolean => {
  return (
    value !== null &&
    value !== undefined &&
    value.trim() !== "" &&
    value.trim() !== "00"
  );
};

/**
 * Utility function to check if a field should be displayed on the ID card
 * Combines field visibility configuration with value validation
 * @param fieldConfig - The field visibility configuration
 * @param fieldName - The name of the field to check
 * @param value - The field value to check
 * @returns boolean - true if the field should be displayed, false otherwise
 */
export const shouldDisplayField = (
  fieldConfig: any,
  fieldName: string,
  value: string | undefined | null
): boolean => {
  const isFieldEnabled = fieldConfig?.[fieldName] !== false;
  const hasValue = hasValidValue(value);
  return isFieldEnabled && hasValue;
};
