import React, { useState, useRef, useCallback, useEffect } from 'react';
import { CropArea } from '../../utils/imageProcessing';

interface CropControlsProps {
  image: HTMLImageElement;
  cropArea: CropArea;
  onCropChange: (cropArea: CropArea) => void;
  aspectRatio?: number;
  minSize?: number;
  showGrid?: boolean;
  className?: string;
}

type ResizeHandle = 
  | 'nw' | 'n' | 'ne' 
  | 'w' | 'e' 
  | 'sw' | 's' | 'se'
  | 'move';

const CropControls: React.FC<CropControlsProps> = ({
  image,
  cropArea,
  onCropChange,
  aspectRatio,
  minSize = 50,
  showGrid = true,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragHandle, setDragHandle] = useState<ResizeHandle | null>(null);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [initialCrop, setInitialCrop] = useState<CropArea>(cropArea);
  const [scale, setScale] = useState(1);

  // Calculate scale factor between displayed image and actual image
  useEffect(() => {
    if (containerRef.current && image) {
      const container = containerRef.current;
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;
      
      const scaleX = containerWidth / image.naturalWidth;
      const scaleY = containerHeight / image.naturalHeight;
      setScale(Math.min(scaleX, scaleY));
    }
  }, [image]);

  const getScaledCrop = useCallback(() => {
    return {
      x: cropArea.x * scale,
      y: cropArea.y * scale,
      width: cropArea.width * scale,
      height: cropArea.height * scale
    };
  }, [cropArea, scale]);

  const handleMouseDown = useCallback((e: React.MouseEvent, handle: ResizeHandle) => {
    e.preventDefault();
    setIsDragging(true);
    setDragHandle(handle);
    setDragStart({ x: e.clientX, y: e.clientY });
    setInitialCrop(cropArea);
  }, [cropArea]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !dragHandle) return;

    const deltaX = (e.clientX - dragStart.x) / scale;
    const deltaY = (e.clientY - dragStart.y) / scale;

    let newCrop = { ...initialCrop };

    switch (dragHandle) {
      case 'move':
        newCrop.x = Math.max(0, Math.min(initialCrop.x + deltaX, image.naturalWidth - initialCrop.width));
        newCrop.y = Math.max(0, Math.min(initialCrop.y + deltaY, image.naturalHeight - initialCrop.height));
        break;

      case 'nw':
        newCrop.x = Math.max(0, initialCrop.x + deltaX);
        newCrop.y = Math.max(0, initialCrop.y + deltaY);
        newCrop.width = Math.max(minSize, initialCrop.width - deltaX);
        newCrop.height = Math.max(minSize, initialCrop.height - deltaY);
        break;

      case 'n':
        newCrop.y = Math.max(0, initialCrop.y + deltaY);
        newCrop.height = Math.max(minSize, initialCrop.height - deltaY);
        break;

      case 'ne':
        newCrop.y = Math.max(0, initialCrop.y + deltaY);
        newCrop.width = Math.max(minSize, initialCrop.width + deltaX);
        newCrop.height = Math.max(minSize, initialCrop.height - deltaY);
        break;

      case 'w':
        newCrop.x = Math.max(0, initialCrop.x + deltaX);
        newCrop.width = Math.max(minSize, initialCrop.width - deltaX);
        break;

      case 'e':
        newCrop.width = Math.max(minSize, initialCrop.width + deltaX);
        break;

      case 'sw':
        newCrop.x = Math.max(0, initialCrop.x + deltaX);
        newCrop.width = Math.max(minSize, initialCrop.width - deltaX);
        newCrop.height = Math.max(minSize, initialCrop.height + deltaY);
        break;

      case 's':
        newCrop.height = Math.max(minSize, initialCrop.height + deltaY);
        break;

      case 'se':
        newCrop.width = Math.max(minSize, initialCrop.width + deltaX);
        newCrop.height = Math.max(minSize, initialCrop.height + deltaY);
        break;
    }

    // Maintain aspect ratio if specified
    if (aspectRatio && dragHandle !== 'move') {
      if (dragHandle.includes('e') || dragHandle.includes('w')) {
        newCrop.height = newCrop.width / aspectRatio;
      } else if (dragHandle.includes('n') || dragHandle.includes('s')) {
        newCrop.width = newCrop.height * aspectRatio;
      }
    }

    // Ensure crop stays within image bounds
    newCrop.x = Math.max(0, Math.min(newCrop.x, image.naturalWidth - newCrop.width));
    newCrop.y = Math.max(0, Math.min(newCrop.y, image.naturalHeight - newCrop.height));
    newCrop.width = Math.min(newCrop.width, image.naturalWidth - newCrop.x);
    newCrop.height = Math.min(newCrop.height, image.naturalHeight - newCrop.y);

    onCropChange(newCrop);
  }, [isDragging, dragHandle, dragStart, initialCrop, scale, image, aspectRatio, minSize, onCropChange]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragHandle(null);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const scaledCrop = getScaledCrop();

  return (
    <div 
      ref={containerRef}
      className={`relative overflow-hidden bg-black ${className}`}
      style={{ aspectRatio: `${image.naturalWidth} / ${image.naturalHeight}` }}
    >
      {/* Background Image */}
      <img
        src={image.src}
        alt="Crop preview"
        className="w-full h-full object-contain"
        draggable={false}
      />

      {/* Dark Overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-50" />

      {/* Crop Area */}
      <div
        className="absolute border-2 border-white bg-transparent cursor-move"
        style={{
          left: scaledCrop.x,
          top: scaledCrop.y,
          width: scaledCrop.width,
          height: scaledCrop.height,
        }}
        onMouseDown={(e) => handleMouseDown(e, 'move')}
      >
        {/* Clear crop area */}
        <div className="absolute inset-0 bg-black bg-opacity-0" />

        {/* Grid Lines */}
        {showGrid && (
          <>
            <div className="absolute top-1/3 left-0 right-0 h-px bg-white opacity-50" />
            <div className="absolute top-2/3 left-0 right-0 h-px bg-white opacity-50" />
            <div className="absolute left-1/3 top-0 bottom-0 w-px bg-white opacity-50" />
            <div className="absolute left-2/3 top-0 bottom-0 w-px bg-white opacity-50" />
          </>
        )}

        {/* Resize Handles */}
        {/* Corner handles */}
        <div
          className="absolute -top-1 -left-1 w-3 h-3 bg-white border border-gray-400 cursor-nw-resize"
          onMouseDown={(e) => handleMouseDown(e, 'nw')}
        />
        <div
          className="absolute -top-1 -right-1 w-3 h-3 bg-white border border-gray-400 cursor-ne-resize"
          onMouseDown={(e) => handleMouseDown(e, 'ne')}
        />
        <div
          className="absolute -bottom-1 -left-1 w-3 h-3 bg-white border border-gray-400 cursor-sw-resize"
          onMouseDown={(e) => handleMouseDown(e, 'sw')}
        />
        <div
          className="absolute -bottom-1 -right-1 w-3 h-3 bg-white border border-gray-400 cursor-se-resize"
          onMouseDown={(e) => handleMouseDown(e, 'se')}
        />

        {/* Edge handles */}
        <div
          className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-white border border-gray-400 cursor-n-resize"
          onMouseDown={(e) => handleMouseDown(e, 'n')}
        />
        <div
          className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-white border border-gray-400 cursor-s-resize"
          onMouseDown={(e) => handleMouseDown(e, 's')}
        />
        <div
          className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white border border-gray-400 cursor-w-resize"
          onMouseDown={(e) => handleMouseDown(e, 'w')}
        />
        <div
          className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white border border-gray-400 cursor-e-resize"
          onMouseDown={(e) => handleMouseDown(e, 'e')}
        />
      </div>
    </div>
  );
};

export default CropControls;
