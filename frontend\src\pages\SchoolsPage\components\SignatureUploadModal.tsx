import React, { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react";
import { School } from "../types";

interface SignatureUploadModalProps {
  isVisible: boolean;
  school: School | null;
  onClose: () => void;
  onSuccess: () => void;
}

const SignatureUploadModal: React.FC<SignatureUploadModalProps> = ({
  isVisible,
  school,
  onClose,
  onSuccess,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const API_BASE_URL =
    process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isVisible) {
      resetForm();
    }
  }, [isVisible, school]);

  const resetForm = () => {
    setSelectedFile(null);
    setPreview(null);
    setError(null);
    setDragActive(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleFileSelect = (file: File) => {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      setError("Please select an image file (PNG, JPG, GIF)");
      return;
    }

    // Validate file size (100MB limit)
    if (file.size > 100 * 1024 * 1024) {
      setError("File size must be less than 100MB");
      return;
    }

    setSelectedFile(file);
    setError(null);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !school) return;

    setUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("signature", selectedFile);

      console.log("Uploading signature for school:", school.id);
      console.log("File details:", {
        name: selectedFile.name,
        size: selectedFile.size,
        type: selectedFile.type,
      });

      const response = await fetch(
        `${API_BASE_URL}/api/school/${school.id}/signature`,
        {
          method: "POST",
          body: formData,
          credentials: "include", // Include credentials for CORS
        }
      );

      console.log("Response status:", response.status);
      console.log(
        "Response headers:",
        Object.fromEntries(response.headers.entries())
      );

      if (!response.ok) {
        let errorMessage = "Failed to upload signature";
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          console.error("Failed to parse error response:", parseError);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log("Upload successful:", result);

      onSuccess();
      onClose();
      resetForm();
    } catch (error: any) {
      console.error("Upload error:", error);
      setError(error.message || "Failed to upload signature");
    } finally {
      setUploading(false);
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Icon
                icon="mdi:signature-freehand"
                className="h-5 w-5 text-white"
              />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Upload Principal Signature
              </h2>
              <p className="text-sm text-gray-500">{school?.name}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <Icon icon="mdi:close" className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Current Signature Display */}
          {school?.signature_path && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                Current Signature
              </h3>
              <div className="w-full h-24 bg-white rounded border-2 border-gray-200 flex items-center justify-center overflow-hidden">
                <img
                  src={`${API_BASE_URL}/api/cards/logos/${school.signature_path}`}
                  alt="Current signature"
                  className="max-w-full max-h-full object-contain"
                />
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Icon
                  icon="mdi:alert-circle"
                  className="h-5 w-5 text-red-500"
                />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            </div>
          )}

          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragActive
                ? "border-purple-400 bg-purple-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {preview ? (
              <div className="space-y-4">
                <div className="mx-auto w-48 h-24 rounded-lg overflow-hidden border-2 border-gray-200 bg-white flex items-center justify-center">
                  <img
                    src={preview}
                    alt="Signature preview"
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {selectedFile?.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {selectedFile &&
                      (selectedFile.size / 1024 / 1024).toFixed(2)}{" "}
                    MB
                  </p>
                </div>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="text-purple-600 hover:text-purple-800 text-sm font-medium"
                >
                  Choose Different Signature
                </button>
              </div>
            ) : (
              <div>
                <div className="mx-auto w-12 h-12 text-gray-400 mb-4">
                  <Icon
                    icon="mdi:signature-freehand"
                    className="w-full h-full"
                  />
                </div>
                <label htmlFor="signature-upload" className="cursor-pointer">
                  <span className="mt-2 block text-sm font-medium text-gray-900">
                    Drop signature image here or click to browse
                  </span>
                  <input
                    ref={fileInputRef}
                    id="signature-upload"
                    name="signature-upload"
                    type="file"
                    accept="image/*"
                    className="sr-only"
                    onChange={handleFileInputChange}
                  />
                </label>
                <p className="mt-1 text-xs text-gray-500">
                  PNG, JPG, GIF up to 100MB
                </p>
                <p className="mt-2 text-xs text-gray-400">
                  Recommended: Transparent background, 300x100px
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleUpload}
            disabled={!selectedFile || uploading}
            className="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            {uploading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Uploading...</span>
              </>
            ) : (
              <>
                <Icon icon="mdi:upload" className="h-4 w-4" />
                <span>Upload Signature</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SignatureUploadModal;
