const sequelize = require('./src/config/database');

async function checkSchema() {
  try {
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Check all tables
    const [tables] = await sequelize.query("SELECT name FROM sqlite_master WHERE type='table'");
    console.log('\nTables in database:');
    tables.forEach(table => console.log(`  - ${table.name}`));

    // Check persons table schema
    console.log('\n=== Persons Table Schema ===');
    const [personColumns] = await sequelize.query("PRAGMA table_info(persons)");
    personColumns.forEach(col => {
      console.log(`  - ${col.name}: ${col.type} (nullable: ${col.notnull === 0}, default: ${col.dflt_value})`);
    });

    // Check schools table schema
    console.log('\n=== Schools Table Schema ===');
    const [schoolColumns] = await sequelize.query("PRAGMA table_info(schools)");
    schoolColumns.forEach(col => {
      console.log(`  - ${col.name}: ${col.type} (nullable: ${col.notnull === 0}, default: ${col.dflt_value})`);
    });

    // Check csv_batches table schema
    console.log('\n=== CSV Batches Table Schema ===');
    const [batchColumns] = await sequelize.query("PRAGMA table_info(csv_batches)");
    batchColumns.forEach(col => {
      console.log(`  - ${col.name}: ${col.type} (nullable: ${col.notnull === 0}, default: ${col.dflt_value})`);
    });

  } catch (error) {
    console.error('Schema check failed:', error);
  } finally {
    await sequelize.close();
  }
}

checkSchema();
