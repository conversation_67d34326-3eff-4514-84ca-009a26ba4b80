/**
 * Frontend caching service with TTL support and pattern-based invalidation
 */
export class ApiCache {
  private cache: Map<string, CacheEntry>;
  private maxSize: number;
  private defaultTTL: number;
  private cleanupInterval: NodeJS.Timeout;

  constructor(maxSize: number = 1000, defaultTTL: number = 300) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL; // 5 minutes default

    // Cleanup expired entries every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000);
  }

  /**
   * Get value from cache
   */
  get<T = any>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    // Update access time for LRU
    entry.accessedAt = Date.now();
    return entry.data as T;
  }

  /**
   * Set value in cache
   */
  set<T = any>(key: string, data: T, ttl?: number): void {
    const expiresAt = Date.now() + (ttl || this.defaultTTL) * 1000;
    const now = Date.now();

    // If cache is full, remove least recently used item
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    this.cache.set(key, {
      data,
      expiresAt,
      createdAt: now,
      accessedAt: now,
      size: this.estimateSize(data),
    });
  }

  /**
   * Delete specific key from cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Invalidate cache entries matching a pattern
   */
  invalidatePattern(pattern: string): number {
    let count = 0;
    const regex = new RegExp(pattern.replace(/\*/g, ".*"));

    // Convert keys to array to avoid iteration issues
    const keys = Array.from(this.cache.keys());
    for (const key of keys) {
      if (regex.test(key)) {
        this.cache.delete(key);
        count++;
      }
    }

    return count;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const now = Date.now();
    let totalSize = 0;
    let expiredCount = 0;

    for (const entry of this.cache.values()) {
      totalSize += entry.size;
      if (now > entry.expiresAt) {
        expiredCount++;
      }
    }

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      totalSize,
      expiredCount,
      hitRate: this.calculateHitRate(),
      memoryUsage: this.getMemoryUsage(),
    };
  }

  /**
   * Get all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get cache entries that match a pattern
   */
  getByPattern<T = any>(
    pattern: string
  ): Array<{ key: string; data: T; entry: CacheEntry }> {
    const regex = new RegExp(pattern.replace(/\*/g, ".*"));
    const results: Array<{ key: string; data: T; entry: CacheEntry }> = [];

    for (const [key, entry] of this.cache.entries()) {
      if (regex.test(key) && Date.now() <= entry.expiresAt) {
        results.push({ key, data: entry.data as T, entry });
      }
    }

    return results;
  }

  /**
   * Preload cache with data
   */
  preload<T = any>(
    entries: Array<{ key: string; data: T; ttl?: number }>
  ): void {
    entries.forEach(({ key, data, ttl }) => {
      this.set(key, data, ttl);
    });
  }

  /**
   * Export cache data for persistence
   */
  export(): CacheExport {
    const entries: CacheExportEntry[] = [];
    const now = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      // Only export non-expired entries
      if (now <= entry.expiresAt) {
        entries.push({
          key,
          data: entry.data,
          expiresAt: entry.expiresAt,
          createdAt: entry.createdAt,
        });
      }
    }

    return {
      entries,
      exportedAt: now,
      version: "1.0",
    };
  }

  /**
   * Import cache data from persistence
   */
  import(cacheExport: CacheExport): number {
    let importedCount = 0;
    const now = Date.now();

    cacheExport.entries.forEach(({ key, data, expiresAt, createdAt }) => {
      // Only import non-expired entries
      if (now <= expiresAt) {
        this.cache.set(key, {
          data,
          expiresAt,
          createdAt,
          accessedAt: now,
          size: this.estimateSize(data),
        });
        importedCount++;
      }
    });

    return importedCount;
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach((key) => this.cache.delete(key));

    if (keysToDelete.length > 0) {
      console.debug(
        `Cache cleanup: removed ${keysToDelete.length} expired entries`
      );
    }
  }

  /**
   * Evict least recently used item
   */
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessedAt < oldestTime) {
        oldestTime = entry.accessedAt;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Estimate size of data in bytes
   */
  private estimateSize(data: any): number {
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch {
      return JSON.stringify(data).length * 2; // Rough estimate
    }
  }

  /**
   * Calculate hit rate (simplified - would need request tracking for accuracy)
   */
  private calculateHitRate(): number {
    // This is a simplified calculation
    // In a real implementation, you'd track hits and misses
    return 0;
  }

  /**
   * Get memory usage estimate
   */
  private getMemoryUsage(): number {
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.size;
    }
    return totalSize;
  }

  /**
   * Destroy cache and cleanup
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.cache.clear();
  }
}

// Types
interface CacheEntry {
  data: any;
  expiresAt: number;
  createdAt: number;
  accessedAt: number;
  size: number;
}

interface CacheStats {
  size: number;
  maxSize: number;
  totalSize: number;
  expiredCount: number;
  hitRate: number;
  memoryUsage: number;
}

interface CacheExportEntry {
  key: string;
  data: any;
  expiresAt: number;
  createdAt: number;
}

interface CacheExport {
  entries: CacheExportEntry[];
  exportedAt: number;
  version: string;
}

// Utility functions for cache management
export const cacheUtils = {
  /**
   * Create cache key from URL and params
   */
  createKey(url: string, params?: Record<string, any>): string {
    const paramString = params ? JSON.stringify(params) : "";
    return `${url}:${paramString}`;
  },

  /**
   * Create pattern for cache invalidation
   */
  createPattern(baseUrl: string): string {
    return `${baseUrl}*`;
  },

  /**
   * Serialize data for caching
   */
  serialize(data: any): string {
    return JSON.stringify(data);
  },

  /**
   * Deserialize cached data
   */
  deserialize<T = any>(data: string): T {
    return JSON.parse(data);
  },
};
