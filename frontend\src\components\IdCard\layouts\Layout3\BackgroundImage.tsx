import IdentityBgGreen from "../../../../assets/images/shapes/identityBgGreen.svg";
import IdentityBgBlue from "../../../../assets/images/shapes/identityBgBlue.svg";
import IdentityBgOrange from "../../../../assets/images/shapes/identityBgOrange.svg";
import { BgColorType } from "./types";

interface BackgroundImageProps {
  bgColor: BgColorType;
}

const BackgroundImage = ({ bgColor }: BackgroundImageProps) => {
  const backgroundImages = {
    green: IdentityBgGreen,
    blue: IdentityBgBlue,
    orange: IdentityBgOrange,
  };

  return (
    <img
      src={backgroundImages[bgColor]}
      alt="Background design"
      className="object-cover w-full h-full"
    />
  );
};

export default BackgroundImage;
