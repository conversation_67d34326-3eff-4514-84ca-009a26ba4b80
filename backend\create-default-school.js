const { School } = require('./src/models');
const sequelize = require('./src/config/database');

async function createDefaultSchool() {
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('Database connection established.');

    // Check if any schools exist
    const existingSchool = await School.findOne();
    
    if (existingSchool) {
      console.log('School already exists:', existingSchool.name);
      console.log('School ID:', existingSchool.id);
      return;
    }

    // Create default school
    const defaultSchool = await School.create({
      name: 'BIRTA GLOBAL SCHOOL',
      address: 'Aduwa Bridge, Atthi Sadan Road, Birtamode',
      phone: '+977-9852672234',
      email: '<EMAIL>'
    });

    console.log('Default school created successfully!');
    console.log('School ID:', defaultSchool.id);
    console.log('School Name:', defaultSchool.name);
    
  } catch (error) {
    console.error('Error creating default school:', error);
  } finally {
    await sequelize.close();
  }
}

createDefaultSchool();
