/**
 * Request queue service for handling duplicate requests and request deduplication
 */
export class RequestQueue {
  private pendingRequests: Map<string, Promise<any>>;
  private requestCounts: Map<string, number>;
  private maxRetries: number;
  private retryDelay: number;

  constructor(maxRetries: number = 3, retryDelay: number = 1000) {
    this.pendingRequests = new Map();
    this.requestCounts = new Map();
    this.maxRetries = maxRetries;
    this.retryDelay = retryDelay;
  }

  /**
   * Add request to queue with deduplication
   */
  async add<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // If request is already pending, return the existing promise
    if (this.pendingRequests.has(key)) {
      console.debug(`Request deduplication: ${key}`);
      return this.pendingRequests.get(key);
    }

    // Create new request promise
    const requestPromise = this.executeWithRetry(key, requestFn);

    // Store pending request
    this.pendingRequests.set(key, requestPromise);

    try {
      const result = await requestPromise;
      return result;
    } finally {
      // Clean up after request completes
      this.pendingRequests.delete(key);
      this.requestCounts.delete(key);
    }
  }

  /**
   * Execute request with retry logic
   */
  private async executeWithRetry<T>(
    key: string,
    requestFn: () => Promise<T>
  ): Promise<T> {
    let lastError: Error;
    const retryCount = this.requestCounts.get(key) || 0;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        this.requestCounts.set(key, attempt);
        const result = await requestFn();
        return result;
      } catch (error) {
        lastError = error as Error;

        // Don't retry on client errors (4xx)
        if (this.isClientError(error)) {
          throw error;
        }

        // Don't retry if we've reached max attempts
        if (attempt === this.maxRetries) {
          break;
        }

        // Wait before retry with exponential backoff
        const delay = this.retryDelay * Math.pow(2, attempt);
        console.warn(
          `Request failed, retrying in ${delay}ms (attempt ${attempt + 1}/${
            this.maxRetries + 1
          }):`,
          key
        );
        await this.delay(delay);
      }
    }

    throw lastError!;
  }

  /**
   * Check if error is a client error (4xx)
   */
  private isClientError(error: any): boolean {
    return (
      error.response &&
      error.response.status >= 400 &&
      error.response.status < 500
    );
  }

  /**
   * Delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Cancel pending request
   */
  cancel(key: string): boolean {
    if (this.pendingRequests.has(key)) {
      this.pendingRequests.delete(key);
      this.requestCounts.delete(key);
      return true;
    }
    return false;
  }

  /**
   * Cancel all pending requests
   */
  cancelAll(): void {
    this.pendingRequests.clear();
    this.requestCounts.clear();
  }

  /**
   * Get pending request keys
   */
  getPendingKeys(): string[] {
    return Array.from(this.pendingRequests.keys());
  }

  /**
   * Check if request is pending
   */
  isPending(key: string): boolean {
    return this.pendingRequests.has(key);
  }

  /**
   * Get queue statistics
   */
  getStats(): QueueStats {
    return {
      pendingCount: this.pendingRequests.size,
      pendingKeys: this.getPendingKeys(),
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay,
    };
  }
}

/**
 * Debounced request manager for search and input handling
 */
export class DebouncedRequestManager {
  private debouncedFunctions: Map<string, (...args: any[]) => void>;
  private pendingTimeouts: Map<string, NodeJS.Timeout>;
  private defaultDelay: number;

  constructor(defaultDelay: number = 300) {
    this.debouncedFunctions = new Map();
    this.pendingTimeouts = new Map();
    this.defaultDelay = defaultDelay;
  }

  /**
   * Create or get debounced function
   */
  debounce<T extends (...args: any[]) => any>(
    key: string,
    fn: T,
    delay?: number
  ): (...args: Parameters<T>) => void {
    const actualDelay = delay || this.defaultDelay;

    if (this.debouncedFunctions.has(key)) {
      return this.debouncedFunctions.get(key)!;
    }

    const debouncedFn = (...args: any[]) => {
      // Clear existing timeout
      const existingTimeout = this.pendingTimeouts.get(key);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      // Set new timeout
      const timeout = setTimeout(() => {
        fn(...args);
        this.pendingTimeouts.delete(key);
      }, actualDelay);

      this.pendingTimeouts.set(key, timeout);
    };

    this.debouncedFunctions.set(key, debouncedFn);
    return debouncedFn;
  }

  /**
   * Cancel debounced function
   */
  cancel(key: string): boolean {
    const timeout = this.pendingTimeouts.get(key);
    if (timeout) {
      clearTimeout(timeout);
      this.pendingTimeouts.delete(key);
      return true;
    }
    return false;
  }

  /**
   * Cancel all debounced functions
   */
  cancelAll(): void {
    for (const timeout of this.pendingTimeouts.values()) {
      clearTimeout(timeout);
    }
    this.pendingTimeouts.clear();
  }

  /**
   * Check if function is pending
   */
  isPending(key: string): boolean {
    return this.pendingTimeouts.has(key);
  }

  /**
   * Get pending function keys
   */
  getPendingKeys(): string[] {
    return Array.from(this.pendingTimeouts.keys());
  }

  /**
   * Cleanup
   */
  destroy(): void {
    this.cancelAll();
    this.debouncedFunctions.clear();
  }
}

/**
 * Batch request manager for combining multiple requests
 */
export class BatchRequestManager {
  private batches: Map<string, BatchInfo>;
  private batchTimeout: number;
  private maxBatchSize: number;

  constructor(batchTimeout: number = 100, maxBatchSize: number = 10) {
    this.batches = new Map();
    this.batchTimeout = batchTimeout;
    this.maxBatchSize = maxBatchSize;
  }

  /**
   * Add request to batch
   */
  add<T>(
    batchKey: string,
    requestData: any,
    resolver: (data: any[]) => Promise<T[]>
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      let batch = this.batches.get(batchKey);

      if (!batch) {
        batch = {
          requests: [],
          resolver,
          timeout: null,
        };
        this.batches.set(batchKey, batch);
      }

      // Add request to batch
      batch.requests.push({
        data: requestData,
        resolve,
        reject,
      });

      // Execute batch if it reaches max size
      if (batch.requests.length >= this.maxBatchSize) {
        this.executeBatch(batchKey);
        return;
      }

      // Set timeout for batch execution
      if (batch.timeout) {
        clearTimeout(batch.timeout);
      }

      batch.timeout = setTimeout(() => {
        this.executeBatch(batchKey);
      }, this.batchTimeout);
    });
  }

  /**
   * Execute batch request
   */
  private async executeBatch(batchKey: string): Promise<void> {
    const batch = this.batches.get(batchKey);
    if (!batch || batch.requests.length === 0) {
      return;
    }

    // Clear timeout
    if (batch.timeout) {
      clearTimeout(batch.timeout);
    }

    // Remove batch from map
    this.batches.delete(batchKey);

    try {
      // Extract request data
      const requestData = batch.requests.map((req) => req.data);

      // Execute batch resolver
      const results = await batch.resolver(requestData);

      // Resolve individual requests
      batch.requests.forEach((request, index) => {
        if (results[index] !== undefined) {
          request.resolve(results[index]);
        } else {
          request.reject(
            new Error("Batch request failed: no result for request")
          );
        }
      });
    } catch (error) {
      // Reject all requests in batch
      batch.requests.forEach((request) => {
        request.reject(error);
      });
    }
  }

  /**
   * Get batch statistics
   */
  getStats(): BatchStats {
    const stats: BatchStats = {
      activeBatches: this.batches.size,
      totalPendingRequests: 0,
      batchDetails: [],
    };

    for (const [key, batch] of this.batches.entries()) {
      stats.totalPendingRequests += batch.requests.length;
      stats.batchDetails.push({
        key,
        requestCount: batch.requests.length,
        hasTimeout: !!batch.timeout,
      });
    }

    return stats;
  }

  /**
   * Clear all batches
   */
  clear(): void {
    for (const batch of this.batches.values()) {
      if (batch.timeout) {
        clearTimeout(batch.timeout);
      }
      // Reject pending requests
      batch.requests.forEach((request) => {
        request.reject(new Error("Batch cleared"));
      });
    }
    this.batches.clear();
  }
}

// Types
interface QueueStats {
  pendingCount: number;
  pendingKeys: string[];
  maxRetries: number;
  retryDelay: number;
}

interface BatchInfo {
  requests: Array<{
    data: any;
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }>;
  resolver: (data: any[]) => Promise<any[]>;
  timeout: NodeJS.Timeout | null;
}

interface BatchStats {
  activeBatches: number;
  totalPendingRequests: number;
  batchDetails: Array<{
    key: string;
    requestCount: number;
    hasTimeout: boolean;
  }>;
}
