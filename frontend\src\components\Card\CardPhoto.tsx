import React, { useState } from 'react';
import { CardPhotoProps } from './types';
import ProfileImage from '../common/ProfileImage';

const CardPhoto: React.FC<CardPhotoProps> = ({
  person,
  photoBase64,
  className = '',
  onError,
  onLoad
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
    onError?.();
  };

  const handleImageLoad = () => {
    setImageLoading(false);
    onLoad?.();
  };

  if (!photoBase64 || imageError) {
    return (
      <div className={`photo-placeholder ${className}`}>
        <ProfileImage
          src=""
          name={person.name}
          designation={person.type}
          className="w-full h-full"
          shape="rounded"
          showInitials={true}
        />
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {imageLoading && (
        <div className="photo-placeholder absolute inset-0 flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-current"></div>
        </div>
      )}
      <img
        src={photoBase64}
        alt={person.name}
        className={`card-photo ${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onError={handleImageError}
        onLoad={handleImageLoad}
      />
    </div>
  );
};

export default CardPhoto;
