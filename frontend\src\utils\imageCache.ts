/**
 * Image cache utility for better image loading performance
 */

interface CacheEntry {
  url: string;
  loaded: boolean;
  error: boolean;
  timestamp: number;
  image?: HTMLImageElement;
}

class ImageCache {
  private cache = new Map<string, CacheEntry>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 100;

  /**
   * Preload an image and cache the result
   */
  async preloadImage(url: string): Promise<boolean> {
    if (!url || url.trim() === "") {
      return false;
    }

    // Check if already cached and not expired
    const cached = this.cache.get(url);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.loaded && !cached.error;
    }

    return new Promise((resolve) => {
      const img = new Image();
      const startTime = Date.now();

      img.onload = () => {
        this.setCacheEntry(url, {
          url,
          loaded: true,
          error: false,
          timestamp: Date.now(),
          image: img,
        });
        console.log(`Image cached successfully: ${url} (${Date.now() - startTime}ms)`);
        resolve(true);
      };

      img.onerror = () => {
        this.setCacheEntry(url, {
          url,
          loaded: false,
          error: true,
          timestamp: Date.now(),
        });
        console.warn(`Image failed to cache: ${url} (${Date.now() - startTime}ms)`);
        resolve(false);
      };

      // Set crossOrigin for CORS handling
      img.crossOrigin = "anonymous";
      img.src = url;

      // Timeout after 10 seconds
      setTimeout(() => {
        this.setCacheEntry(url, {
          url,
          loaded: false,
          error: true,
          timestamp: Date.now(),
        });
        console.warn(`Image cache timeout: ${url}`);
        resolve(false);
      }, 10000);
    });
  }

  /**
   * Check if an image is cached and loaded
   */
  isImageCached(url: string): boolean {
    const cached = this.cache.get(url);
    if (!cached || Date.now() - cached.timestamp > this.CACHE_DURATION) {
      return false;
    }
    return cached.loaded && !cached.error;
  }

  /**
   * Get cached image status
   */
  getCacheStatus(url: string): { loaded: boolean; error: boolean; cached: boolean } {
    const cached = this.cache.get(url);
    if (!cached || Date.now() - cached.timestamp > this.CACHE_DURATION) {
      return { loaded: false, error: false, cached: false };
    }
    return { loaded: cached.loaded, error: cached.error, cached: true };
  }

  /**
   * Preload multiple images
   */
  async preloadImages(urls: string[]): Promise<{ loaded: string[]; failed: string[] }> {
    const results = await Promise.allSettled(
      urls.map(url => this.preloadImage(url))
    );

    const loaded: string[] = [];
    const failed: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        loaded.push(urls[index]);
      } else {
        failed.push(urls[index]);
      }
    });

    return { loaded, failed };
  }

  /**
   * Clear expired cache entries
   */
  clearExpired(): void {
    const now = Date.now();
    for (const [url, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.CACHE_DURATION) {
        this.cache.delete(url);
      }
    }
  }

  /**
   * Clear all cache
   */
  clearAll(): void {
    this.cache.clear();
  }

  /**
   * Get cache size
   */
  getCacheSize(): number {
    return this.cache.size;
  }

  /**
   * Set cache entry with size limit
   */
  private setCacheEntry(url: string, entry: CacheEntry): void {
    // Clear expired entries if cache is getting full
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.clearExpired();
    }

    // If still full, remove oldest entries
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      // Remove oldest 20% of entries
      const toRemove = Math.floor(this.MAX_CACHE_SIZE * 0.2);
      for (let i = 0; i < toRemove && i < entries.length; i++) {
        this.cache.delete(entries[i][0]);
      }
    }

    this.cache.set(url, entry);
  }
}

// Create a singleton instance
export const imageCache = new ImageCache();

// Auto-cleanup every 5 minutes
setInterval(() => {
  imageCache.clearExpired();
}, 5 * 60 * 1000);

export default imageCache;
