import { IdCardProps, StudentData } from "./types";
import Card<PERSON>eader from "./CardHeader";
import StudentInfo from "./StudentInfo";
import CardFooter from "./CardFooter";

const IdentityCard = ({ data, id }: { data: IdCardProps; id?: number }) => {
  const studentData: StudentData = {
    studentName: data.studentName,
    studentclass: data.studentclass,
    roll: data.roll,
    address: data.address,
    phone: data.phone,
    personType: data.personType,
    parentName: data.parentName,
    department: data.department,
    fieldVisibilityConfig: data.fieldVisibilityConfig,
  };

  return (
    <div
      className="h-[324px] w-[204px] bg-white rounded-sm overflow-hidden shadow-lg flex flex-col"
      data-card-id={id}
    >
      <CardHeader
        logo={data.logo}
        motto={data.motto}
        schoolName={data.schoolName}
        subname={data.subname}
        contact={data.contact}
      />

      <StudentInfo image={data.image} data={studentData} />

      <CardFooter
        validity={data.validity}
        principalSignature={data.principalSignature}
      />
    </div>
  );
};

export default IdentityCard;
