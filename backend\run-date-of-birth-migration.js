const path = require("path");

// Ensure we're working from the correct directory
const appRoot = process.cwd();
console.log("Working directory:", appRoot);

const sequelize = require(path.join(appRoot, "src/config/database"));
const migration = require(path.join(
  appRoot,
  "src/migrations/add-date-of-birth-to-persons"
));

async function runMigration() {
  try {
    console.log("Starting migration: add-date-of-birth-to-persons");

    // Connect to database
    await sequelize.authenticate();
    console.log("Database connection established successfully.");

    // Check if the column already exists
    const tableDescription = await sequelize
      .getQueryInterface()
      .describeTable("persons");

    if (tableDescription.date_of_birth) {
      console.log("date_of_birth column already exists, skipping migration");
      await sequelize.close();
      process.exit(0);
    }

    // Run the migration
    console.log("Running add-date-of-birth-to-persons migration...");
    await migration.up(sequelize.getQueryInterface(), sequelize);

    console.log("Migration completed successfully");

    // Verify the migration
    const updatedTableDescription = await sequelize
      .getQueryInterface()
      .describeTable("persons");
    if (updatedTableDescription.date_of_birth) {
      console.log("✓ date_of_birth column verified in persons table");
    } else {
      console.log("✗ date_of_birth column not found after migration");
    }

    // Close database connection
    await sequelize.close();
    process.exit(0);
  } catch (error) {
    console.error("Migration failed:", error);

    // Try to rollback if needed
    try {
      console.log("Attempting rollback...");
      await migration.down(sequelize.getQueryInterface(), sequelize);
      console.log("Rollback completed");
    } catch (rollbackError) {
      console.error("Rollback failed:", rollbackError);
    }

    await sequelize.close();
    process.exit(1);
  }
}

runMigration();
