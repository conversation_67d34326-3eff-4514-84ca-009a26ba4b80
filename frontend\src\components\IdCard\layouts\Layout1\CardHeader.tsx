import { HeaderProps } from "./types";
import { renderSchoolNameForHeader } from "../../../../utils/layout1DataMapping";

const CardHeader = ({
  logo,
  motto,
  schoolName,
  subname,
  contact,
}: HeaderProps) => {
  // Get optimal formatting for school name
  const { lines, className } = renderSchoolNameForHeader(schoolName);

  return (
    <>
      {/* Header Section */}
      <div className="bg-[#2d3091] px-1.5 py-1.5 text-white relative flex-shrink-0">
        <div className="flex items-center gap-1">
          <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center flex-shrink-0">
            <div className="relative w-full h-full">
              <img
                src={logo}
                alt="Logo"
                className="absolute inset-0 object-cover rounded-full w-full h-full overflow-hidden"
              />
            </div>
          </div>

          <div className="flex-1 min-w-0 overflow-hidden">
            <div className="text-[7px] italic mb-0.5 text-center">{motto}</div>

            {/* Smart school name formatting - handles long names */}
            <div className={className}>
              {lines.length === 1 ? (
                <span>{lines[0]}</span>
              ) : (
                lines.map((line, index) => <div key={index}>{line}</div>)
              )}
            </div>

            {/* Address and Contact on same line separated by "|" */}
            <div className="text-[10px] text-center font-semibold -mt-0.5 break-words leading-tight">
              <span>{subname}</span>
              {subname && contact && <span className="mx-1">|</span>}
              <span className="text-[#eee231] text-[7px]">{contact}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="h-0.5 bg-[#00a54f] flex-shrink-0"></div>

      <div className="bg-[#2d3091] w-fit mx-auto px-2.5 rounded-b-md text-white text-center py-0.5 flex-shrink-0">
        <div className="text-[9px] font-bold">ID CARD</div>
      </div>
    </>
  );
};

export default CardHeader;
