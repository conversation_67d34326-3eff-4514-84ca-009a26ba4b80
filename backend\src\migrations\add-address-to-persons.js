const { DataTypes } = require('sequelize');

/**
 * Migration to add address column to persons table
 * This column allows individual persons to have their own addresses
 * instead of using the school's address
 */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Check if address column already exists
      const tableDescription = await queryInterface.describeTable('persons');
      if (tableDescription.address) {
        console.log('address column already exists, skipping migration');
        await transaction.commit();
        return;
      }

      // Add address column
      await queryInterface.addColumn('persons', 'address', {
        type: DataTypes.TEXT,
        allowNull: true,
      }, { transaction });

      await transaction.commit();
      console.log('Successfully added address column to persons table');
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      await queryInterface.removeColumn('persons', 'address', { transaction });
      await transaction.commit();
      console.log('Successfully removed address column from persons table');
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
