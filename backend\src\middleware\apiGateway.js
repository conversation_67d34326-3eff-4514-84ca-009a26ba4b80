const compression = require("compression");
const morgan = require("morgan");
const helmet = require("helmet");
const cors = require("cors");
const { createLogger } = require("../utils/logger");
const { cacheMiddleware } = require("./cache");
const { requestValidator } = require("./validation");

const logger = createLogger("api-gateway");

/**
 * Production-grade API Gateway middleware stack
 * Handles security, caching, compression, and logging
 */
class ApiGateway {
  constructor() {
    // Rate limiting removed
  }

  /**
   * Get security middleware stack
   */
  getSecurityMiddleware() {
    return [
      // Security headers
      helmet({
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
          },
        },
        crossOriginEmbedderPolicy: false,
      }),

      // CORS configuration
      cors({
        origin: (origin, callback) => {
          // Get allowed origins from environment variable or use defaults
          const corsOrigins = process.env.CORS_ORIGIN
            ? process.env.CORS_ORIGIN.split(",").map((origin) => origin.trim())
            : [];

          const allowedOrigins = [
            process.env.FRONTEND_URL || "http://localhost:3000",
            "https://print.webstudiomatrix.com", // Frontend production URL
            "https://print-api.webstudiomatrix.com", // API production URL
            "http://localhost:3000",
            "http://localhost:3001", // Add port 3001 for frontend
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001", // Add port 3001 for 127.0.0.1
            ...corsOrigins, // Add origins from environment variable
          ];

          // Allow requests with no origin (mobile apps, etc.)
          if (!origin) return callback(null, true);

          if (allowedOrigins.includes(origin)) {
            callback(null, true);
          } else {
            logger.warn(`CORS blocked origin: ${origin}`);
            callback(new Error("Not allowed by CORS"));
          }
        },
        credentials: true,
        methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allowedHeaders: [
          "Content-Type",
          "Authorization",
          "X-Requested-With",
          "X-Request-ID",
        ],
        exposedHeaders: ["X-Total-Count", "X-Page-Count"],
      }),

      // Compression
      compression({
        filter: (req, res) => {
          if (req.headers["x-no-compression"]) {
            return false;
          }
          return compression.filter(req, res);
        },
        threshold: 1024, // Only compress responses larger than 1KB
      }),
    ];
  }

  /**
   * Get logging middleware
   */
  getLoggingMiddleware() {
    return morgan("combined", {
      stream: {
        write: (message) => {
          logger.info(message.trim());
        },
      },
      skip: (req, res) => {
        // Skip logging for health checks and static files
        return req.path === "/health" || req.path.startsWith("/uploads");
      },
    });
  }

  /**
   * Request preprocessing middleware
   */
  getPreprocessingMiddleware() {
    return [
      // Request ID for tracing
      (req, res, next) => {
        req.requestId = `req_${Date.now()}_${Math.random()
          .toString(36)
          .substring(2, 11)}`;
        res.setHeader("X-Request-ID", req.requestId);
        next();
      },

      // Request timing
      (req, res, next) => {
        req.startTime = Date.now();
        const originalSend = res.send;

        res.send = function (data) {
          const duration = Date.now() - req.startTime;
          res.setHeader("X-Response-Time", `${duration}ms`);

          if (duration > 5000) {
            // Log slow requests
            logger.warn(`Slow request detected`, {
              requestId: req.requestId,
              method: req.method,
              path: req.path,
              duration: `${duration}ms`,
              ip: req.ip,
            });
          }

          return originalSend.call(this, data);
        };

        next();
      },

      // Request size validation
      (req, res, next) => {
        const maxSize = 200 * 1024 * 1024; // 200MB max request size
        if (
          req.headers["content-length"] &&
          parseInt(req.headers["content-length"]) > maxSize
        ) {
          return res.status(413).json({
            error: "Request entity too large",
            code: "REQUEST_TOO_LARGE",
            maxSize: "200MB",
          });
        }
        next();
      },
    ];
  }

  /**
   * Error handling middleware
   */
  getErrorHandlingMiddleware() {
    return (err, req, res, next) => {
      logger.error("API Gateway Error", {
        requestId: req.requestId,
        error: err.message,
        stack: err.stack,
        method: req.method,
        path: req.path,
        ip: req.ip,
      });

      // Don't leak error details in production
      const isDevelopment = process.env.NODE_ENV === "development";

      if (err.name === "ValidationError") {
        return res.status(400).json({
          error: "Validation failed",
          code: "VALIDATION_ERROR",
          details: isDevelopment ? err.details : undefined,
        });
      }

      if (err.name === "UnauthorizedError") {
        return res.status(401).json({
          error: "Unauthorized",
          code: "UNAUTHORIZED",
        });
      }

      // Default error response
      res.status(err.status || 500).json({
        error: isDevelopment ? err.message : "Internal server error",
        code: err.code || "INTERNAL_ERROR",
        requestId: req.requestId,
      });
    };
  }

  /**
   * Health check middleware
   */
  getHealthCheckMiddleware() {
    return (req, res, next) => {
      if (req.path === "/health") {
        return res.json({
          status: "OK",
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: process.env.npm_package_version || "1.0.0",
        });
      }
      next();
    };
  }
}

module.exports = new ApiGateway();
