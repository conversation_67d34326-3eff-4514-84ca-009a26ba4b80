const { body, param, query, validationResult } = require("express-validator");
const { createLogger } = require("../utils/logger");

const logger = createLogger("validation");

/**
 * Enhanced validation middleware with comprehensive rules
 */
class ValidationManager {
  constructor() {
    this.commonRules = this.setupCommonRules();
  }

  /**
   * Setup common validation rules
   */
  setupCommonRules() {
    return {
      // ID validation
      id: param("id")
        .isInt({ min: 1 })
        .withMessage("ID must be a positive integer"),

      // Pagination validation
      page: query("page")
        .optional()
        .isInt({ min: 1 })
        .withMessage("Page must be a positive integer")
        .toInt(),

      limit: query("limit")
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage("Limit must be between 1 and 100")
        .toInt(),

      // Search validation
      search: query("search")
        .optional()
        .isLength({ min: 1, max: 100 })
        .withMessage("Search term must be between 1 and 100 characters")
        .trim()
        .escape(),

      // School validation
      schoolId: query("school_id")
        .optional()
        .isInt({ min: 1 })
        .withMessage("School ID must be a positive integer")
        .toInt(),

      // Person type validation
      personType: query("type")
        .optional()
        .isIn(["student", "staff", "non_teaching"])
        .withMessage("Type must be student, staff, or non_teaching"),

      // Email validation
      email: body("email")
        .isEmail()
        .withMessage("Must be a valid email address")
        .normalizeEmail()
        .isLength({ max: 255 })
        .withMessage("Email must not exceed 255 characters"),

      // Password validation
      password: body("password")
        .isLength({ min: 8, max: 128 })
        .withMessage("Password must be between 8 and 128 characters")
        .matches(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
        )
        .withMessage(
          "Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character"
        ),

      // Name validation
      name: body("name")
        .isLength({ min: 1, max: 100 })
        .withMessage("Name must be between 1 and 100 characters")
        .trim()
        .matches(/^[a-zA-Z\s\-\.\']+$/)
        .withMessage(
          "Name can only contain letters, spaces, hyphens, dots, and apostrophes"
        ),

      // Phone validation
      phone: body("contact_no")
        .optional()
        .matches(/^[\+]?[1-9][\d]{0,15}$/)
        .withMessage(
          "Phone number must be valid (up to 16 digits, optional + prefix)"
        ),

      // File validation
      file: (fieldName, allowedTypes = [], maxSize = 100 * 1024 * 1024) => {
        return (req, res, next) => {
          if (!req.file && !req.files) {
            return next();
          }

          const file = req.file || (req.files && req.files[fieldName]);
          if (!file) {
            return next();
          }

          // Check file size
          if (file.size > maxSize) {
            return res.status(400).json({
              error: "File too large",
              code: "FILE_TOO_LARGE",
              maxSize: `${maxSize / (1024 * 1024)}MB`,
            });
          }

          // Check file type
          if (
            allowedTypes.length > 0 &&
            !allowedTypes.includes(file.mimetype)
          ) {
            return res.status(400).json({
              error: "Invalid file type",
              code: "INVALID_FILE_TYPE",
              allowedTypes,
            });
          }

          next();
        };
      },
    };
  }

  /**
   * Person validation rules
   */
  getPersonValidation() {
    return [
      body("name")
        .isLength({ min: 1, max: 100 })
        .withMessage("Name is required and must not exceed 100 characters")
        .trim(),

      body("type")
        .isIn(["student", "staff", "non_teaching"])
        .withMessage("Type must be student, staff, or non_teaching"),

      body("person_id")
        .optional()
        .isLength({ min: 1, max: 50 })
        .withMessage("Person ID must not exceed 50 characters if provided")
        .trim(),

      body("class")
        .optional()
        .isLength({ max: 20 })
        .withMessage("Class must not exceed 20 characters")
        .trim(),

      body("section")
        .optional()
        .isLength({ max: 10 })
        .withMessage("Section must not exceed 10 characters")
        .trim(),

      body("roll_number")
        .optional()
        .isLength({ max: 20 })
        .withMessage("Roll number must not exceed 20 characters")
        .trim(),

      body("parents_name")
        .optional()
        .isLength({ max: 100 })
        .withMessage("Parent's name must not exceed 100 characters")
        .trim(),

      body("contact_no")
        .optional()
        .matches(/^[\+]?[1-9][\d]{0,15}$/)
        .withMessage("Contact number must be valid"),

      body("department")
        .optional()
        .isLength({ max: 50 })
        .withMessage("Department must not exceed 50 characters")
        .trim(),

      body("designation")
        .optional()
        .isLength({ max: 50 })
        .withMessage("Designation must not exceed 50 characters")
        .trim(),

      body("school_id")
        .optional()
        .isInt({ min: 1 })
        .withMessage("School ID must be a positive integer"),
    ];
  }

  /**
   * School validation rules
   */
  getSchoolValidation() {
    return [
      body("name")
        .isLength({ min: 1, max: 100 })
        .withMessage(
          "School name is required and must not exceed 100 characters"
        )
        .trim(),

      body("address")
        .isLength({ min: 1, max: 500 })
        .withMessage("Address is required and must not exceed 500 characters")
        .trim(),

      body("phone")
        .matches(/^[\+]?[1-9][\d]{0,15}$/)
        .withMessage("Phone number must be valid"),

      body("email")
        .optional()
        .isEmail()
        .withMessage("Must be a valid email address")
        .normalizeEmail()
        .isLength({ max: 255 })
        .withMessage("Email must not exceed 255 characters"),
    ];
  }

  /**
   * Authentication validation rules
   */
  getAuthValidation() {
    return {
      login: [
        body("username")
          .isLength({ min: 3, max: 50 })
          .withMessage("Username must be between 3 and 50 characters")
          .trim(),

        body("password")
          .isLength({ min: 1 })
          .withMessage("Password is required"),
      ],

      register: [
        body("username")
          .isLength({ min: 3, max: 50 })
          .withMessage("Username must be between 3 and 50 characters")
          .trim()
          .matches(/^[a-zA-Z0-9_]+$/)
          .withMessage(
            "Username can only contain letters, numbers, and underscores"
          ),

        this.commonRules.email,
        this.commonRules.password,

        body("first_name")
          .optional()
          .isLength({ max: 50 })
          .withMessage("First name must not exceed 50 characters")
          .trim(),

        body("last_name")
          .optional()
          .isLength({ max: 50 })
          .withMessage("Last name must not exceed 50 characters")
          .trim(),

        body("role")
          .optional()
          .isIn(["admin", "user", "school_admin"])
          .withMessage("Role must be admin, user, or school_admin"),
      ],
    };
  }

  /**
   * Bulk operation validation
   */
  getBulkValidation() {
    return [
      body("personIds")
        .isArray({ min: 1, max: 100 })
        .withMessage("Person IDs must be an array with 1-100 items"),

      body("personIds.*")
        .isInt({ min: 1 })
        .withMessage("Each person ID must be a positive integer"),

      body("type")
        .optional()
        .isIn(["student", "staff", "non_teaching"])
        .withMessage("Type must be student, staff, or non_teaching"),
    ];
  }

  /**
   * CSV upload validation
   */
  getCsvValidation() {
    return [
      this.commonRules.file(
        "file",
        ["text/csv", "application/vnd.ms-excel"],
        100 * 1024 * 1024
      ), // 100MB max

      body("school_id")
        .optional()
        .isInt({ min: 1 })
        .withMessage("School ID must be a positive integer"),
    ];
  }
}

// Create singleton instance
const validationManager = new ValidationManager();

/**
 * Validation result handler middleware
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorDetails = errors.array().map((error) => ({
      field: error.param,
      message: error.msg,
      value: error.value,
      location: error.location,
    }));

    logger.warn("Validation failed", {
      requestId: req.requestId,
      path: req.path,
      method: req.method,
      errors: errorDetails,
      ip: req.ip,
    });

    return res.status(400).json({
      error: "Validation failed",
      code: "VALIDATION_ERROR",
      details: errorDetails,
    });
  }

  next();
};

/**
 * Request sanitization middleware
 */
const sanitizeRequest = (req, res, next) => {
  // Remove null bytes and control characters
  const sanitizeString = (str) => {
    if (typeof str !== "string") return str;
    return str.replace(/[\x00-\x1F\x7F]/g, "");
  };

  // Recursively sanitize object
  const sanitizeObject = (obj) => {
    if (obj === null || typeof obj !== "object") {
      return typeof obj === "string" ? sanitizeString(obj) : obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }

    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[sanitizeString(key)] = sanitizeObject(value);
    }
    return sanitized;
  };

  // Sanitize request data
  req.body = sanitizeObject(req.body);
  req.query = sanitizeObject(req.query);
  req.params = sanitizeObject(req.params);

  next();
};

module.exports = {
  validationManager,
  handleValidationErrors,
  sanitizeRequest,

  // Export validation rules
  personValidation: validationManager.getPersonValidation(),
  schoolValidation: validationManager.getSchoolValidation(),
  authValidation: validationManager.getAuthValidation(),
  bulkValidation: validationManager.getBulkValidation(),
  csvValidation: validationManager.getCsvValidation(),

  // Export common rules
  commonRules: validationManager.commonRules,
};
