import IdentityCard from "./IdentityCard";
import { IdCardProps, StudentData } from "./types";

const Layout3 = ({
  id,
  schoolName,
  subname,
  contact,
  logo,
  image,
  studentName,
  address,
  studentclass,
  phone,
  roll,
  bgColor,
  personType,
  department,
  fieldVisibilityConfig,
  validity,
  principalSignature,
}: IdCardProps) => {
  const data: StudentData = {
    companyLogo: logo,
    image: image,
    schoolName: schoolName,
    location: subname,
    contact: contact,
    studentName: studentName,
    class: studentclass || "",
    rollNo: roll,
    contactNo: phone,
    address: address,
    personType: personType,
    department: department,
    fieldVisibilityConfig: fieldVisibilityConfig,
    validity: validity,
    principalSignature: principalSignature,
  };

  return (
    <div data-card-id={id}>
      <IdentityCard bgColor={bgColor} data={data} />
    </div>
  );
};

export default Layout3;
