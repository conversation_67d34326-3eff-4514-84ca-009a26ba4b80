import { FooterProps } from "./types";

const CardFooter = ({ validity, principalSignature }: FooterProps) => {
  return (
    <div className="bg-[#2d3091] relative z-10 text-white flex py-2 flex-shrink-0">
      <div className="flex-1 px-2 flex items-center">
        <div className="text-[9px] font-medium whitespace-nowrap ">
          Validity Upto: {validity}
        </div>
      </div>

      <div
        className="w-24 absolute right-0 top-0 h-[110%] bg-[#00a54f] flex items-center  justify-center flex-col"
        style={{ clipPath: "polygon(0 0, 100% 0, 100% 100%, 20% 100%)" }}
      >
        <div className="relative flex flex-col items-center justify-center">
          {principalSignature ? (
            <img
              src={principalSignature}
              alt="Principal Signature"
              className="h-5 object-contain"
              onError={(e) => {
                console.error(
                  "Failed to load principal signature:",
                  principalSignature
                );
                e.currentTarget.style.display = "none";
              }}
            />
          ) : (
            <div className="h-3 flex items-center justify-center text-[6px] text-[#2d3091]">
              No Signature
            </div>
          )}
          <span className="w-8 border-t border-dotted -mt-0.5 border-[#2d3091]"></span>
        </div>
        <div className="text-[8px] mb-1 text-[#2d3091] font-medium text-center ">
          Principal
        </div>
      </div>
    </div>
  );
};

export default CardFooter;
