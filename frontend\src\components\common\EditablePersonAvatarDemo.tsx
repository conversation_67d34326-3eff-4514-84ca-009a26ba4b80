import React, { useState } from "react";
import EditablePersonAvatar from "./EditablePersonAvatar";
import { UploadProfileModal } from "../UploadProfileModal";
import { Person } from "../../services/personService";

/**
 * Demo component showcasing different EditablePersonAvatar configurations
 */
const EditablePersonAvatarDemo: React.FC = () => {
  const [selectedPerson, setSelectedPerson] = useState<Person | null>(null);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);

  // Sample persons for demo
  const samplePersons: Person[] = [
    {
      id: 1,
      person_id: "STU001",
      name: "<PERSON>",
      type: "student",
      photo_path: "processed_sample1.jpg", // Has photo
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: 2,
      person_id: "STF001",
      name: "<PERSON>",
      type: "staff",
      // No photo_path - will show initials
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: 3,
      person_id: "NT001",
      name: "<PERSON>",
      type: "non_teaching",
      photo_path: "processed_sample2.jpg", // Has photo
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ];

  const handleEditClick = (person: Person) => {
    console.log("Edit clicked for:", person.name);
    setSelectedPerson(person);
    setUploadModalOpen(true);
  };

  const handleAvatarClick = (person: Person) => {
    console.log("Avatar clicked for:", person.name);
  };

  const handleUploadSuccess = () => {
    console.log("Upload successful");
    setUploadModalOpen(false);
    setSelectedPerson(null);
  };

  const handleUploadClose = () => {
    setUploadModalOpen(false);
    setSelectedPerson(null);
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Editable Person Avatar Demo</h1>

      <div className="space-y-12">
        {/* Size Variations */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Size Variations</h2>
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <EditablePersonAvatar
                person={samplePersons[0]}
                size="sm"
                showEditIcon={true}
                onEditClick={handleEditClick}
              />
              <p className="text-sm mt-2">Small</p>
            </div>
            <div className="text-center">
              <EditablePersonAvatar
                person={samplePersons[0]}
                size="md"
                showEditIcon={true}
                onEditClick={handleEditClick}
              />
              <p className="text-sm mt-2">Medium</p>
            </div>
            <div className="text-center">
              <EditablePersonAvatar
                person={samplePersons[0]}
                size="lg"
                showEditIcon={true}
                onEditClick={handleEditClick}
              />
              <p className="text-sm mt-2">Large</p>
            </div>
            <div className="text-center">
              <EditablePersonAvatar
                person={samplePersons[0]}
                size="xl"
                showEditIcon={true}
                onEditClick={handleEditClick}
              />
              <p className="text-sm mt-2">Extra Large</p>
            </div>
          </div>
        </section>

        {/* Edit Icon Styles */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Edit Icon Styles</h2>
          <div className="grid grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-medium mb-4">
                Overlay Style (Hover to see)
              </h3>
              <div className="flex items-center space-x-4">
                {samplePersons.map((person) => (
                  <div key={person.id} className="text-center">
                    <EditablePersonAvatar
                      person={person}
                      size="lg"
                      showEditIcon={true}
                      onEditClick={handleEditClick}
                      editIconStyle="overlay"
                    />
                    <p className="text-sm mt-2">{person.name}</p>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">
                Badge Style (Always visible)
              </h3>
              <div className="flex items-center space-x-4">
                {samplePersons.map((person) => (
                  <div key={person.id} className="text-center">
                    <EditablePersonAvatar
                      person={person}
                      size="lg"
                      showEditIcon={true}
                      onEditClick={handleEditClick}
                      editIconStyle="badge"
                    />
                    <p className="text-sm mt-2">{person.name}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Person Types */}
        <section>
          <h2 className="text-xl font-semibold mb-4">
            Person Types (No Photos - Shows Initials)
          </h2>
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <EditablePersonAvatar
                person={{ ...samplePersons[0], photo_path: undefined }}
                size="lg"
                showEditIcon={true}
                onEditClick={handleEditClick}
                editIconStyle="badge"
              />
              <p className="text-sm mt-2">Student</p>
            </div>
            <div className="text-center">
              <EditablePersonAvatar
                person={{ ...samplePersons[1], photo_path: undefined }}
                size="lg"
                showEditIcon={true}
                onEditClick={handleEditClick}
                editIconStyle="badge"
              />
              <p className="text-sm mt-2">Staff</p>
            </div>
            <div className="text-center">
              <EditablePersonAvatar
                person={{ ...samplePersons[2], photo_path: undefined }}
                size="lg"
                showEditIcon={true}
                onEditClick={handleEditClick}
                editIconStyle="badge"
              />
              <p className="text-sm mt-2">Non-Teaching</p>
            </div>
          </div>
        </section>

        {/* Interactive Examples */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Interactive Examples</h2>
          <div className="bg-gray-50 p-6 rounded-lg">
            <p className="text-sm text-gray-600 mb-4">
              Click the edit icons to open the image editor modal. Click the
              avatars to see click events in console.
            </p>
            <div className="flex items-center space-x-6">
              {samplePersons.map((person) => (
                <div key={person.id} className="text-center">
                  <EditablePersonAvatar
                    person={person}
                    size="xl"
                    showEditIcon={true}
                    onEditClick={handleEditClick}
                    onClick={handleAvatarClick}
                    editIconStyle="badge"
                  />
                  <p className="text-sm mt-2 font-medium">{person.name}</p>
                  <p className="text-xs text-gray-500">{person.person_id}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Usage Examples */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Usage Examples</h2>
          <div className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
            <pre>{`// Basic usage with edit functionality
<EditablePersonAvatar
  person={person}
  size="lg"
  showEditIcon={true}
  onEditClick={handleEditPhoto}
  editIconStyle="badge"
/>

// Table/list usage with overlay style
<EditablePersonAvatar
  person={person}
  size="md"
  showEditIcon={true}
  onEditClick={handleEditPhoto}
  onClick={handlePersonSelect}
  editIconStyle="overlay"
/>

// Profile page usage
<EditablePersonAvatar
  person={person}
  size="xl"
  showEditIcon={true}
  onEditClick={handleEditPhoto}
  editIconStyle="badge"
  className="mx-auto"
/>`}</pre>
          </div>
        </section>
      </div>

      {/* Upload Modal */}
      <UploadProfileModal
        person={selectedPerson}
        isOpen={uploadModalOpen}
        onClose={handleUploadClose}
        onSuccess={handleUploadSuccess}
      />
    </div>
  );
};

export default EditablePersonAvatarDemo;
