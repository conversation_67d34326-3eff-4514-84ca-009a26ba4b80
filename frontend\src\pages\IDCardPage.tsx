import React, { useState } from "react";
import IdCardList from "../components/IdCard/IDCardList";
import { Breadcrumb, useBreadcrumb, PageHeader } from "../components/common";
import { Icon } from "@iconify/react";

// Interface for ID Card field visibility configuration
interface IDCardFieldVisibility {
  name: boolean;
  class: boolean;
  section: boolean;
  roll_number: boolean;
  parents_name: boolean;
  contact_no: boolean;
  department: boolean;
  designation: boolean;
  address: boolean;
  date_of_birth: boolean;
}

// ID Card Field Configuration Form Component
const IDCardFieldConfigForm: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: IDCardFieldVisibility) => void;
  currentConfig: IDCardFieldVisibility;
}> = ({ isOpen, onClose, onSave, currentConfig }) => {
  const [fieldConfig, setFieldConfig] =
    useState<IDCardFieldVisibility>(currentConfig);

  const handleToggleField = (fieldName: keyof IDCardFieldVisibility) => {
    setFieldConfig((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }));
  };

  const handleSave = () => {
    onSave(fieldConfig);
    onClose();
  };

  const handleClose = () => {
    // Reset to current config when closing without saving
    setFieldConfig(currentConfig);
    onClose();
  };

  if (!isOpen) return null;

  const fieldDefinitions = [
    {
      key: "name",
      label: "Full Name",
      required: false,
      description: "Person's full name",
    },
    {
      key: "class",
      label: "Class",
      required: false,
      description: "Student class or staff category",
    },
    {
      key: "section",
      label: "Section",
      required: false,
      description: "Class section (A, B, C, etc.)",
    },
    {
      key: "roll_number",
      label: "Roll Number",
      required: false,
      description: "Student roll number or staff ID",
    },
    {
      key: "parents_name",
      label: "Parent's Name",
      required: false,
      description: "Parent or guardian name",
    },
    {
      key: "contact_no",
      label: "Contact Number",
      required: false,
      description: "Phone number",
    },
    {
      key: "department",
      label: "Department",
      required: false,
      description: "Academic or work department",
    },
    {
      key: "address",
      label: "Address",
      required: false,
      description: "Person's address",
    },
    {
      key: "date_of_birth",
      label: "Date of Birth",
      required: false,
      description: "Date of birth (Layout 4 only)",
    },
    {
      key: "designation",
      label: "Designation",
      required: false,
      description: "Role or position",
    },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
              <Icon icon="mdi:eye-settings" className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                ID Card Field Configuration
              </h2>
              <p className="text-sm text-gray-500">
                Choose which fields to show or hide on ID cards
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <Icon icon="mdi:close" className="h-6 w-6" />
          </button>
        </div>

        {/* Configuration Form */}
        <div className="p-6">
          <div className="mb-4">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Field Visibility Settings
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Toggle the switches to show or hide fields on the ID cards.
            </p>
          </div>

          <div className="space-y-4">
            {fieldDefinitions.map((field) => {
              const isVisible =
                fieldConfig[field.key as keyof IDCardFieldVisibility];
              const isRequired = field.required;

              return (
                <div
                  key={field.key}
                  className={`flex items-center justify-between p-4 rounded-lg border ${
                    isRequired
                      ? "bg-blue-50 border-blue-200"
                      : "bg-gray-50 border-gray-200"
                  }`}
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium text-gray-900">
                        {field.label}
                      </h4>
                      {isRequired && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Required
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {field.description}
                    </p>
                  </div>

                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-500">
                      {isVisible ? "Visible" : "Hidden"}
                    </span>
                    <button
                      type="button"
                      onClick={() =>
                        handleToggleField(
                          field.key as keyof IDCardFieldVisibility
                        )
                      }
                      disabled={isRequired}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${
                        isVisible ? "bg-green-600" : "bg-gray-200"
                      } ${
                        isRequired
                          ? "opacity-50 cursor-not-allowed"
                          : "cursor-pointer"
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          isVisible ? "translate-x-6" : "translate-x-1"
                        }`}
                      />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Summary */}
          <div className="mt-6 p-4 bg-gray-100 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">
              Configuration Summary
            </h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-green-600 font-medium">
                  Visible Fields:{" "}
                  {Object.values(fieldConfig).filter(Boolean).length}
                </span>
              </div>
              <div>
                <span className="text-gray-600 font-medium">
                  Hidden Fields:{" "}
                  {Object.values(fieldConfig).filter((v) => !v).length}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSave}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2"
            >
              <Icon icon="mdi:content-save" className="h-4 w-4" />
              <span>Save Configuration</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const IDCardPage: React.FC = () => {
  const { createBreadcrumbs } = useBreadcrumb();
  const [isConfigFormOpen, setIsConfigFormOpen] = useState(false);

  // Default field visibility configuration
  const [fieldVisibilityConfig, setFieldVisibilityConfig] =
    useState<IDCardFieldVisibility>({
      name: true,
      class: true,
      section: true,
      roll_number: true,
      parents_name: true,
      contact_no: true,
      department: true,
      designation: true,
      address: true,
      date_of_birth: true,
    });

  // Create breadcrumb items
  const breadcrumbItems = createBreadcrumbs(
    { label: "Dashboard", path: "/", icon: "mdi:home" },
    { label: "ID Cards", icon: "mdi:card-account-details" }
  );

  const handleSaveFieldConfig = (config: IDCardFieldVisibility) => {
    setFieldVisibilityConfig(config);
    console.log("Field visibility configuration saved:", config);
    // Here you can save the configuration to localStorage, API, or pass it to IdCardList
  };

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbItems} />

      {/* Header with Configuration Button */}
      <PageHeader
        title="ID Card System"
        description="Generate and print ID cards for students and staff"
        buttonText="Configure Fields"
        onButtonClick={() => setIsConfigFormOpen(true)}
      />

      <IdCardList fieldVisibilityConfig={fieldVisibilityConfig} />

      {/* ID Card Field Configuration Modal */}
      <IDCardFieldConfigForm
        isOpen={isConfigFormOpen}
        onClose={() => setIsConfigFormOpen(false)}
        onSave={handleSaveFieldConfig}
        currentConfig={fieldVisibilityConfig}
      />
    </div>
  );
};

export default IDCardPage;
