const { Model, DataTypes } = require("sequelize");
const sequelize = require("../config/database");

class Person extends Model {}

Person.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    person_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: false, // Unique per school, not globally
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM("student", "staff", "non_teaching"),
      allowNull: false,
    },
    class: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    section: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    roll_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    parents_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    contact_no: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    department: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    designation: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    date_of_birth: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Date of birth for the person",
    },
    photo_path: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    csv_batch_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "csv_batches",
        key: "id",
      },
    },
    school_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "schools",
        key: "id",
      },
    },
  },
  {
    sequelize,
    modelName: "Person",
    tableName: "persons",
    timestamps: true,
    underscored: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

module.exports = Person;
