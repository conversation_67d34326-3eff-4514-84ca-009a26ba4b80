import React from 'react';
import { Icon } from '@iconify/react';

/**
 * Global reusable Header component for pages
 */
export interface HeaderProps {
  title: string;
  description?: string;
  icon?: string;
  button?: {
    label: string;
    onClick: () => void;
    icon?: string;
    variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning';
    disabled?: boolean;
    loading?: boolean;
  } | null;
  breadcrumb?: {
    items: Array<{
      label: string;
      href?: string;
      onClick?: () => void;
    }>;
  };
  actions?: Array<{
    label: string;
    onClick: () => void;
    icon?: string;
    variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning';
    disabled?: boolean;
  }>;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const Header: React.FC<HeaderProps> = ({
  title,
  description,
  icon,
  button,
  breadcrumb,
  actions = [],
  className = '',
  size = 'md'
}) => {
  // Size variants
  const sizeClasses = {
    sm: {
      title: 'text-xl font-semibold',
      description: 'text-sm',
      spacing: 'space-y-2',
      padding: 'py-4'
    },
    md: {
      title: 'text-2xl font-bold',
      description: 'text-base',
      spacing: 'space-y-3',
      padding: 'py-6'
    },
    lg: {
      title: 'text-3xl font-bold',
      description: 'text-lg',
      spacing: 'space-y-4',
      padding: 'py-8'
    }
  };

  // Button variant classes
  const buttonVariants = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600',
    success: 'bg-green-600 hover:bg-green-700 text-white border-green-600',
    danger: 'bg-red-600 hover:bg-red-700 text-white border-red-600',
    warning: 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600'
  };

  const currentSize = sizeClasses[size];

  return (
    <div className={`${className} ${currentSize.padding}`}>
      {/* Breadcrumb */}
      {breadcrumb && (
        <nav className="mb-4" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            {breadcrumb.items.map((item, index) => (
              <li key={index} className="flex items-center">
                {index > 0 && (
                  <Icon 
                    icon="mdi:chevron-right" 
                    className="w-4 h-4 mx-2 text-gray-400" 
                  />
                )}
                {item.href ? (
                  <a
                    href={item.href}
                    className="hover:text-gray-700 transition-colors"
                  >
                    {item.label}
                  </a>
                ) : item.onClick ? (
                  <button
                    onClick={item.onClick}
                    className="hover:text-gray-700 transition-colors"
                  >
                    {item.label}
                  </button>
                ) : (
                  <span className={index === breadcrumb.items.length - 1 ? 'text-gray-900 font-medium' : ''}>
                    {item.label}
                  </span>
                )}
              </li>
            ))}
          </ol>
        </nav>
      )}

      {/* Header Content */}
      <div className="flex items-start justify-between">
        {/* Title and Description */}
        <div className={`flex-1 ${currentSize.spacing}`}>
          <div className="flex items-center space-x-3">
            {icon && (
              <div className="flex-shrink-0">
                <Icon 
                  icon={icon} 
                  className={`${
                    size === 'sm' ? 'w-6 h-6' : 
                    size === 'md' ? 'w-8 h-8' : 
                    'w-10 h-10'
                  } text-gray-600`} 
                />
              </div>
            )}
            <h1 className={`${currentSize.title} text-gray-900 leading-tight`}>
              {title}
            </h1>
          </div>
          
          {description && (
            <p className={`${currentSize.description} text-gray-600 mt-2 max-w-3xl`}>
              {description}
            </p>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-3 ml-6">
          {/* Additional Actions */}
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={action.onClick}
              disabled={action.disabled}
              className={`
                inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md
                transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2
                ${action.disabled 
                  ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed' 
                  : `${buttonVariants[action.variant || 'secondary']} focus:ring-${action.variant || 'gray'}-500`
                }
              `}
            >
              {action.icon && (
                <Icon 
                  icon={action.icon} 
                  className="w-4 h-4 mr-2" 
                />
              )}
              {action.label}
            </button>
          ))}

          {/* Primary Button */}
          {button && (
            <button
              onClick={button.onClick}
              disabled={button.disabled || button.loading}
              className={`
                inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md
                transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2
                ${button.disabled || button.loading
                  ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                  : `${buttonVariants[button.variant || 'primary']} focus:ring-${button.variant || 'blue'}-500`
                }
              `}
            >
              {button.loading ? (
                <Icon 
                  icon="mdi:loading" 
                  className="w-4 h-4 mr-2 animate-spin" 
                />
              ) : button.icon ? (
                <Icon 
                  icon={button.icon} 
                  className="w-4 h-4 mr-2" 
                />
              ) : null}
              {button.label}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Header;
