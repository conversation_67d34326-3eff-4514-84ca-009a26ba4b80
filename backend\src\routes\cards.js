const express = require("express");
const { fn, col } = require("sequelize");
const { Person, School } = require("../models");
const path = require("path");
const fs = require("fs");

const router = express.Router();

// Card configuration constants
const CARD_COLORS = {
  student: "#52C41A", // Green
  staff: "#4A90E2", // Blue
  non_teaching: "#FF6B35", // Orange
};

const DEFAULT_DIMENSIONS = {
  width: 325,
  height: 500,
  printWidth: "54mm", // 2.125 inches (vertical width)
  printHeight: "85.6mm", // 3.375 inches (vertical height)
};

const DEFAULT_THEMES = {
  student: {
    primaryColor: CARD_COLORS.student,
    secondaryColor: "#FF8A65",
    textColor: "#333333",
    backgroundColor: "#FFFFFF",
    layout: "standard",
  },
  staff: {
    primaryColor: CARD_COLORS.staff,
    secondaryColor: "#64B5F6",
    textColor: "#333333",
    backgroundColor: "#FFFFFF",
    layout: "standard",
  },
  non_teaching: {
    primaryColor: CARD_COLORS.non_teaching,
    secondaryColor: "#81C784",
    textColor: "#333333",
    backgroundColor: "#FFFFFF",
    layout: "standard",
  },
};

// Get card configuration for React components
router.get("/config/:personId", async (req, res) => {
  try {
    const person = await Person.findByPk(req.params.personId, {
      include: [
        {
          model: School,
          attributes: [
            "id",
            "name",
            "address",
            "phone",
            "email",
            "logo_path",
            "stamp_path",
            "signature_path",
          ],
        },
      ],
    });

    if (!person) {
      return res.status(404).json({ error: "Person not found" });
    }

    let school = person.School;
    if (!school) {
      // Fallback to first school if person doesn't have school assigned
      school = await School.findOne();
      if (!school) {
        school = await School.create({
          name: "BIRTA GLOBAL SCHOOL",
          address: "Aduwa Bridge, Atthi Sadan Road, Birtamode",
          phone: "+977-9852672234",
          email: "<EMAIL>",
        });
      }
    }

    // Create card configuration
    const cardConfig = {
      person: {
        id: person.id,
        person_id: person.person_id,
        name: person.name,
        type: person.type,
        class: person.class,
        section: person.section,
        roll_number: person.roll_number,
        parents_name: person.parents_name,
        contact_no: person.contact_no,
        photo_path: person.photo_path,
        csv_batch_id: person.csv_batch_id,
        created_at: person.created_at,
        updated_at: person.updated_at,
      },
      school: {
        id: school.id,
        name: school.name,
        address: school.address,
        phone: school.phone,
        email: school.email,
        logo_path: school.logo_path,
        created_at: school.created_at,
        updated_at: school.updated_at,
      },
      template: {
        type: person.type,
        theme: DEFAULT_THEMES[person.type],
        dimensions: DEFAULT_DIMENSIONS,
        showElements: {
          logo: true,
          photo: true,
          qrCode: false,
          decorativeElements: true,
        },
      },
    };

    res.json(cardConfig);
  } catch (error) {
    console.error("Error generating card config:", error);
    res.status(500).json({ error: "Failed to generate card configuration" });
  }
});

// Get statistics for card generation
router.get("/stats", async (req, res) => {
  try {
    const { school_id } = req.query;
    const whereClause = school_id ? { school_id } : {};

    const stats = await Person.findAll({
      attributes: ["type", [fn("COUNT", col("id")), "count"]],
      where: whereClause,
      group: ["type"],
    });

    const formattedStats = stats.reduce((acc, stat) => {
      acc[stat.type] = parseInt(stat.get("count"));
      return acc;
    }, {});

    res.json({
      total: Object.values(formattedStats).reduce(
        (sum, count) => sum + count,
        0
      ),
      breakdown: formattedStats,
    });
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch statistics" });
  }
});

// Get single card configuration for frontend PDF generation
router.get("/pdf/:personId", async (req, res) => {
  try {
    const person = await Person.findByPk(req.params.personId);

    if (!person) {
      return res.status(404).json({ error: "Person not found" });
    }

    let school = await School.findOne();
    if (!school) {
      school = await School.create({
        name: "BIRTA GLOBAL SCHOOL",
        address: "Aduwa Bridge, Atthi Sadan Road, Birtamode",
        phone: "+977-9852672234",
        email: "<EMAIL>",
      });
    }

    // Return card configuration for frontend PDF generation
    const cardConfig = {
      person: {
        id: person.id,
        person_id: person.person_id,
        name: person.name,
        type: person.type,
        class: person.class,
        section: person.section,
        roll_number: person.roll_number,
        parents_name: person.parents_name,
        contact_no: person.contact_no,
        photo_path: person.photo_path,
        designation: person.designation,
        department: person.department,
        address: person.address,
      },
      school: {
        id: school.id,
        name: school.name,
        address: school.address,
        phone: school.phone,
        email: school.email,
        logo_path: school.logo_path,
      },
      template: {
        type: person.type,
        theme: DEFAULT_THEMES[person.type],
        dimensions: DEFAULT_DIMENSIONS,
        showElements: {
          logo: true,
          photo: true,
          qrCode: false,
          decorativeElements: true,
        },
      },
    };

    res.json(cardConfig);
  } catch (error) {
    console.error("Card config generation error:", error);
    res.status(500).json({ error: "Failed to generate card configuration" });
  }
});

// Get multiple cards configuration for frontend PDF generation
router.post("/pdf/batch", async (req, res) => {
  try {
    const { personIds, type } = req.body;

    if (!personIds || !Array.isArray(personIds)) {
      return res.status(400).json({ error: "Person IDs array is required" });
    }

    let whereClause = {
      id: personIds,
    };

    if (type && ["student", "staff", "non_teaching"].includes(type)) {
      whereClause.type = type;
    }

    const persons = await Person.findAll({
      where: whereClause,
      order: [["name", "ASC"]],
    });

    if (persons.length === 0) {
      return res.status(404).json({ error: "No persons found" });
    }

    let school = await School.findOne();
    if (!school) {
      school = await School.create({
        name: "BIRTA GLOBAL SCHOOL",
        address: "Aduwa Bridge, Atthi Sadan Road, Birtamode",
        phone: "+977-9852672234",
        email: "<EMAIL>",
      });
    }

    // Return card configurations for frontend PDF generation
    const cardConfigs = persons.map((person) => ({
      person: {
        id: person.id,
        person_id: person.person_id,
        name: person.name,
        type: person.type,
        class: person.class,
        section: person.section,
        roll_number: person.roll_number,
        parents_name: person.parents_name,
        contact_no: person.contact_no,
        photo_path: person.photo_path,
        department: person.department,
        address: person.address,
      },
      school: {
        id: school.id,
        name: school.name,
        address: school.address,
        phone: school.phone,
        email: school.email,
        logo_path: school.logo_path,
      },
      template: {
        type: person.type,
        theme: DEFAULT_THEMES[person.type],
        dimensions: DEFAULT_DIMENSIONS,
        showElements: {
          logo: true,
          photo: true,
          qrCode: false,
          decorativeElements: true,
        },
      },
    }));

    res.json({
      cardConfigs,
      filename: type ? `${type}_cards_batch.pdf` : "cards_batch.pdf",
    });
  } catch (error) {
    console.error("Batch card config generation error:", error);
    res
      .status(500)
      .json({ error: "Failed to generate batch card configurations" });
  }
});

// Get all cards configuration of a specific type for frontend PDF generation
router.get("/pdf/type/:type", async (req, res) => {
  try {
    const { type } = req.params;

    if (!["student", "staff", "non_teaching"].includes(type)) {
      return res.status(400).json({
        error: "Invalid type. Must be student, staff, or non_teaching",
      });
    }

    const persons = await Person.findAll({
      where: { type },
      order: [["name", "ASC"]],
    });

    if (persons.length === 0) {
      return res.status(404).json({ error: `No ${type} records found` });
    }

    let school = await School.findOne();
    if (!school) {
      school = await School.create({
        name: "BIRTA GLOBAL SCHOOL",
        address: "Aduwa Bridge, Atthi Sadan Road, Birtamode",
        phone: "+977-9852672234",
        email: "<EMAIL>",
      });
    }

    // Return card configurations for frontend PDF generation
    const cardConfigs = persons.map((person) => ({
      person: {
        id: person.id,
        person_id: person.person_id,
        name: person.name,
        type: person.type,
        class: person.class,
        section: person.section,
        roll_number: person.roll_number,
        parents_name: person.parents_name,
        contact_no: person.contact_no,
        photo_path: person.photo_path,
        department: person.department,
        address: person.address,
      },
      school: {
        id: school.id,
        name: school.name,
        address: school.address,
        phone: school.phone,
        email: school.email,
        logo_path: school.logo_path,
      },
      template: {
        type: person.type,
        theme: DEFAULT_THEMES[person.type],
        dimensions: DEFAULT_DIMENSIONS,
        showElements: {
          logo: true,
          photo: true,
          qrCode: false,
          decorativeElements: true,
        },
      },
    }));

    res.json({ cardConfigs, filename: `${type}_cards.pdf` });
  } catch (error) {
    console.error("Type card config generation error:", error);
    res
      .status(500)
      .json({ error: `Failed to generate ${type} card configurations` });
  }
});

// Handle preflight requests for photos
router.options("/photos/:filename", (req, res) => {
  // Set comprehensive CORS headers for preflight
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control"
  );
  res.header("Cross-Origin-Resource-Policy", "cross-origin");
  res.header("Access-Control-Max-Age", "86400"); // Cache preflight for 24 hours
  res.sendStatus(200);
});

// Serve photos for card components
router.get("/photos/:filename", (req, res) => {
  const filename = req.params.filename;
  const photoPath = path.join(__dirname, "../../uploads/photos", filename);

  console.log(`Photo request: ${filename} from origin: ${req.get("Origin")}`);
  console.log(`Photo path: ${photoPath}`);
  console.log(`File exists: ${fs.existsSync(photoPath)}`);

  // Check if file exists
  if (!fs.existsSync(photoPath)) {
    console.log(`Photo not found: ${filename}`);
    // Set CORS headers even for 404 responses
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET");
    res.header("Cross-Origin-Resource-Policy", "cross-origin");
    return res.status(404).json({ error: "Photo not found" });
  }

  try {
    // Get file stats for proper headers
    const stats = fs.statSync(photoPath);
    const fileExtension = path.extname(filename).toLowerCase();

    // Determine MIME type
    let mimeType = "image/jpeg"; // default
    switch (fileExtension) {
      case ".png":
        mimeType = "image/png";
        break;
      case ".jpg":
      case ".jpeg":
        mimeType = "image/jpeg";
        break;
      case ".gif":
        mimeType = "image/gif";
        break;
      case ".webp":
        mimeType = "image/webp";
        break;
      case ".svg":
        mimeType = "image/svg+xml";
        break;
    }

    // Set comprehensive headers for image serving
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET");
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control"
    );
    res.header("Cross-Origin-Resource-Policy", "cross-origin");
    res.header("Content-Type", mimeType);
    res.header("Content-Length", stats.size);
    res.header("Cache-Control", "public, max-age=31536000"); // Cache for 1 year
    res.header("ETag", `"${stats.mtime.getTime()}-${stats.size}"`);
    res.header("Last-Modified", stats.mtime.toUTCString());

    // Handle conditional requests
    const ifNoneMatch = req.get("If-None-Match");
    const etag = `"${stats.mtime.getTime()}-${stats.size}"`;

    if (ifNoneMatch === etag) {
      return res.status(304).end();
    }

    console.log(
      `Serving photo: ${filename} with CORS headers and proper MIME type: ${mimeType}`
    );
    res.sendFile(photoPath);
  } catch (error) {
    console.error(`Error serving photo ${filename}:`, error);
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Cross-Origin-Resource-Policy", "cross-origin");
    res.status(500).json({ error: "Internal server error" });
  }
});

// Handle preflight requests for logos
router.options("/logos/:filename", (req, res) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control"
  );
  res.header("Cross-Origin-Resource-Policy", "cross-origin");
  res.header("Access-Control-Max-Age", "86400"); // Cache preflight for 24 hours
  res.sendStatus(200);
});

// Serve school logos for card components
router.get("/logos/:filename", (req, res) => {
  const filename = req.params.filename;
  const logoPath = path.join(
    __dirname,
    "../../uploads/school-assets",
    filename
  );

  console.log(`Logo request: ${filename} from origin: ${req.get("Origin")}`);
  console.log(`Logo path: ${logoPath}`);
  console.log(`File exists: ${fs.existsSync(logoPath)}`);

  // Check if file exists
  if (!fs.existsSync(logoPath)) {
    console.log(`Logo not found: ${filename}`);
    // Set CORS headers even for 404 responses
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET");
    res.header("Cross-Origin-Resource-Policy", "cross-origin");
    return res.status(404).json({ error: "Logo not found" });
  }

  try {
    // Get file stats for proper headers
    const stats = fs.statSync(logoPath);
    const fileExtension = path.extname(filename).toLowerCase();

    // Determine MIME type
    let mimeType = "image/jpeg"; // default
    switch (fileExtension) {
      case ".png":
        mimeType = "image/png";
        break;
      case ".jpg":
      case ".jpeg":
        mimeType = "image/jpeg";
        break;
      case ".gif":
        mimeType = "image/gif";
        break;
      case ".webp":
        mimeType = "image/webp";
        break;
      case ".svg":
        mimeType = "image/svg+xml";
        break;
    }

    // Set comprehensive headers for image serving
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET");
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control"
    );
    res.header("Cross-Origin-Resource-Policy", "cross-origin");
    res.header("Content-Type", mimeType);
    res.header("Content-Length", stats.size);
    res.header("Cache-Control", "public, max-age=31536000"); // Cache for 1 year
    res.header("ETag", `"${stats.mtime.getTime()}-${stats.size}"`);
    res.header("Last-Modified", stats.mtime.toUTCString());

    // Handle conditional requests
    const ifNoneMatch = req.get("If-None-Match");
    const etag = `"${stats.mtime.getTime()}-${stats.size}"`;

    if (ifNoneMatch === etag) {
      return res.status(304).end();
    }

    console.log(
      `Serving logo: ${filename} with CORS headers and proper MIME type: ${mimeType}`
    );
    res.sendFile(logoPath);
  } catch (error) {
    console.error(`Error serving logo ${filename}:`, error);
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Cross-Origin-Resource-Policy", "cross-origin");
    res.status(500).json({ error: "Internal server error" });
  }
});

// Diagnostic endpoint to check file existence and server status
router.get("/diagnostic/photos/:filename", (req, res) => {
  const filename = req.params.filename;
  const photoPath = path.join(__dirname, "../../uploads/photos", filename);

  // Set CORS headers for diagnostic endpoint
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET");
  res.header("Cross-Origin-Resource-Policy", "cross-origin");

  try {
    const exists = fs.existsSync(photoPath);
    let stats = null;

    if (exists) {
      stats = fs.statSync(photoPath);
    }

    const diagnosticInfo = {
      filename,
      requestedPath: photoPath,
      exists,
      serverTime: new Date().toISOString(),
      nodeEnv: process.env.NODE_ENV,
      uploadPath: process.env.UPLOAD_PATH || "./uploads",
      stats: exists
        ? {
            size: stats.size,
            mtime: stats.mtime,
            isFile: stats.isFile(),
            mode: stats.mode.toString(8),
          }
        : null,
      directories: {
        uploadsExists: fs.existsSync(path.join(__dirname, "../../uploads")),
        photosExists: fs.existsSync(
          path.join(__dirname, "../../uploads/photos")
        ),
      },
    };

    console.log(`Diagnostic request for ${filename}:`, diagnosticInfo);
    res.json(diagnosticInfo);
  } catch (error) {
    console.error(`Diagnostic error for ${filename}:`, error);
    res.status(500).json({
      error: "Diagnostic failed",
      message: error.message,
      filename,
    });
  }
});

// Diagnostic endpoint for logos
router.get("/diagnostic/logos/:filename", (req, res) => {
  const filename = req.params.filename;
  const logoPath = path.join(
    __dirname,
    "../../uploads/school-assets",
    filename
  );

  // Set CORS headers for diagnostic endpoint
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET");
  res.header("Cross-Origin-Resource-Policy", "cross-origin");

  try {
    const exists = fs.existsSync(logoPath);
    let stats = null;

    if (exists) {
      stats = fs.statSync(logoPath);
    }

    const diagnosticInfo = {
      filename,
      requestedPath: logoPath,
      exists,
      serverTime: new Date().toISOString(),
      nodeEnv: process.env.NODE_ENV,
      uploadPath: process.env.UPLOAD_PATH || "./uploads",
      stats: exists
        ? {
            size: stats.size,
            mtime: stats.mtime,
            isFile: stats.isFile(),
            mode: stats.mode.toString(8),
          }
        : null,
      directories: {
        uploadsExists: fs.existsSync(path.join(__dirname, "../../uploads")),
        schoolAssetsExists: fs.existsSync(
          path.join(__dirname, "../../uploads/school-assets")
        ),
      },
    };

    console.log(`Logo diagnostic request for ${filename}:`, diagnosticInfo);
    res.json(diagnosticInfo);
  } catch (error) {
    console.error(`Logo diagnostic error for ${filename}:`, error);
    res.status(500).json({
      error: "Diagnostic failed",
      message: error.message,
      filename,
    });
  }
});

module.exports = router;
