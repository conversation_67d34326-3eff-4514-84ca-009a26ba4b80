import React, { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { TableActionProps, TableActionConfig } from "./types";
// import { TableData } from './types';

export const TableAction = <T extends Record<string, any>>({
  row,
  actions,
  className = "",
}: TableActionProps<T>) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [actionToConfirm, setActionToConfirm] =
    useState<TableActionConfig<T> | null>(null);

  const handleActionClick = (action: TableActionConfig<T>) => {
    // Check if action is disabled
    if (typeof action.disabled === "function" && action.disabled(row)) return;
    if (typeof action.disabled === "boolean" && action.disabled) return;

    // Handle delete action with confirmation
    if (action.type === "delete") {
      setActionToConfirm(action);
      setShowDeleteDialog(true);
      return;
    }

    // Handle toggle action
    if (action.type === "toggle") {
      action.onClick?.(row);
      return;
    }

    // Handle other actions
    action.onClick?.(row);
  };

  const handleConfirmAction = () => {
    if (actionToConfirm) {
      actionToConfirm.onClick?.(row);
    }
    setShowDeleteDialog(false);
    setActionToConfirm(null);
  };

  const renderToggleAction = (action: TableActionConfig<T>) => (
    <div
      key={`toggle-${row._id}`}
      className="flex text-black cursor-pointer"
      onClick={() => handleActionClick(action)}
    >
      <label
        htmlFor={`toggle-${row._id}`}
        className="flex cursor-pointer relative"
      >
        <input
          type="checkbox"
          id={`toggle-${row._id}`}
          className="sr-only"
          checked={row.isActive || false}
          onChange={() => handleActionClick(action)}
        />
        <div
          className={`toggle-bg border-2 h-[18px] w-[36px] rounded-full transition-colors duration-200 ease-in-out ${
            row.isActive
              ? "bg-blue-600 border-blue-600"
              : "bg-gray-200 border-gray-200"
          }`}
        >
          <div
            className={`toggle-dot absolute left-0 top-1/2 -translate-y-1/2 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out transform ${
              row.isActive
                ? "translate-x-[19px] border-blue-600"
                : "translate-x-[1px] border-gray-200"
            }`}
          />
        </div>
      </label>
    </div>
  );

  const renderIconAction = (action: TableActionConfig<T>) => {
    const isDisabled =
      typeof action.disabled === "function"
        ? action.disabled(row)
        : action.disabled;

    return (
      <div
        key={`${action.type}-${row._id}`}
        className={`text-xl cursor-pointer transition-opacity ${
          isDisabled ? "opacity-50 cursor-not-allowed" : "hover:opacity-80"
        }`}
        onClick={() => !isDisabled && handleActionClick(action)}
        title={action.title}
      >
        <Icon
          icon={action.icon || "mdi:help"}
          fontSize={16}
          color={isDisabled ? "#9CA3AF" : action.color || "#6B7280"}
        />
      </div>
    );
  };

  const renderCustomAction = (action: TableActionConfig<T>) => {
    const isDisabled =
      typeof action.disabled === "function"
        ? action.disabled(row)
        : action.disabled;

    return (
      <button
        key={`custom-${row._id}`}
        className={`px-2 py-1 text-white rounded text-xs transition-colors ${
          isDisabled
            ? "bg-gray-400 cursor-not-allowed"
            : "bg-blue-600 hover:bg-blue-700"
        }`}
        onClick={() => !isDisabled && handleActionClick(action)}
        disabled={isDisabled}
        title={action.title}
      >
        {action.title || "Action"}
      </button>
    );
  };

  const visibleActions = actions.filter((action) => {
    if (typeof action.show === "function") {
      return action.show(row);
    }
    return action.show !== false;
  });

  if (visibleActions.length === 0) {
    return null;
  }

  return (
    <>
      <td
        className={`px-6 flex md:min-w-[10rem] w-full gap-3 py-8 place-items-center text-right text-sm font-medium ${className}`}
      >
        {visibleActions.map((action) => {
          if (action.type === "toggle") {
            return renderToggleAction(action);
          }

          if (action.type === "custom") {
            return renderCustomAction(action);
          }

          return renderIconAction(action);
        })}
      </td>

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <div className="flex items-center mb-4">
              <Icon
                icon="material-symbols:warning"
                className="text-red-500 mr-3"
                fontSize={24}
              />
              <h3 className="text-lg font-semibold text-gray-900">
                Confirm {actionToConfirm?.title || "Action"}
              </h3>
            </div>
            <p className="text-gray-600 mb-6">
              Are you sure you want to{" "}
              {actionToConfirm?.title?.toLowerCase() || "perform this action"}?
              This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteDialog(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmAction}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                {actionToConfirm?.title || "Confirm"}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TableAction;
