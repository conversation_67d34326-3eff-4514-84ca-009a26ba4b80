import { useState } from "react";
import axios from "axios";
import { School } from "../types";
import { useSchool } from "../../../contexts/SchoolContext";
import { useNotification } from "../../../contexts/NotificationContext";
import { processImageFile } from "../../../utils/imageUtils";

export const useSchoolOperations = () => {
  const { deleteSchool, refreshSchools } = useSchool();
  const { showNotification } = useNotification();
  const [uploadingLogo, setUploadingLogo] = useState<number | null>(null);

  const API_BASE_URL =
    process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

  const handleDelete = async (school: School) => {
    if (
      !window.confirm(
        `Are you sure you want to delete ${school.name}? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      await deleteSchool(school.id);
      showNotification("School deleted successfully", "success");
    } catch (error: any) {
      showNotification(error.message || "Failed to delete school", "error");
    }
  };

  const handleLogoUpload = async (schoolId: number, file: File) => {
    setUploadingLogo(schoolId);
    try {
      // Process the image with validation and compression
      const result = await processImageFile(file, {
        maxSizeMB: 2,
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 0.8,
        autoCompress: true,
      });

      if (!result.isValid) {
        showNotification(result.error || "Invalid image file", "error");
        return;
      }

      if (!result.file) {
        showNotification("No file to upload", "error");
        return;
      }

      const formData = new FormData();
      formData.append("logo", result.file);

      await axios.post(
        `${API_BASE_URL}/api/school/${schoolId}/logo`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      showNotification("Logo uploaded successfully", "success");
      refreshSchools();
    } catch (error: any) {
      showNotification(
        error.response?.data?.error || "Failed to upload logo",
        "error"
      );
    } finally {
      setUploadingLogo(null);
    }
  };

  return {
    uploadingLogo,
    handleDelete,
    handleLogoUpload,
  };
};
