import { apiService, ApiResponse } from "./apiService";
import { BatchRequestManager } from "./requestQueue";

/**
 * Card service for ID card generation and management
 */
export class CardService {
  private batchManager: BatchRequestManager;

  constructor() {
    this.batchManager = new BatchRequestManager(50, 20); // 50ms timeout, max 20 requests
  }

  /**
   * Get card configuration for a person
   */
  async getCardConfig(personId: number): Promise<ApiResponse<CardConfig>> {
    return apiService.get<CardConfig>(`/api/cards/config/${personId}`, {
      cache: true,
      cacheTTL: 300, // 5 minutes cache
      cacheKey: `card-config:${personId}`,
    });
  }

  /**
   * Get card configuration for PDF generation
   */
  async getCardPdfConfig(personId: number): Promise<ApiResponse<CardConfig>> {
    return apiService.get<CardConfig>(`/api/cards/pdf/${personId}`, {
      cache: true,
      cacheTTL: 180, // 3 minutes cache for PDF configs
      cacheKey: `card-pdf:${personId}`,
    });
  }

  /**
   * Get multiple card configurations using batch requests
   */
  async getCardConfigsBatch(personIds: number[]): Promise<CardConfig[]> {
    const batchKey = "card-configs-batch";

    const results = await Promise.all(
      personIds.map((personId) =>
        this.batchManager.add(batchKey, personId, async (ids: number[]) => {
          // Make single batch request for all IDs
          const response = await apiService.post<CardConfig[]>(
            "/api/cards/pdf/batch",
            {
              personIds: ids,
            }
          );
          return response.data;
        })
      )
    );

    return results;
  }

  /**
   * Get all cards of a specific type for PDF generation
   */
  async getCardsByType(type: PersonType): Promise<ApiResponse<CardConfig[]>> {
    return apiService.get<CardConfig[]>(`/api/cards/pdf/type/${type}`, {
      cache: true,
      cacheTTL: 120, // 2 minutes cache for bulk operations
      cacheKey: `cards-by-type:${type}`,
    });
  }

  /**
   * Get card statistics
   */
  async getCardStats(schoolId?: number): Promise<ApiResponse<CardStats>> {
    const params = schoolId ? { school_id: schoolId } : {};

    return apiService.get<CardStats>("/api/cards/stats", {
      params,
      cache: true,
      cacheTTL: 180, // 3 minutes cache
      cacheKey: `card-stats:${schoolId || "all"}`,
    });
  }

  /**
   * Generate single card PDF
   */
  async generateSingleCardPdf(
    personId: number,
    options: PdfOptions = {}
  ): Promise<Blob> {
    const config = await this.getCardPdfConfig(personId);
    return this.generatePdfFromConfig([config.data], options);
  }

  /**
   * Generate batch cards PDF
   */
  async generateBatchCardsPdf(
    personIds: number[],
    options: PdfOptions = {}
  ): Promise<Blob> {
    const configs = await this.getCardConfigsBatch(personIds);
    return this.generatePdfFromConfig(configs, options);
  }

  /**
   * Generate cards PDF by type
   */
  async generateCardsPdfByType(
    type: PersonType,
    options: PdfOptions = {}
  ): Promise<Blob> {
    const response = await this.getCardsByType(type);
    return this.generatePdfFromConfig(response.data, options);
  }

  /**
   * Generate PDF from card configurations (client-side)
   */
  private async generatePdfFromConfig(
    configs: CardConfig[],
    options: PdfOptions = {}
  ): Promise<Blob> {
    // This would use a PDF generation library like jsPDF or pdf-lib
    // For now, we'll make a server request for PDF generation
    const response = await fetch(
      `${this.getBaseUrl()}/api/cards/generate-pdf`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
        },
        credentials: "include",
        body: JSON.stringify({
          configs,
          options,
        }),
      }
    );

    if (!response.ok) {
      throw new Error("PDF generation failed");
    }

    return response.blob();
  }

  /**
   * Preview card configuration
   */
  async previewCard(personId: number): Promise<ApiResponse<CardPreview>> {
    return apiService.get<CardPreview>(`/api/cards/preview/${personId}`, {
      cache: true,
      cacheTTL: 300,
      cacheKey: `card-preview:${personId}`,
    });
  }

  /**
   * Update card template settings
   */
  async updateCardTemplate(
    personId: number,
    templateSettings: Partial<CardTemplate>
  ): Promise<ApiResponse<CardConfig>> {
    const response = await apiService.put<CardConfig>(
      `/api/cards/template/${personId}`,
      templateSettings
    );

    // Invalidate related cache
    apiService.clearCache(`card-config:${personId}`);
    apiService.clearCache(`card-pdf:${personId}`);
    apiService.clearCache(`card-preview:${personId}`);

    return response;
  }

  /**
   * Validate card data
   */
  validateCardData(config: CardConfig): ValidationResult {
    const errors: string[] = [];

    if (!config.person) {
      errors.push("Person data is required");
    } else {
      if (!config.person.name) {
        errors.push("Person name is required");
      }
      if (!config.person.type) {
        errors.push("Person type is required");
      }
      if (!config.person.person_id) {
        errors.push("Person ID is required");
      }
    }

    if (!config.school) {
      errors.push("School data is required");
    } else {
      if (!config.school.name) {
        errors.push("School name is required");
      }
      if (!config.school.address) {
        errors.push("School address is required");
      }
    }

    if (!config.template) {
      errors.push("Template configuration is required");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get asset URL for card images
   */
  getAssetUrl(path: string, type: "photo" | "logo" = "photo"): string {
    if (!path) return "";
    if (path.startsWith("http")) return path;

    // Use the proper API endpoints with CORS headers instead of direct uploads path
    if (type === "photo") {
      return `${this.getBaseUrl()}/api/cards/photos/${path}`;
    } else {
      return `${this.getBaseUrl()}/api/cards/logos/${path}`;
    }
  }

  /**
   * Get default card dimensions
   */
  getDefaultDimensions(): CardDimensions {
    return {
      width: 324,
      height: 204,
      printWidth: "85.6mm",
      printHeight: "53.98mm",
    };
  }

  /**
   * Get default theme for person type
   */
  getDefaultTheme(type: PersonType): CardTheme {
    const themes: Record<PersonType, CardTheme> = {
      student: {
        primaryColor: "#2d3091",
        secondaryColor: "#ffffff",
        textColor: "#000000",
        layout: "standard",
      },
      staff: {
        primaryColor: "#1e40af",
        secondaryColor: "#ffffff",
        textColor: "#000000",
        layout: "standard",
      },
      non_teaching: {
        primaryColor: "#059669",
        secondaryColor: "#ffffff",
        textColor: "#000000",
        layout: "standard",
      },
    };

    return themes[type];
  }

  /**
   * Get base URL
   */
  private getBaseUrl(): string {
    return (
      process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com"
    );
  }

  /**
   * Clear card-related caches
   */
  clearCache(): void {
    apiService.clearCache("card*");
  }

  /**
   * Get service statistics
   */
  getStats(): CardServiceStats {
    return {
      cache: apiService.getCacheStats(),
      batch: this.batchManager.getStats(),
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.batchManager.clear();
  }
}

// Types
export type PersonType = "student" | "staff" | "non_teaching";

export interface CardConfig {
  person: {
    id: number;
    person_id: string;
    name: string;
    type: PersonType;
    class?: string;
    section?: string;
    roll_number?: string;
    parents_name?: string;
    contact_no?: string;
    photo_path?: string;
    department?: string;
    designation?: string;
  };
  school: {
    id: number;
    name: string;
    address: string;
    phone: string;
    email?: string;
    logo_path?: string;
    stamp_path?: string;
    signature_path?: string;
  };
  template: CardTemplate;
}

export interface CardTemplate {
  type: PersonType;
  theme: CardTheme;
  dimensions: CardDimensions;
  showElements: {
    logo: boolean;
    photo: boolean;
    qrCode: boolean;
    decorativeElements: boolean;
  };
  renderMode?: "default" | "vertical" | "mai_valley";
}

export interface CardTheme {
  primaryColor: string;
  secondaryColor: string;
  textColor: string;
  layout: "standard" | "compact";
}

export interface CardDimensions {
  width: number;
  height: number;
  printWidth: string;
  printHeight: string;
}

export interface CardStats {
  total: number;
  breakdown: Record<PersonType, number>;
  by_school?: Record<string, number>;
}

export interface CardPreview {
  html: string;
  css: string;
  config: CardConfig;
}

export interface PdfOptions {
  format?: "A4" | "Letter";
  orientation?: "portrait" | "landscape";
  cardsPerPage?: number;
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface CardServiceStats {
  cache: any;
  batch: any;
}

// Create singleton instance
export const cardService = new CardService();
