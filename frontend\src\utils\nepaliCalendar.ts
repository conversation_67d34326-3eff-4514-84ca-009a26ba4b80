/**
 * Nepali Calendar (<PERSON><PERSON><PERSON>) utilities
 * Conversion between AD and BS dates
 */

// Nepali calendar data - days in each month for different years
const nepaliCalendarData: { [year: number]: number[] } = {
  2080: [31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30],
  2081: [31, 31, 32, 32, 31, 30, 30, 30, 29, 30, 29, 31],
  2082: [31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 30],
  2083: [31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 30],
  2084: [31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 30],
  2085: [31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 30],
  2086: [30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 30],
  2087: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30],
  2088: [31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30],
  2089: [31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31],
  2090: [30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 30],
  2091: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30],
  2092: [31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30],
  2093: [31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31],
  2094: [31, 31, 32, 31, 31, 30, 30, 30, 29, 30, 29, 30],
  2095: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30],
  2096: [31, 32, 31, 32, 31, 30, 30, 29, 30, 29, 30, 30],
  2097: [31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31],
  2098: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30],
  2099: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30],
  2100: [31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 30],
};

// Reference date: 2000/01/01 AD = 2056/09/17 BS
const referenceAD = new Date(2000, 0, 1); // January 1, 2000
const referenceBSYear = 2056;
const referenceBSMonth = 9;
const referenceBSDay = 17;

/**
 * Convert AD date to BS date
 */
export const adToBS = (
  adDate: Date
): { year: number; month: number; day: number } => {
  const diffDays = Math.floor(
    (adDate.getTime() - referenceAD.getTime()) / (1000 * 60 * 60 * 24)
  );

  let bsYear = referenceBSYear;
  let bsMonth = referenceBSMonth;
  let bsDay = referenceBSDay + diffDays;

  // Adjust for days overflow/underflow
  while (bsDay > (nepaliCalendarData[bsYear]?.[bsMonth - 1] || 30)) {
    bsDay -= nepaliCalendarData[bsYear]?.[bsMonth - 1] || 30;
    bsMonth++;
    if (bsMonth > 12) {
      bsMonth = 1;
      bsYear++;
    }
  }

  while (bsDay <= 0) {
    bsMonth--;
    if (bsMonth <= 0) {
      bsMonth = 12;
      bsYear--;
    }
    bsDay += nepaliCalendarData[bsYear]?.[bsMonth - 1] || 30;
  }

  return { year: bsYear, month: bsMonth, day: bsDay };
};

/**
 * Convert BS date to AD date
 */
export const bsToAD = (
  bsYear: number,
  bsMonth: number,
  bsDay: number
): Date => {
  let totalDays = 0;

  // Calculate days from reference BS date to target BS date
  if (bsYear >= referenceBSYear) {
    // Forward calculation
    for (let year = referenceBSYear; year < bsYear; year++) {
      const yearData = nepaliCalendarData[year];
      if (yearData) {
        totalDays += yearData.reduce((sum, days) => sum + days, 0);
      } else {
        totalDays += 365; // Fallback
      }
    }

    for (let month = referenceBSMonth; month < bsMonth; month++) {
      totalDays += nepaliCalendarData[referenceBSYear]?.[month - 1] || 30;
    }

    totalDays += bsDay - referenceBSDay;
  } else {
    // Backward calculation
    for (let year = bsYear; year < referenceBSYear; year++) {
      const yearData = nepaliCalendarData[year];
      if (yearData) {
        totalDays -= yearData.reduce((sum, days) => sum + days, 0);
      } else {
        totalDays -= 365; // Fallback
      }
    }

    for (let month = bsMonth; month < referenceBSMonth; month++) {
      totalDays -= nepaliCalendarData[bsYear]?.[month - 1] || 30;
    }

    totalDays -= referenceBSDay - bsDay;
  }

  const resultDate = new Date(referenceAD);
  resultDate.setDate(resultDate.getDate() + totalDays);
  return resultDate;
};

/**
 * Format BS date as YYYY/MM/DD
 */
export const formatBSDate = (
  bsYear: number,
  bsMonth: number,
  bsDay: number
): string => {
  return `${bsYear}/${String(bsMonth).padStart(2, "0")}/${String(
    bsDay
  ).padStart(2, "0")}`;
};

/**
 * Parse BS date string (YYYY/MM/DD or YYYY-MM-DD)
 */
export const parseBSDate = (
  dateString: string
): { year: number; month: number; day: number } | null => {
  const parts = dateString.split(/[\/\-]/);
  if (parts.length !== 3) return null;

  const year = parseInt(parts[0]);
  const month = parseInt(parts[1]);
  const day = parseInt(parts[2]);

  if (isNaN(year) || isNaN(month) || isNaN(day)) return null;
  if (month < 1 || month > 12) return null;
  if (day < 1 || day > 32) return null;

  return { year, month, day };
};

/**
 * Get current BS date
 */
export const getCurrentBSDate = (): {
  year: number;
  month: number;
  day: number;
} => {
  return adToBS(new Date());
};

/**
 * Validate BS date
 */
export const isValidBSDate = (
  bsYear: number,
  bsMonth: number,
  bsDay: number
): boolean => {
  if (bsMonth < 1 || bsMonth > 12) return false;

  const yearData = nepaliCalendarData[bsYear];
  if (!yearData) return false;

  const maxDays = yearData[bsMonth - 1];
  return bsDay >= 1 && bsDay <= maxDays;
};

/**
 * Get days in a BS month
 */
export const getBSDaysInMonth = (bsYear: number, bsMonth: number): number => {
  const yearData = nepaliCalendarData[bsYear];
  return yearData?.[bsMonth - 1] || 30;
};

/**
 * Nepali month names
 */
export const nepaliMonthNames = [
  "बैशाख",
  "जेठ",
  "आषाढ",
  "श्रावण",
  "भाद्र",
  "आश्विन",
  "कार्तिक",
  "मंसिर",
  "पौष",
  "माघ",
  "फाल्गुन",
  "चैत्र",
];

/**
 * English month names for BS
 */
export const englishBSMonthNames = [
  "Baisakh",
  "Jestha",
  "Ashadh",
  "Shrawan",
  "Bhadra",
  "Ashwin",
  "Kartik",
  "Mangsir",
  "Poush",
  "Magh",
  "Falgun",
  "Chaitra",
];
