const { DataTypes } = require('sequelize');

/**
 * Migration to add designation column to persons table
 * This column is used for staff and non_teaching personnel
 */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Check if designation column already exists
      const tableDescription = await queryInterface.describeTable('persons');
      if (tableDescription.designation) {
        console.log('designation column already exists, skipping migration');
        await transaction.commit();
        return;
      }

      // Add designation column
      await queryInterface.addColumn('persons', 'designation', {
        type: DataTypes.STRING,
        allowNull: true,
      }, { transaction });

      await transaction.commit();
      console.log('Successfully added designation column to persons table');
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      await queryInterface.removeColumn('persons', 'designation', { transaction });
      await transaction.commit();
      console.log('Successfully removed designation column from persons table');
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
