import IdentityCard from "./IdentityCard";
import { IdCardProps, StudentData } from "./types";

const Layout2 = ({
  id,
  schoolName,
  subname,
  contact,
  logo,
  image,
  studentName,
  address,
  studentclass,
  phone,
  roll,
  bgColor,
  personType,
  parentName,
  department,
  fieldVisibilityConfig,
  validity,
  principalSignature,
}: IdCardProps) => {
  const data: StudentData = {
    companyLogo: logo,
    image: image,
    schoolName: schoolName,
    location: subname,
    contact: contact,
    studentName: studentName,
    class: studentclass || "",
    rollNo: roll,
    parentName: parentName || address,
    contactNo: phone,
    personType: personType,
    department: department,
    fieldVisibilityConfig: fieldVisibilityConfig,
    validity: validity,
    principalSignature: principalSignature,
  };

  return (
    <div data-card-id={id}>
      <IdentityCard bgColor={bgColor} data={data} />
    </div>
  );
};

export default Layout2;
