import BackgroundImage from "./BackgroundImage";
import { BgColorType } from "./types";
import ProfileImage from "../../../common/ProfileImage";

interface CardHeaderProps {
  bgColor: BgColorType;
  logo: string;
  studentImage: string;
  studentName: string;
  personType: "student" | "staff" | "non_teaching";
}

const CardHeader = ({
  bgColor,
  logo,
  studentImage,
  studentName,
  personType,
}: CardHeaderProps) => {
  // Debug logging for image URL
  console.log(
    `Layout2 CardHeader - Student: ${studentName}, Image URL: ${studentImage}`
  );

  return (
    <div className="relative h-[130px]  mb-0 flex-shrink-0">
      <BackgroundImage bgColor={bgColor} />
      <img
        src={logo}
        alt="School Logo"
        className="object-contain absolute bg-white w-[48px] h-[48px] top-3 left-8 rounded-full"
      />
      <div className="absolute w-[80px] h-[80px] -translate-y-[86px] translate-x-[73px] rounded-full overflow-hidden">
        <ProfileImage
          src={studentImage}
          name={studentName}
          designation={personType}
          className="w-full h-full"
          shape="circle"
          showInitials={true}
        />
      </div>
    </div>
  );
};

export default CardHeader;
