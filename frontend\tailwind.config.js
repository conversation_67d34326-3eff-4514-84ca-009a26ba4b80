/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        student: "#FF6B35",
        staff: "#4A90E2",
        "non-teaching": "#52C41A",
        "birta-orange": "#FF6B35",
        "birta-blue": "#4A90E2",
        "birta-green": "#52C41A",
      },
      animation: {
        fadeIn: "fadeIn 0.3s ease-in-out",
        pulse: "pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0", transform: "translateY(-10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
      },
      animationDelay: {
        2000: "2s",
      },
    },
  },
  plugins: [],
};
