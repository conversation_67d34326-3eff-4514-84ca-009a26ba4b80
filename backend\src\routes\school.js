const express = require("express");
const { body, validationResult } = require("express-validator");
const { School } = require("../models");
const {
  schoolAssetUpload,
  handleMulterError,
} = require("../middleware/upload");
const path = require("path");

const router = express.Router();

// Get all schools (base route for backward compatibility)
router.get("/", async (req, res) => {
  try {
    const schools = await School.findAll({
      order: [["name", "ASC"]],
    });

    res.json(schools);
  } catch (error) {
    console.error("GET /api/school - Error:", error.message);
    res.status(500).json({ error: "Failed to fetch schools" });
  }
});

// Get all schools
router.get("/all", async (req, res) => {
  try {
    const schools = await School.findAll({
      order: [["name", "ASC"]],
    });

    res.json(schools);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch schools" });
  }
});

// Get school configuration (legacy endpoint - returns first school)
router.get("/config", async (req, res) => {
  try {
    let school = await School.findOne();

    if (!school) {
      // Create default school if none exists
      school = await School.create({
        name: "BIRTA GLOBAL SCHOOL",
        address: "Aduwa Bridge, Atthi Sadan Road, Birtamode",
        phone: "+977-9852672234",
        email: "<EMAIL>",
      });
    }

    res.json(school);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch school configuration" });
  }
});

// Get specific school by ID
router.get("/:id", async (req, res) => {
  try {
    const school = await School.findByPk(req.params.id);

    if (!school) {
      return res.status(404).json({ error: "School not found" });
    }

    res.json(school);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch school" });
  }
});

// Create new school
router.post(
  "/",
  [
    body("name").notEmpty().withMessage("School name is required"),
    body("address").notEmpty().withMessage("Address is required"),
    body("phone").notEmpty().withMessage("Phone is required"),
    body("email").optional(),
    body("validity_date")
      .optional()
      .isISO8601()
      .withMessage("Validity date must be a valid date (YYYY-MM-DD)"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const school = await School.create(req.body);
      res.status(201).json(school);
    } catch (error) {
      if (error.name === "SequelizeUniqueConstraintError") {
        return res
          .status(400)
          .json({ error: "School with this email already exists" });
      }
      res.status(500).json({ error: "Failed to create school" });
      console.log(error);
    }
  }
);

// Update specific school
router.put(
  "/:id",
  [
    body("name").notEmpty().withMessage("School name is required"),
    body("address").notEmpty().withMessage("Address is required"),
    body("phone").notEmpty().withMessage("Phone is required"),
    body("email")
      .optional()
      .isEmail()
      .withMessage("Must be a valid email address"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const school = await School.findByPk(req.params.id);
      if (!school) {
        return res.status(404).json({ error: "School not found" });
      }

      await school.update(req.body);
      res.json(school);
    } catch (error) {
      if (error.name === "SequelizeUniqueConstraintError") {
        return res
          .status(400)
          .json({ error: "School with this email already exists" });
      }
      res.status(500).json({ error: "Failed to update school" });
    }
  }
);

// Delete school
router.delete("/:id", async (req, res) => {
  try {
    const school = await School.findByPk(req.params.id);
    if (!school) {
      return res.status(404).json({ error: "School not found" });
    }

    // Check if school has associated persons
    const { Person } = require("../models");
    const personCount = await Person.count({
      where: { school_id: req.params.id },
    });

    if (personCount > 0) {
      return res.status(400).json({
        error:
          "Cannot delete school with associated persons. Please transfer or delete all persons first.",
      });
    }

    await school.destroy();
    res.json({ message: "School deleted successfully" });
  } catch (error) {
    res.status(500).json({ error: "Failed to delete school" });
  }
});

// Update school configuration
router.put(
  "/config",
  [
    body("name").notEmpty().withMessage("School name is required"),
    body("address").notEmpty().withMessage("Address is required"),
    body("phone").notEmpty().withMessage("Phone is required"),
    body("email")
      .optional()
      .isEmail()
      .withMessage("Must be a valid email address"),
    body("validity_date")
      .optional()
      .isISO8601()
      .withMessage("Validity date must be a valid date (YYYY-MM-DD)"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      let school = await School.findOne();

      if (!school) {
        school = await School.create(req.body);
      } else {
        await school.update(req.body);
      }

      res.json(school);
    } catch (error) {
      res.status(500).json({ error: "Failed to update school configuration" });
    }
  }
);

// Upload logo for specific school
router.post(
  "/:id/logo",
  schoolAssetUpload.single("logo"),
  handleMulterError,
  async (req, res) => {
    try {
      const school = await School.findByPk(req.params.id);

      if (!school) {
        return res.status(404).json({ error: "School not found" });
      }

      if (!req.file) {
        return res.status(400).json({ error: "No logo file provided" });
      }

      await school.update({ logo_path: req.file.filename });
      res.json({ message: "Logo uploaded successfully", school });
    } catch (error) {
      console.error("Logo upload error:", error);
      res.status(500).json({ error: "Failed to upload logo" });
    }
  }
);

// Upload stamp for specific school
router.post(
  "/:id/stamp",
  schoolAssetUpload.single("stamp"),
  handleMulterError,
  async (req, res) => {
    try {
      const school = await School.findByPk(req.params.id);

      if (!school) {
        return res.status(404).json({ error: "School not found" });
      }

      if (!req.file) {
        return res.status(400).json({ error: "No stamp file provided" });
      }

      await school.update({ stamp_path: req.file.filename });
      res.json({ message: "Stamp uploaded successfully", school });
    } catch (error) {
      console.error("Stamp upload error:", error);
      res.status(500).json({ error: "Failed to upload stamp" });
    }
  }
);

// Upload signature for specific school
router.post(
  "/:id/signature",
  schoolAssetUpload.single("signature"),
  handleMulterError,
  async (req, res) => {
    try {
      const school = await School.findByPk(req.params.id);

      if (!school) {
        return res.status(404).json({ error: "School not found" });
      }

      if (!req.file) {
        return res.status(400).json({ error: "No signature file provided" });
      }

      await school.update({ signature_path: req.file.filename });
      res.json({
        message: "Principal signature uploaded successfully",
        school,
      });
    } catch (error) {
      console.error("Signature upload error:", error);
      res.status(500).json({ error: "Failed to upload signature" });
    }
  }
);

// Update validity date for specific school
router.put(
  "/:id/validity",
  [
    body("validity_date")
      .notEmpty()
      .withMessage("Validity date is required")
      .isISO8601()
      .withMessage("Validity date must be a valid date (YYYY-MM-DD)"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const school = await School.findByPk(req.params.id);

      if (!school) {
        return res.status(404).json({ error: "School not found" });
      }

      await school.update({ validity_date: req.body.validity_date });
      res.json({
        message: "Validity date updated successfully",
        school,
        validity_date: req.body.validity_date,
      });
    } catch (error) {
      console.error("Validity date update error:", error);
      res.status(500).json({ error: "Failed to update validity date" });
    }
  }
);

// Upload school assets (logo, stamp, signature) - Legacy endpoint
router.post(
  "/assets",
  schoolAssetUpload.fields([
    { name: "logo", maxCount: 1 },
    { name: "stamp", maxCount: 1 },
    { name: "signature", maxCount: 1 },
  ]),
  handleMulterError,
  async (req, res) => {
    try {
      let school = await School.findOne();

      if (!school) {
        return res
          .status(404)
          .json({ error: "School configuration not found" });
      }

      const files = req.files;
      const updates = {};

      if (files.logo) {
        updates.logo_path = files.logo[0].filename;
      }

      if (files.stamp) {
        updates.stamp_path = files.stamp[0].filename;
      }

      if (files.signature) {
        updates.signature_path = files.signature[0].filename;
      }

      await school.update(updates);
      res.json({ message: "Assets uploaded successfully", school });
    } catch (error) {
      res.status(500).json({ error: "Failed to upload assets" });
    }
  }
);

// Serve school assets
router.get("/assets/:type/:filename", (req, res) => {
  const { type, filename } = req.params;

  if (!["logo", "stamp", "signature"].includes(type)) {
    return res.status(400).json({ error: "Invalid asset type" });
  }

  const filePath = path.join(
    __dirname,
    "../../uploads/school-assets",
    filename
  );
  res.sendFile(filePath);
});

module.exports = router;
