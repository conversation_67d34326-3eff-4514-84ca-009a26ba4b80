import React, {
  create<PERSON>ontext,
  use<PERSON>ontext,
  useState,
  useEffect,
  use<PERSON><PERSON>back,
  ReactNode,
} from "react";
import axios from "axios";

interface User {
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role: "admin" | "user" | "school_admin";
  school_id?: number;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  School?: {
    id: number;
    name: string;
    address?: string;
    phone?: string;
    email?: string;
  };
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  updateProfile: (profileData: UpdateProfileData) => Promise<void>;
  clearError: () => void;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
  role?: "admin" | "user" | "school_admin";
  school_id?: number;
}

interface UpdateProfileData {
  first_name?: string;
  last_name?: string;
  email?: string;
}

interface AuthProviderProps {
  children: ReactNode;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

// Configure axios defaults
axios.defaults.withCredentials = true;

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Set up axios interceptor for token management
  useEffect(() => {
    const token = localStorage.getItem("accessToken");
    if (token) {
      axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    }

    // Response interceptor to handle token expiration
    const responseInterceptor = axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (
          error.response?.status === 401 &&
          error.response?.data?.code === "TOKEN_EXPIRED"
        ) {
          // Token expired, clear auth state
          localStorage.removeItem("accessToken");
          delete axios.defaults.headers.common["Authorization"];
          setUser(null);
          setError("Session expired. Please login again.");
        }
        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, []);

  // Check if user is authenticated on app load
  const checkAuth = useCallback(async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("accessToken");

      if (!token) {
        setLoading(false);
        return;
      }

      // Verify token by fetching user profile
      const response = await axios.get(`${API_BASE_URL}/api/auth/profile`);
      setUser(response.data.user);
      setError(null);
    } catch (error: any) {
      console.error("Auth check failed:", error);
      // Clear invalid token
      localStorage.removeItem("accessToken");
      delete axios.defaults.headers.common["Authorization"];
      setUser(null);

      if (error.response?.status !== 401) {
        setError("Failed to verify authentication");
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // Login function
  const login = async (username: string, password: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
        username,
        password,
      });

      const { user: userData, accessToken } = response.data;

      // Store token
      localStorage.setItem("accessToken", accessToken);
      axios.defaults.headers.common["Authorization"] = `Bearer ${accessToken}`;

      setUser(userData);
    } catch (error: any) {
      console.error("Login error:", error);
      const errorMessage = error.response?.data?.error || "Login failed";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (userData: RegisterData): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post(
        `${API_BASE_URL}/api/auth/register`,
        userData
      );

      const { user: newUser, accessToken } = response.data;

      // Store token
      localStorage.setItem("accessToken", accessToken);
      axios.defaults.headers.common["Authorization"] = `Bearer ${accessToken}`;

      setUser(newUser);
    } catch (error: any) {
      console.error("Registration error:", error);
      const errorMessage = error.response?.data?.error || "Registration failed";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      setLoading(true);

      // Call logout endpoint to clear server-side cookies
      await axios.post(`${API_BASE_URL}/api/auth/logout`);
    } catch (error) {
      console.error("Logout error:", error);
      // Continue with client-side cleanup even if server call fails
    } finally {
      // Clear client-side auth state
      localStorage.removeItem("accessToken");
      delete axios.defaults.headers.common["Authorization"];
      setUser(null);
      setError(null);
      setLoading(false);
    }
  };

  // Refresh user profile
  const refreshProfile = async (): Promise<void> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/auth/profile`);
      setUser(response.data.user);
      setError(null);
    } catch (error: any) {
      console.error("Profile refresh error:", error);
      const errorMessage =
        error.response?.data?.error || "Failed to refresh profile";
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Update user profile
  const updateProfile = async (
    profileData: UpdateProfileData
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.put(
        `${API_BASE_URL}/api/auth/profile`,
        profileData
      );
      setUser(response.data.user);
    } catch (error: any) {
      console.error("Profile update error:", error);
      const errorMessage =
        error.response?.data?.error || "Failed to update profile";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  // Check authentication on mount
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const value: AuthContextType = {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    refreshProfile,
    updateProfile,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export type { User, RegisterData, UpdateProfileData, AuthContextType };
