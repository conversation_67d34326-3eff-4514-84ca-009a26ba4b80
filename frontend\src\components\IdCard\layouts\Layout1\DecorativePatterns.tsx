// Decorative patterns for Layout1
const DecorativePatterns = () => {
  return (
    <>
      {/* Left Decorative Patterns */}
      <div className="absolute">
        <div className="absolute rotate-90 left-[3px] top-1 w-8 h-8">
          <svg
            width="32"
            height="32"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <polygon
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
              fill="transparent"
              stroke="green"
              strokeWidth="2"
            />
          </svg>
        </div>
        <div className="absolute -rotate-90 -left-2  z-10 top-8 w-14 h-14">
          <svg
            width="70"
            height="70"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <polygon
              points="50,10 90,35 90,85 10,85 10,35"
              fill="transparent"
              stroke="#00a54f"
              strokeWidth="1"
            />
          </svg>
        </div>
        <div className="absolute rotate-90 -left-10 top-3 w-20 h-20">
          <svg
            width="100"
            height="100"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <polygon
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
              fill="transparent"
              stroke="red"
              strokeWidth="1"
            />
          </svg>
        </div>
        <div className="absolute rotate-90 -left-7 top-[74px] w-8 h-8">
          <svg
            width="32"
            height="32"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <polygon
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
              fill="transparent"
              stroke="#f3ee50"
              strokeWidth="1"
            />
          </svg>
        </div>
      </div>

      {/* Right Decorative Patterns */}
      <div className="absolute right-0">
        <div className="absolute -rotate-90 right-[16px] top-1 w-8 h-8">
          <svg
            width="32"
            height="32"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <polygon
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
              fill="transparent"
              stroke="green"
              strokeWidth="2"
            />
          </svg>
        </div>
        <div className="absolute rotate-90 right-1 z-10 top-5 w-14 h-14">
          <svg
            width="70"
            height="70"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <polygon
              points="50,10 90,35 90,85 10,85 10,35"
              fill="transparent"
              stroke="#00a54f"
              strokeWidth="1"
            />
          </svg>
        </div>
        <div className="absolute -rotate-90 -right-7 top-8 w-20 h-20">
          <svg
            width="100"
            height="100"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <polygon
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
              fill="transparent"
              stroke="red"
              strokeWidth="1"
            />
          </svg>
        </div>
        <div className="absolute -rotate-90 -right-4 top-[74px] w-8 h-8">
          <svg
            width="32"
            height="32"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <polygon
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
              fill="transparent"
              stroke="#f3ee50"
              strokeWidth="1"
            />
          </svg>
        </div>
      </div>
    </>
  );
};

export default DecorativePatterns;
