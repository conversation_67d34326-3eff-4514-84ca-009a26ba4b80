"use strict";

const { DataTypes } = require("sequelize");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log("Adding date_of_birth column to persons table...");

      // Add date_of_birth column to persons table
      await queryInterface.addColumn(
        "persons",
        "date_of_birth",
        {
          type: DataTypes.DATEONLY,
          allowNull: true,
          comment: "Date of birth for the person",
        },
        { transaction }
      );

      await transaction.commit();
      console.log("Successfully added date_of_birth to persons table");
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log("Removing date_of_birth column from persons table...");

      // Remove date_of_birth column from persons table
      await queryInterface.removeColumn("persons", "date_of_birth", {
        transaction,
      });

      await transaction.commit();
      console.log("Successfully removed date_of_birth from persons table");
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
