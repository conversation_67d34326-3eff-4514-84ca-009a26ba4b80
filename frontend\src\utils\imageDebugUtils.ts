/* eslint-disable */
/**
 * Utility functions for debugging image loading issues in production
 */

export interface ImageDebugInfo {
  url: string;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  corsEnabled: boolean;
  contentType?: string;
  contentLength?: string;
  lastModified?: string;
  etag?: string;
  cacheControl?: string;
}

/**
 * Test image URL accessibility and CORS configuration
 */
export const debugImageUrl = async (url: string): Promise<ImageDebugInfo> => {
  try {
    const response = await fetch(url, {
      method: "HEAD",
      mode: "cors",
      credentials: "omit",
      headers: {
        Accept: "image/*",
      },
    });

    const headers: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });

    return {
      url,
      status: response.status,
      statusText: response.statusText,
      headers,
      corsEnabled: headers["access-control-allow-origin"] !== undefined,
      contentType: headers["content-type"],
      contentLength: headers["content-length"],
      lastModified: headers["last-modified"],
      etag: headers["etag"],
      cacheControl: headers["cache-control"],
    };
  } catch (error) {
    console.error("Failed to debug image URL:", error);
    throw error;
  }
};

/**
 * Test if an image can be loaded with different CORS settings
 */
export const testImageLoad = (
  url: string,
  crossOrigin?: string
): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();

    img.onload = () => {
      console.log(`Image loaded successfully: ${url}`);
      resolve(true);
    };

    img.onerror = (error) => {
      console.warn(`Image failed to load: ${url}`, error);
      resolve(false);
    };

    if (crossOrigin) {
      img.crossOrigin = crossOrigin;
    }

    img.src = url;
  });
};

/**
 * Get diagnostic information from the server
 */
export const getServerDiagnostic = async (imageUrl: string): Promise<any> => {
  try {
    // Extract filename from the image URL
    const filename = getImageFilename(imageUrl);

    // Determine if it's a photo or logo based on URL pattern
    const isPhoto = imageUrl.includes("/photos/");
    const isLogo = imageUrl.includes("/logos/");

    let diagnosticUrl: string;
    if (isPhoto) {
      diagnosticUrl = imageUrl.replace("/photos/", "/diagnostic/photos/");
    } else if (isLogo) {
      diagnosticUrl = imageUrl.replace("/logos/", "/diagnostic/logos/");
    } else {
      throw new Error("Unknown image type - not a photo or logo");
    }

    const response = await fetch(diagnosticUrl, {
      method: "GET",
      mode: "cors",
      credentials: "omit",
    });

    if (!response.ok) {
      throw new Error(
        `Diagnostic request failed: ${response.status} ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Failed to get server diagnostic:", error);
    throw error;
  }
};

/**
 * Comprehensive image debugging function
 */
export const debugImageIssues = async (url: string, personName: string) => {
  console.group(`🔍 Debugging image issues for ${personName}`);

  try {
    // Test basic URL accessibility
    console.log("1. Testing URL accessibility...");
    const debugInfo = await debugImageUrl(url);
    console.log("URL Debug Info:", debugInfo);

    if (debugInfo.status !== 200) {
      console.error(
        `❌ HTTP Error: ${debugInfo.status} ${debugInfo.statusText}`
      );
    } else {
      console.log("✅ URL is accessible");
    }

    // Get server diagnostic information
    console.log("2. Getting server diagnostic...");
    try {
      const serverDiagnostic = await getServerDiagnostic(url);
      console.log("Server Diagnostic:", serverDiagnostic);

      if (!serverDiagnostic.exists) {
        console.error("❌ File does not exist on server");
        console.log("Server paths:", {
          requestedPath: serverDiagnostic.requestedPath,
          uploadsExists: serverDiagnostic.directories?.uploadsExists,
          photosExists: serverDiagnostic.directories?.photosExists,
          schoolAssetsExists: serverDiagnostic.directories?.schoolAssetsExists,
        });
      } else {
        console.log("✅ File exists on server");
        console.log("File stats:", serverDiagnostic.stats);
      }
    } catch (diagnosticError) {
      console.warn("⚠️ Could not get server diagnostic:", diagnosticError);
    }

    // Test CORS configuration
    console.log("3. Testing CORS configuration...");
    if (debugInfo.corsEnabled) {
      console.log("✅ CORS headers present");
      console.log(
        "CORS Origin:",
        debugInfo.headers["access-control-allow-origin"]
      );
    } else {
      console.warn("⚠️ No CORS headers found");
    }

    // Test image loading with different CORS settings
    console.log("4. Testing image loading...");

    const loadWithoutCors = await testImageLoad(url);
    const loadWithCors = await testImageLoad(url, "anonymous");

    console.log("Load without CORS:", loadWithoutCors ? "✅" : "❌");
    console.log("Load with CORS:", loadWithCors ? "✅" : "❌");

    // Provide recommendations
    console.log("5. Recommendations:");
    if (!debugInfo.corsEnabled) {
      console.warn("- Add proper CORS headers to the server");
    }
    if (debugInfo.status !== 200) {
      console.warn("- Check if the image file exists on the server");
      console.warn("- Verify the file path and permissions");
    }
    if (!loadWithCors && debugInfo.corsEnabled) {
      console.warn("- CORS headers might be incorrectly configured");
      console.warn("- Check if the server allows the current origin");
    }
  } catch (error) {
    console.error("❌ Failed to debug image:", error);

    if (error instanceof TypeError && error.message.includes("CORS")) {
      console.warn("This appears to be a CORS-related error");
      console.warn("Recommendations:");
      console.warn("- Ensure the server has proper CORS headers");
      console.warn("- Check if the API server is running");
      console.warn("- Verify the image URL is correct");
    }
  }

  console.groupEnd();
};

/**
 * Quick image URL validator
 */
export const isValidImageUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const validProtocols = ["http:", "https:", "data:"];
    return validProtocols.includes(urlObj.protocol);
  } catch {
    return false;
  }
};

/**
 * Extract filename from image URL
 */
export const getImageFilename = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    return pathname.split("/").pop() || "unknown";
  } catch {
    return "invalid-url";
  }
};

/**
 * Check if URL is from the same origin
 */
export const isSameOrigin = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return urlObj.origin === window.location.origin;
  } catch {
    return false;
  }
};
