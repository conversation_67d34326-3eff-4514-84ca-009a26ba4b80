const { User } = require('./src/models');
const sequelize = require('./src/config/database');
const { hashPassword } = require('./src/utils/auth');

async function resetAdminPassword() {
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('Database connection established.');

    // Find admin user
    const adminUser = await User.findOne({ 
      where: { username: 'admin' } 
    });
    
    if (!adminUser) {
      console.log('Admin user not found!');
      return;
    }

    // Hash new password
    const newPassword = 'admin123';
    const password_hash = await hashPassword(newPassword);

    // Update admin user password
    await adminUser.update({
      password_hash: password_hash
    });

    console.log('Admin password reset successfully!');
    console.log('Username: admin');
    console.log('New Password: admin123');
    console.log('Email:', adminUser.email);

    await sequelize.close();
  } catch (error) {
    console.error('Error resetting admin password:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  resetAdminPassword();
}

module.exports = { resetAdminPassword };
