import React, { useState } from 'react';
import { ImageEditorModal } from './index';

/**
 * Test component for Image Editor functionality
 */
const ImageEditorTest: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [processedFile, setProcessedFile] = useState<File | null>(null);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
      setShowEditor(true);
    }
  };

  const handleEditorSave = (file: File) => {
    setProcessedFile(file);
    setShowEditor(false);
    console.log('Processed file:', file);
  };

  const handleEditorClose = () => {
    setShowEditor(false);
  };

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Image Editor Test</h1>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select an image to test the editor:
          </label>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>

        {selectedFile && (
          <div className="border border-gray-200 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">Original File:</h3>
            <p className="text-sm text-gray-600">
              Name: {selectedFile.name}<br />
              Size: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB<br />
              Type: {selectedFile.type}
            </p>
          </div>
        )}

        {processedFile && (
          <div className="border border-green-200 rounded-lg p-4 bg-green-50">
            <h3 className="font-medium text-green-900 mb-2">Processed File:</h3>
            <p className="text-sm text-green-700">
              Name: {processedFile.name}<br />
              Size: {(processedFile.size / 1024 / 1024).toFixed(2)} MB<br />
              Type: {processedFile.type}
            </p>
            
            <div className="mt-4">
              <img
                src={URL.createObjectURL(processedFile)}
                alt="Processed"
                className="max-w-full h-auto rounded-md border border-gray-200"
                style={{ maxHeight: '300px' }}
              />
            </div>
          </div>
        )}

        {selectedFile && (
          <button
            onClick={() => setShowEditor(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Open Image Editor
          </button>
        )}
      </div>

      {selectedFile && (
        <ImageEditorModal
          file={selectedFile}
          isOpen={showEditor}
          onClose={handleEditorClose}
          onSave={handleEditorSave}
          aspectRatio={1}
          maxWidth={800}
          maxHeight={800}
        />
      )}
    </div>
  );
};

export default ImageEditorTest;
