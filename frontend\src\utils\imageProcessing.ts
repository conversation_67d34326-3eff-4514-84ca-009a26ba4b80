/**
 * Image processing utilities for cropping, brightness, contrast, and saturation adjustments
 */

export interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ImageFilters {
  brightness: number; // -100 to 100
  contrast: number;   // -100 to 100
  saturation: number; // -100 to 100
}

export interface ProcessedImageResult {
  canvas: HTMLCanvasElement;
  dataUrl: string;
  blob: Blob;
}

/**
 * Load an image from a file or URL
 */
export const loadImage = (src: string | File): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('Failed to load image'));
    
    if (src instanceof File) {
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target?.result as string;
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(src);
    } else {
      img.src = src;
    }
  });
};

/**
 * Apply filters to canvas context
 */
export const applyFilters = (ctx: CanvasRenderingContext2D, filters: ImageFilters): void => {
  const { brightness, contrast, saturation } = filters;
  
  // Convert values to CSS filter format
  const brightnessValue = 100 + brightness; // 0-200%
  const contrastValue = 100 + contrast;     // 0-200%
  const saturationValue = 100 + saturation; // 0-200%
  
  ctx.filter = `brightness(${brightnessValue}%) contrast(${contrastValue}%) saturate(${saturationValue}%)`;
};

/**
 * Crop and process an image with filters
 */
export const processImage = async (
  image: HTMLImageElement,
  cropArea: CropArea,
  filters: ImageFilters,
  outputWidth?: number,
  outputHeight?: number
): Promise<ProcessedImageResult> => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }

  // Set canvas dimensions to crop area or specified output size
  canvas.width = outputWidth || cropArea.width;
  canvas.height = outputHeight || cropArea.height;

  // Apply filters
  applyFilters(ctx, filters);

  // Draw the cropped image
  ctx.drawImage(
    image,
    cropArea.x,
    cropArea.y,
    cropArea.width,
    cropArea.height,
    0,
    0,
    canvas.width,
    canvas.height
  );

  // Convert to data URL and blob
  const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
  
  const blob = await new Promise<Blob>((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob);
      } else {
        reject(new Error('Failed to create blob'));
      }
    }, 'image/jpeg', 0.9);
  });

  return { canvas, dataUrl, blob };
};

/**
 * Get image dimensions
 */
export const getImageDimensions = (image: HTMLImageElement): { width: number; height: number } => {
  return {
    width: image.naturalWidth,
    height: image.naturalHeight
  };
};

/**
 * Calculate crop area to maintain aspect ratio
 */
export const calculateAspectRatioCrop = (
  imageWidth: number,
  imageHeight: number,
  aspectRatio: number
): CropArea => {
  let cropWidth = imageWidth;
  let cropHeight = imageHeight;
  
  const imageAspectRatio = imageWidth / imageHeight;
  
  if (imageAspectRatio > aspectRatio) {
    // Image is wider than desired aspect ratio
    cropWidth = imageHeight * aspectRatio;
  } else {
    // Image is taller than desired aspect ratio
    cropHeight = imageWidth / aspectRatio;
  }
  
  return {
    x: (imageWidth - cropWidth) / 2,
    y: (imageHeight - cropHeight) / 2,
    width: cropWidth,
    height: cropHeight
  };
};

/**
 * Validate crop area bounds
 */
export const validateCropArea = (
  cropArea: CropArea,
  imageWidth: number,
  imageHeight: number,
  minSize: number = 50
): CropArea => {
  const validated = { ...cropArea };
  
  // Ensure minimum size
  validated.width = Math.max(validated.width, minSize);
  validated.height = Math.max(validated.height, minSize);
  
  // Ensure crop area doesn't exceed image bounds
  validated.x = Math.max(0, Math.min(validated.x, imageWidth - validated.width));
  validated.y = Math.max(0, Math.min(validated.y, imageHeight - validated.y));
  validated.width = Math.min(validated.width, imageWidth - validated.x);
  validated.height = Math.min(validated.height, imageHeight - validated.y);
  
  return validated;
};

/**
 * Convert file to data URL
 */
export const fileToDataUrl = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target?.result as string);
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
};

/**
 * Download processed image
 */
export const downloadImage = (dataUrl: string, filename: string = 'processed-image.jpg'): void => {
  const link = document.createElement('a');
  link.download = filename;
  link.href = dataUrl;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
