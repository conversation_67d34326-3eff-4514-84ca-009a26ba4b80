import React, { useState, useMemo, useCallback, useRef } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { MasterTableProps, TableData, TableState } from "./types";
import TableAction from "./TableAction";
import Pagination from "./Pagination";
import { debounce } from "lodash";

/**
 * Enhanced production-grade MasterTable component with advanced features
 */
export const EnhancedMasterTable = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  onRowSelect,
  showSelectAll = true,
  selectionMode = "multiple",
  actions = [],
  showActions = true,
  bulkActions = [],
  onRowClick,
  onRowDoubleClick,
  rowClassName,
  showBg = true,
  hasPadding = true,
  className = "",
  size = "md",
  variant = "default",
  showPagination = false,
  paginationDetails,
  onPageChange,
  onItemsPerPageChange,
  emptyStateMessage = "No data was found",
  emptyStateIcon = "mdi:database-off",
  emptyStateAction,
  sortable = false,
  onSort,
  sortConfig,
  searchable = false,
  searchPlaceholder = "Search...",
  onSearch,
  filters = [],
  onFilter,
  virtualized = false,
  rowHeight = 60,
  maxHeight,
  ariaLabel = "Data table",
  ariaDescription,
  resizable = false,
  reorderable = false,
  exportable = false,
  onExport,
  loadingRows = 5,
  skeleton = false,
}: MasterTableProps<T>) => {
  // State management
  const [tableState, setTableState] = useState<TableState>({
    selectedRows: new Set<string>(),
    sortConfig,
    filters,
    searchQuery: "",
  });

  const [searchQuery, setSearchQuery] = useState("");
  const tableRef = useRef<HTMLDivElement>(null);

  // Debounced search
  const debouncedSearch = useCallback(
    (query: string) => {
      const debouncedFn = debounce(() => {
        onSearch?.(query);
        setTableState((prev) => ({ ...prev, searchQuery: query }));
      }, 300);
      debouncedFn();
    },
    [onSearch]
  );

  // Handle search input
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const query = e.target.value;
      setSearchQuery(query);
      debouncedSearch(query);
    },
    [debouncedSearch]
  );

  // Row selection handlers
  const handleRowSelect = useCallback(
    (row: TableData<T>) => {
      setTableState((prev) => {
        const newSelectedRows = new Set(prev.selectedRows);

        if (selectionMode === "single") {
          newSelectedRows.clear();
          newSelectedRows.add(row._id);
        } else {
          if (newSelectedRows.has(row._id)) {
            newSelectedRows.delete(row._id);
          } else {
            newSelectedRows.add(row._id);
          }
        }

        onRowSelect?.(Array.from(newSelectedRows));
        return { ...prev, selectedRows: newSelectedRows };
      });
    },
    [selectionMode, onRowSelect]
  );

  const handleSelectAll = useCallback(() => {
    setTableState((prev) => {
      let newSelectedRows: Set<string>;

      if (prev.selectedRows.size === data.length) {
        newSelectedRows = new Set();
      } else {
        newSelectedRows = new Set(data.map((row) => row._id));
      }

      onRowSelect?.(Array.from(newSelectedRows));
      return { ...prev, selectedRows: newSelectedRows };
    });
  }, [data, onRowSelect]);

  // Sorting handler
  const handleSort = useCallback(
    (key: string) => {
      if (!sortable) return;

      const direction: "asc" | "desc" =
        tableState.sortConfig?.key === key &&
        tableState.sortConfig.direction === "asc"
          ? "desc"
          : "asc";

      const newSortConfig = { key, direction };
      setTableState((prev) => ({ ...prev, sortConfig: newSortConfig }));
      onSort?.(key, direction);
    },
    [sortable, tableState.sortConfig, onSort]
  );

  // Row click handlers
  const handleRowClick = useCallback(
    (row: TableData<T>, event: React.MouseEvent) => {
      // Prevent row click when clicking on action buttons or checkboxes
      if (
        (event.target as HTMLElement).closest(".table-action, .table-checkbox")
      ) {
        return;
      }
      onRowClick?.(row);
    },
    [onRowClick]
  );

  const handleRowDoubleClick = useCallback(
    (row: TableData<T>) => {
      onRowDoubleClick?.(row);
    },
    [onRowDoubleClick]
  );

  // Memoized computed values
  const visibleActions = useMemo(
    () => actions.filter((action) => action.show !== false),
    [actions]
  );

  const hasSelectedRows = tableState.selectedRows.size > 0;
  const isAllSelected =
    tableState.selectedRows.size === data.length && data.length > 0;
  const isIndeterminate = hasSelectedRows && !isAllSelected;

  // Table size classes
  const sizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base",
  };

  // Variant classes
  const variantClasses = {
    default: "",
    striped: "table-striped",
    bordered: "table-bordered",
  };

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <>
      {Array.from({ length: loadingRows }).map((_, index) => (
        <tr key={`skeleton-${index}`} className="animate-pulse">
          {showSelectAll && (
            <td className="px-6 py-4">
              <div className="h-4 w-4 bg-gray-200 rounded"></div>
            </td>
          )}
          {columns.map((column, colIndex) => (
            <td key={`skeleton-${index}-${colIndex}`} className="px-6 py-4">
              <div className="h-4 bg-gray-200 rounded w-full"></div>
            </td>
          ))}
          {showActions && visibleActions.length > 0 && (
            <td className="px-6 py-4">
              <div className="flex space-x-2">
                {visibleActions.map((_, actionIndex) => (
                  <div
                    key={actionIndex}
                    className="h-8 w-8 bg-gray-200 rounded"
                  ></div>
                ))}
              </div>
            </td>
          )}
        </tr>
      ))}
    </>
  );

  // Empty state component
  const EmptyState = () => (
    <tr>
      <td
        colSpan={
          columns.length + (showSelectAll ? 1 : 0) + (showActions ? 1 : 0)
        }
        className="px-6 py-12 text-center"
      >
        <div className="flex flex-col items-center justify-center space-y-4">
          <Icon icon={emptyStateIcon} className="w-16 h-16 text-gray-400" />
          <div>
            <p className="text-lg font-medium text-gray-900 mb-2">
              {emptyStateMessage}
            </p>
            {emptyStateAction && (
              <button
                onClick={emptyStateAction.onClick}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {emptyStateAction.label}
              </button>
            )}
          </div>
        </div>
      </td>
    </tr>
  );

  // Table header component
  const TableHeader = () => (
    <thead className="bg-gray-50">
      <tr>
        {showSelectAll && selectionMode === "multiple" && (
          <th className="px-6 py-3 text-left">
            <input
              type="checkbox"
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded table-checkbox"
              checked={isAllSelected}
              ref={(input) => {
                if (input) input.indeterminate = isIndeterminate;
              }}
              onChange={handleSelectAll}
              aria-label="Select all rows"
            />
          </th>
        )}

        {columns.map((column, index) => (
          <th
            key={`header-${index}`}
            className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
              column.sortable && sortable
                ? "cursor-pointer hover:bg-gray-100"
                : ""
            } ${column.className || ""}`}
            style={{ width: column.width }}
            onClick={() => column.sortable && handleSort(column.key)}
          >
            <div className="flex items-center space-x-1">
              <span>{column.header}</span>
              {column.sortable && sortable && (
                <Icon
                  icon={
                    tableState.sortConfig?.key === column.key
                      ? tableState.sortConfig.direction === "asc"
                        ? "mdi:arrow-up"
                        : "mdi:arrow-down"
                      : "mdi:unfold-more-horizontal"
                  }
                  className="w-4 h-4"
                />
              )}
            </div>
          </th>
        ))}

        {showActions && visibleActions.length > 0 && (
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </th>
        )}
      </tr>
    </thead>
  );

  return (
    <div className={`${className} space-y-4`}>
      {/* Search and Bulk Actions Bar */}
      {(searchable ||
        (hasSelectedRows && bulkActions.length > 0) ||
        exportable) && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {searchable && (
              <div className="relative">
                <Icon
                  icon="mdi:magnify"
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                />
                <input
                  type="text"
                  placeholder={searchPlaceholder}
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            )}

            {hasSelectedRows && bulkActions.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">
                  {tableState.selectedRows.size} selected
                </span>
                {bulkActions.map((action, index) => (
                  <button
                    key={index}
                    onClick={() => action.onClick?.(data[0])} // Bulk action
                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    {action.icon && (
                      <Icon icon={action.icon} className="w-4 h-4 mr-1" />
                    )}
                    {action.title}
                  </button>
                ))}
              </div>
            )}
          </div>

          {exportable && (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onExport?.("csv")}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <Icon icon="mdi:download" className="w-4 h-4 mr-1" />
                Export
              </button>
            </div>
          )}
        </div>
      )}

      {/* Table Container */}
      <div
        ref={tableRef}
        className={`overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg ${
          maxHeight ? "overflow-y-auto" : ""
        }`}
        style={{ maxHeight }}
        role="table"
        aria-label={ariaLabel}
      >
        <table
          className={`min-w-full divide-y divide-gray-200 ${sizeClasses[size]} ${variantClasses[variant]}`}
        >
          <TableHeader />

          <tbody className="bg-white divide-y divide-gray-200">
            {loading || skeleton ? (
              <LoadingSkeleton />
            ) : data.length === 0 ? (
              <EmptyState />
            ) : (
              data.map((row, index) => (
                <tr
                  key={`${row._id}-${index}`}
                  className={`
                    ${index % 2 === 0 && showBg ? "bg-gray-50" : ""}
                    ${tableState.selectedRows.has(row._id) ? "bg-blue-50" : ""}
                    ${onRowClick ? "cursor-pointer hover:bg-blue-50" : ""}
                    ${rowClassName ? rowClassName(row) : ""}
                    transition-colors
                  `}
                  onClick={(e) => handleRowClick(row, e)}
                  onDoubleClick={() => handleRowDoubleClick(row)}
                  style={{ height: virtualized ? rowHeight : undefined }}
                >
                  {showSelectAll && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded table-checkbox"
                        checked={tableState.selectedRows.has(row._id)}
                        onChange={(e) => {
                          e.stopPropagation();
                          handleRowSelect(row);
                        }}
                        aria-label={`Select row ${row._id}`}
                      />
                    </td>
                  )}

                  {columns.map((column, colIndex) => (
                    <td
                      key={`${row._id}-${colIndex}`}
                      className={`${
                        hasPadding ? "px-6" : ""
                      } py-4 whitespace-nowrap text-sm text-gray-900 ${
                        column.className || ""
                      }`}
                    >
                      {column.render
                        ? column.render(row[column.key], row)
                        : typeof row[column.key] === "boolean"
                        ? row[column.key]
                          ? "Yes"
                          : "No"
                        : row[column.key] ?? "N/A"}
                    </td>
                  ))}

                  {showActions && visibleActions.length > 0 && (
                    <TableAction row={row} actions={visibleActions} />
                  )}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {showPagination && paginationDetails && (
        <Pagination
          currentPage={paginationDetails.currentPage}
          onPageChange={onPageChange}
          onItemsPerPageChange={onItemsPerPageChange}
          itemsPerPage={paginationDetails.limit}
          totalItems={paginationDetails.totalCount || 0}
        />
      )}
    </div>
  );
};

export default EnhancedMasterTable;
