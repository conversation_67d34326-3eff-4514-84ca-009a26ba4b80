const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const cookieParser = require("cookie-parser");
const dotenv = require("dotenv");
const path = require("path");
const sequelize = require("./config/database");
const apiGateway = require("./middleware/apiGateway");
const { createLogger, requestLogger, errorLogger } = require("./utils/logger");
const { sanitizeRequest } = require("./middleware/validation");

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;
const logger = createLogger("app");

// Trust proxy for accurate IP addresses behind load balancers
app.set("trust proxy", 1);

// Health check endpoint (before any middleware for fastest response)
app.use(apiGateway.getHealthCheckMiddleware());

// Security middleware stack
app.use(...apiGateway.getSecurityMiddleware());

// Request preprocessing and logging
app.use(apiGateway.getLoggingMiddleware());
app.use(...apiGateway.getPreprocessingMiddleware());
app.use(requestLogger("api"));

// Request sanitization
app.use(sanitizeRequest);

// Body parsing middleware (consolidated)
app.use(
  express.json({
    limit: "200mb",
    verify: (req, res, buf) => {
      req.rawBody = buf; // Store raw body for webhook verification if needed
    },
  })
);
app.use(express.urlencoded({ extended: true, limit: "200mb" }));
app.use(cookieParser());

// Static file serving with CORS headers
app.use("/uploads", (req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept"
  );
  res.header("Cross-Origin-Resource-Policy", "cross-origin");
  next();
});
app.use("/uploads", express.static(path.join(__dirname, "../uploads")));

// Route imports
const personsRoutes = require("./routes/persons");
const schoolRoutes = require("./routes/school");
const cardsRoutes = require("./routes/cards");
const authRoutes = require("./routes/auth");

// API Routes
app.use("/api/auth", authRoutes);
app.use("/api/persons", personsRoutes);
app.use("/api/school", schoolRoutes);
app.use("/api/cards", cardsRoutes);

// 404 handler for undefined API endpoints
app.use("/api", (req, res, next) => {
  if (!res.headersSent) {
    logger.warn(`API endpoint not found: ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get("User-Agent"),
    });

    return res.status(404).json({
      error: "API endpoint not found",
      code: "ENDPOINT_NOT_FOUND",
      path: req.path,
      method: req.method,
    });
  }
  next();
});

// Error handling middleware (must be last)
app.use(errorLogger("error"));
app.use(apiGateway.getErrorHandlingMiddleware());

// Database connection and server startup
const startServer = async () => {
  try {
    logger.info("Starting School Card Application...");

    // Test database connection
    await sequelize.authenticate();
    logger.info("Database connection established successfully");

    // Sync database models
    await sequelize.sync({ force: false });
    logger.info("Database models synchronized");

    // Start HTTP server
    const server = app.listen(PORT, () => {
      logger.info(`🚀 Server running on port ${PORT}`, {
        port: PORT,
        environment: process.env.NODE_ENV || "development",
        nodeVersion: process.version,
      });
    });

    // Graceful shutdown handling
    const gracefulShutdown = async (signal) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`);

      server.close(async () => {
        try {
          await sequelize.close();
          logger.info("Database connection closed");

          // Close logger
          await require("./utils/logger").shutdown();

          logger.info("Graceful shutdown completed");
          process.exit(0);
        } catch (error) {
          logger.error("Error during graceful shutdown:", error);
          process.exit(1);
        }
      });

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error("Forced shutdown after timeout");
        process.exit(1);
      }, 30000);
    };

    // Handle shutdown signals
    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));

    // Handle uncaught exceptions
    process.on("uncaughtException", (error) => {
      logger.error("Uncaught Exception:", error);
      gracefulShutdown("UNCAUGHT_EXCEPTION");
    });

    process.on("unhandledRejection", (reason, promise) => {
      logger.error("Unhandled Rejection at:", promise, "reason:", reason);
      gracefulShutdown("UNHANDLED_REJECTION");
    });
  } catch (error) {
    logger.error("Failed to start server:", error);
    process.exit(1);
  }
};

// Start the server
startServer();

module.exports = app;
