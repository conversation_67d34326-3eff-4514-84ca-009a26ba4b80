import React, { useState, useEffect, useCallback } from "react";
import axios from "axios";
import { useSchool } from "../contexts/SchoolContext";
import { Statistics } from "../components/Statistics";
import { Breadcrumb, useBreadcrumb } from "../components/common";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useNavigate } from "react-router-dom";
import { Person } from "../services/personService";

interface Stats {
  total: number;
  breakdown: {
    student?: number;
    staff?: number;
    non_teaching?: number;
  };
}

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

const DashboardPage: React.FC = () => {
  const { selectedSchool } = useSchool();
  const [stats, setStats] = useState<Stats | null>(null);
  const [persons, setPersons] = useState<Person[]>([]);
  const [loading, setLoading] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);
  const { createBreadcrumb } = useBreadcrumb();
  const navigate = useNavigate();

  const fetchStats = useCallback(async () => {
    if (!selectedSchool) return;

    try {
      setStatsLoading(true);
      const response = await axios.get(`${API_BASE_URL}/api/cards/stats`, {
        params: { school_id: selectedSchool.id },
      });
      setStats(response.data);
    } catch (error) {
      console.error("Error fetching stats:", error);
    } finally {
      setStatsLoading(false);
    }
  }, [selectedSchool]);

  const fetchPersons = useCallback(async () => {
    if (!selectedSchool) return;

    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/api/persons`, {
        params: { school_id: selectedSchool.id, limit: 1000 },
      });
      setPersons(response.data.persons || []);
    } catch (error) {
      console.error("Error fetching persons:", error);
    } finally {
      setLoading(false);
    }
  }, [selectedSchool]);

  useEffect(() => {
    if (selectedSchool) {
      fetchStats();
      fetchPersons();
    }
  }, [selectedSchool, fetchStats, fetchPersons]);

  if (!selectedSchool) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-6xl mb-4">🏫</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No School Selected
          </h2>
          <p className="text-gray-600">
            Please select a school from the sidebar to view the dashboard.
          </p>
        </div>
      </div>
    );
  }

  // Create breadcrumb items
  const breadcrumbItems = [createBreadcrumb("Dashboard", "/", "mdi:home")];

  return (
    <div className="space-y-8">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbItems} />

      {/* Enhanced Header with Gradient Background */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 rounded-xl shadow-lg overflow-hidden">
        <div className="px-8 py-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <Icon icon="proicons:person" className="w-8 h-8" />
                <h1 className="text-3xl font-bold">{selectedSchool.name}</h1>
              </div>
              <p className="text-blue-100 mb-4">{selectedSchool.address}</p>
              <div className="flex items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <Icon icon="mdi:phone" className="w-4 h-4" />
                  <span>{selectedSchool.phone}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icon icon="mdi:email" className="w-4 h-4" />
                  <span>{selectedSchool.email}</span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 min-w-[120px]">
                <div className="text-4xl font-bold">
                  {statsLoading ? (
                    <div className="animate-pulse">--</div>
                  ) : (
                    stats?.total || 0
                  )}
                </div>
                <div className="text-sm text-blue-100">Total ID Cards</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Students Card */}
        <div className="group relative bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-400/10 to-orange-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-3 rounded-full bg-gradient-to-br from-orange-400 to-orange-600 shadow-lg">
                  <Icon icon="mdi:school" className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="text-3xl font-bold text-gray-900">
                    {statsLoading ? (
                      <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                    ) : (
                      stats?.breakdown?.student || 0
                    )}
                  </div>
                  <div className="text-sm font-medium text-orange-600">
                    Students
                  </div>
                </div>
              </div>
              <div className="text-orange-400 opacity-20 group-hover:opacity-30 transition-opacity">
                <Icon icon="mdi:account-group" className="w-12 h-12" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-orange-600">
              <Icon icon="mdi:trending-up" className="w-4 h-4 mr-1" />
              <span>Active learners</span>
            </div>
          </div>
        </div>

        {/* Teaching Staff Card */}
        <div className="group relative bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-3 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 shadow-lg">
                  <Icon icon="mdi:account-tie" className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="text-3xl font-bold text-gray-900">
                    {statsLoading ? (
                      <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                    ) : (
                      stats?.breakdown?.staff || 0
                    )}
                  </div>
                  <div className="text-sm font-medium text-blue-600">
                    Teaching Staff
                  </div>
                </div>
              </div>
              <div className="text-blue-400 opacity-20 group-hover:opacity-30 transition-opacity">
                <Icon icon="mdi:teach" className="w-12 h-12" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-blue-600">
              <Icon icon="mdi:book-open-variant" className="w-4 h-4 mr-1" />
              <span>Educators</span>
            </div>
          </div>
        </div>

        {/* Non-Teaching Staff Card */}
        <div className="group relative bg-gradient-to-br from-green-50 to-green-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-green-400/10 to-green-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-3 rounded-full bg-gradient-to-br from-green-400 to-green-600 shadow-lg">
                  <Icon
                    icon="mdi:account-hard-hat"
                    className="w-6 h-6 text-white"
                  />
                </div>
                <div>
                  <div className="text-3xl font-bold text-gray-900">
                    {statsLoading ? (
                      <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                    ) : (
                      stats?.breakdown?.non_teaching || 0
                    )}
                  </div>
                  <div className="text-sm font-medium text-green-600">
                    Support Staff
                  </div>
                </div>
              </div>
              <div className="text-green-400 opacity-20 group-hover:opacity-30 transition-opacity">
                <Icon icon="mdi:account-cog" className="w-12 h-12" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-green-600">
              <Icon icon="mdi:hand-heart" className="w-4 h-4 mr-1" />
              <span>Support team</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Quick Actions</h2>
          <Icon icon="mdi:lightning-bolt" className="w-6 h-6 text-yellow-500" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <button
            onClick={() => navigate("/persons")}
            className="group flex items-center space-x-3 p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg hover:from-blue-100 hover:to-blue-200 transition-all duration-200 border border-blue-200 hover:border-blue-300"
          >
            <div className="p-2 bg-blue-500 rounded-lg group-hover:bg-blue-600 transition-colors">
              <Icon icon="mdi:account-plus" className="w-5 h-5 text-white" />
            </div>
            <div className="text-left">
              <div className="font-medium text-gray-900">Add Person</div>
              <div className="text-sm text-gray-600">Upload new data</div>
            </div>
          </button>

          <button
            onClick={() => navigate("/id-cards")}
            className="group flex items-center space-x-3 p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg hover:from-purple-100 hover:to-purple-200 transition-all duration-200 border border-purple-200 hover:border-purple-300"
          >
            <div className="p-2 bg-purple-500 rounded-lg group-hover:bg-purple-600 transition-colors">
              <Icon
                icon="mdi:card-account-details"
                className="w-5 h-5 text-white"
              />
            </div>
            <div className="text-left">
              <div className="font-medium text-gray-900">ID Cards</div>
              <div className="text-sm text-gray-600">Generate cards</div>
            </div>
          </button>

          <button
            onClick={() => navigate("/schools")}
            className="group flex items-center space-x-3 p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg hover:from-green-100 hover:to-green-200 transition-all duration-200 border border-green-200 hover:border-green-300"
          >
            <div className="p-2 bg-green-500 rounded-lg group-hover:bg-green-600 transition-colors">
              <Icon icon="mdi:school" className="w-5 h-5 text-white" />
            </div>
            <div className="text-left">
              <div className="font-medium text-gray-900">Schools</div>
              <div className="text-sm text-gray-600">Manage schools</div>
            </div>
          </button>

          <button
            onClick={() =>
              window.scrollTo({
                top: document.body.scrollHeight,
                behavior: "smooth",
              })
            }
            className="group flex items-center space-x-3 p-4 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg hover:from-orange-100 hover:to-orange-200 transition-all duration-200 border border-orange-200 hover:border-orange-300"
          >
            <div className="p-2 bg-orange-500 rounded-lg group-hover:bg-orange-600 transition-colors">
              <Icon icon="mdi:chart-line" className="w-5 h-5 text-white" />
            </div>
            <div className="text-left">
              <div className="font-medium text-gray-900">Statistics</div>
              <div className="text-sm text-gray-600">View details</div>
            </div>
          </button>
        </div>
      </div>

      {/* Enhanced Recent Activity */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Recent Additions</h2>
          <div className="flex items-center space-x-2">
            <Icon icon="mdi:clock-outline" className="w-5 h-5 text-gray-400" />
            <span className="text-sm text-gray-500">Last 10 entries</span>
          </div>
        </div>
        {loading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, index) => (
              <div
                key={index}
                className="animate-pulse flex items-center space-x-4 p-4 bg-gray-50 rounded-lg"
              >
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
            ))}
          </div>
        ) : persons.length > 0 ? (
          <div className="space-y-3">
            {persons.slice(0, 5).map((person, index) => (
              <div
                key={person.id}
                className="group flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-all duration-200 border border-transparent hover:border-gray-200"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div
                      className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-lg font-bold shadow-md ${
                        person.type === "student"
                          ? "bg-gradient-to-br from-orange-400 to-orange-600"
                          : person.type === "staff"
                          ? "bg-gradient-to-br from-blue-400 to-blue-600"
                          : "bg-gradient-to-br from-green-400 to-green-600"
                      }`}
                    >
                      {person.name.charAt(0).toUpperCase()}
                    </div>
                    <div
                      className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                        person.type === "student"
                          ? "bg-orange-500"
                          : person.type === "staff"
                          ? "bg-blue-500"
                          : "bg-green-500"
                      }`}
                    >
                      <Icon
                        icon={
                          person.type === "student"
                            ? "mdi:school"
                            : person.type === "staff"
                            ? "mdi:account-tie"
                            : "mdi:account-hard-hat"
                        }
                        className="w-2.5 h-2.5 text-white m-0.5"
                      />
                    </div>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 group-hover:text-gray-700">
                      {person.name}
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <span>
                        {person.type === "student" &&
                        person.class &&
                        person.section
                          ? `Class ${person.class}-${person.section}`
                          : person.type.replace("_", " ").toUpperCase()}
                      </span>
                      {person.person_id && (
                        <>
                          <span>•</span>
                          <span className="font-mono text-xs bg-gray-200 px-2 py-1 rounded">
                            {person.person_id}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {new Date(person.created_at).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(person.created_at).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </div>
                  </div>
                  <Icon
                    icon="mdi:chevron-right"
                    className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors"
                  />
                </div>
              </div>
            ))}
            {persons.length > 5 && (
              <div className="pt-4 border-t border-gray-200">
                <button
                  onClick={() => navigate("/persons")}
                  className="w-full flex items-center justify-center space-x-2 p-3 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200"
                >
                  <span className="font-medium">
                    View all {persons.length} persons
                  </span>
                  <Icon icon="mdi:arrow-right" className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <Icon icon="mdi:account-plus" className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No persons added yet
            </h3>
            <p className="text-gray-500 mb-6">
              Get started by adding students, staff, or support personnel to
              this school.
            </p>
            <button
              onClick={() => navigate("/persons")}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Icon icon="mdi:plus" className="w-4 h-4" />
              <span>Add First Person</span>
            </button>
          </div>
        )}
      </div>

      {/* Enhanced Statistics Component */}
      {stats && (
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900">
                Detailed Statistics
              </h2>
              <Icon icon="mdi:chart-box" className="w-6 h-6 text-gray-600" />
            </div>
          </div>
          <div className="p-6">
            <Statistics stats={stats} persons={persons} />
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardPage;
