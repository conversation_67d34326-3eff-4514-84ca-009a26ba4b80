const { Model, DataTypes } = require("sequelize");
const sequelize = require("../config/database");

class School extends Model {}

School.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: {
          msg: "Must be a valid email address",
        },
      },
    },
    logo_path: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    stamp_path: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    signature_path: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    validity_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Validity date for ID cards issued by this school",
    },
  },
  {
    sequelize,
    modelName: "School",
    tableName: "schools",
    timestamps: true,
    underscored: false,
    createdAt: "createdAt",
    updatedAt: "updatedAt",
  }
);

module.exports = School;
