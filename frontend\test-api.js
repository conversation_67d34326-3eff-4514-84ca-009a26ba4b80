// Test script to check API response for persons
const axios = require("axios");

const API_BASE_URL = "https://print-api.webstudiomatrix.com";

async function testPersonsAPI() {
  try {
    // First, get all schools to find the correct school_id
    console.log("Getting all schools...");
    const schoolsResponse = await axios.get(`${API_BASE_URL}/api/school/all`);
    console.log("Schools:", schoolsResponse.data);

    if (schoolsResponse.data.length === 0) {
      console.log("No schools found!");
      return;
    }

    const school = schoolsResponse.data[0]; // Use first school
    console.log(`\nTesting with school: ${school.name} (ID: ${school.id})`);

    console.log("\nTesting API without limit parameter...");

    // Test without limit (should show default behavior)
    const response1 = await axios.get(`${API_BASE_URL}/api/persons`, {
      params: { school_id: school.id },
    });

    console.log(
      `Without limit: Got ${response1.data.persons?.length || 0} persons`
    );
    console.log("Total in DB:", response1.data.total);
    console.log("Pages:", response1.data.pages);
    console.log("Current page:", response1.data.page);

    // Test with high limit
    console.log("\nTesting API with limit=1000...");
    const response2 = await axios.get(`${API_BASE_URL}/api/persons`, {
      params: {
        school_id: school.id,
        limit: 1000,
      },
    });

    console.log(
      `With limit=1000: Got ${response2.data.persons?.length || 0} persons`
    );
    console.log("Total in DB:", response2.data.total);
    console.log("Pages:", response2.data.pages);

    // Test with different page sizes
    console.log("\nTesting API with limit=50...");
    const response3 = await axios.get(`${API_BASE_URL}/api/persons`, {
      params: {
        school_id: school.id,
        limit: 50,
      },
    });

    console.log(
      `With limit=50: Got ${response3.data.persons?.length || 0} persons`
    );
    console.log("Total in DB:", response3.data.total);
    console.log("Pages:", response3.data.pages);

    // Test pagination - get page 2
    if (response3.data.pages > 1) {
      console.log("\nTesting pagination - page 2...");
      const response4 = await axios.get(`${API_BASE_URL}/api/persons`, {
        params: {
          school_id: school.id,
          limit: 50,
          page: 2,
        },
      });

      console.log(
        `Page 2 with limit=50: Got ${
          response4.data.persons?.length || 0
        } persons`
      );
      console.log("Total in DB:", response4.data.total);
      console.log("Current page:", response4.data.page);
    }
  } catch (error) {
    console.error("Error testing API:", error.response?.data || error.message);
  }
}

testPersonsAPI();
