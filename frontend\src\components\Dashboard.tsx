import React, { useState, useEffect } from "react";
import axios from "axios";
import { useSchool } from "../contexts/SchoolContext";
import { PersonUpload } from "./PersonUpload";
import { PersonList } from "./PersonList";
import { Statistics } from "./Statistics";
import { Person } from "../services/personService";

interface Stats {
  total: number;
  breakdown: {
    student?: number;
    staff?: number;
    non_teaching?: number;
  };
}

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

export const Dashboard: React.FC = () => {
  const { selectedSchool } = useSchool();
  const [persons, setPersons] = useState<Person[]>([]);
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<"upload" | "list" | "stats">(
    "upload"
  );

  const fetchPersons = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/api/persons`, {
        params: selectedSchool
          ? { school_id: selectedSchool.id, limit: 1000 }
          : { limit: 1000 },
      });
      console.log(
        `Dashboard: Fetched ${
          response.data.persons?.length || 0
        } persons from API`
      );
      setPersons(response.data.persons || []);
    } catch (error) {
      console.error("Error fetching persons:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const url = selectedSchool
        ? `${API_BASE_URL}/api/cards/stats?school_id=${selectedSchool.id}`
        : `${API_BASE_URL}/api/cards/stats`;
      const response = await axios.get(url);
      setStats(response.data);
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  };

  useEffect(() => {
    fetchPersons();
    fetchStats();
  }, [selectedSchool]);

  const handleUploadSuccess = () => {
    fetchPersons();
    fetchStats();
  };

  const tabs = [
    { id: "upload", label: "Upload Data", icon: "📤" },
    { id: "list", label: "Person List", icon: "👥" },
    { id: "stats", label: "Statistics", icon: "📊" },
  ] as const;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gray-900">
                  {selectedSchool?.name || "School Card Management"}
                </h1>
                <p className="text-sm text-gray-500">
                  ID Card Management System
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {stats && (
                <div className="text-sm text-gray-600">
                  Total Cards:{" "}
                  <span className="font-semibold">{stats.total}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? "border-birta-orange text-birta-orange"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {activeTab === "upload" && (
            <PersonUpload
              onUploadSuccess={handleUploadSuccess}
              selectedSchoolId={selectedSchool?.id}
            />
          )}

          {activeTab === "list" && (
            <PersonList
              persons={persons}
              loading={loading}
              onRefresh={fetchPersons}
            />
          )}

          {activeTab === "stats" && (
            <Statistics stats={stats} persons={persons} />
          )}
        </div>
      </main>
    </div>
  );
};
