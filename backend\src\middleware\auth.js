const { verifyToken, extractTokenFromHeader } = require("../utils/auth");
const { User } = require("../models");

/**
 * Middleware to authenticate JWT tokens
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const authenticateToken = async (req, res, next) => {
  try {
    // Try to get token from Authorization header first
    let token = extractTokenFromHeader(req.headers.authorization);
    
    // If no token in header, try to get from cookies
    if (!token && req.cookies && req.cookies.accessToken) {
      token = req.cookies.accessToken;
    }

    if (!token) {
      return res.status(401).json({
        error: "Access denied. No token provided.",
        code: "NO_TOKEN",
      });
    }

    // Verify the token
    const decoded = verifyToken(token);
    
    // Get user from database to ensure they still exist and are active
    const user = await User.findByPk(decoded.id, {
      attributes: { exclude: ["password_hash"] },
      include: [
        {
          model: require("../models").School,
          attributes: ["id", "name"],
        },
      ],
    });

    if (!user) {
      return res.status(401).json({
        error: "Access denied. User not found.",
        code: "USER_NOT_FOUND",
      });
    }

    if (!user.is_active) {
      return res.status(401).json({
        error: "Access denied. User account is inactive.",
        code: "USER_INACTIVE",
      });
    }

    // Add user info to request object
    req.user = user;
    req.token = token;
    
    next();
  } catch (error) {
    if (error.message === "Token expired") {
      return res.status(401).json({
        error: "Access denied. Token expired.",
        code: "TOKEN_EXPIRED",
      });
    } else if (error.message === "Invalid token") {
      return res.status(401).json({
        error: "Access denied. Invalid token.",
        code: "INVALID_TOKEN",
      });
    } else {
      console.error("Authentication error:", error);
      return res.status(500).json({
        error: "Internal server error during authentication.",
        code: "AUTH_ERROR",
      });
    }
  }
};

/**
 * Middleware to check if user has required role
 * @param {string|Array} roles - Required role(s)
 * @returns {Function} - Express middleware function
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: "Access denied. Authentication required.",
        code: "AUTH_REQUIRED",
      });
    }

    const userRole = req.user.role;
    const requiredRoles = Array.isArray(roles) ? roles : [roles];

    if (!requiredRoles.includes(userRole)) {
      return res.status(403).json({
        error: "Access denied. Insufficient permissions.",
        code: "INSUFFICIENT_PERMISSIONS",
        required: requiredRoles,
        current: userRole,
      });
    }

    next();
  };
};

/**
 * Middleware to check if user belongs to a specific school or is admin
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requireSchoolAccess = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      error: "Access denied. Authentication required.",
      code: "AUTH_REQUIRED",
    });
  }

  const userRole = req.user.role;
  const userSchoolId = req.user.school_id;
  const requestedSchoolId = req.params.schoolId || req.body.school_id;

  // Admins can access all schools
  if (userRole === "admin") {
    return next();
  }

  // School admins and users can only access their own school
  if (userSchoolId && requestedSchoolId && userSchoolId.toString() === requestedSchoolId.toString()) {
    return next();
  }

  return res.status(403).json({
    error: "Access denied. You can only access data from your assigned school.",
    code: "SCHOOL_ACCESS_DENIED",
  });
};

/**
 * Optional authentication middleware - doesn't fail if no token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const optionalAuth = async (req, res, next) => {
  try {
    let token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token && req.cookies && req.cookies.accessToken) {
      token = req.cookies.accessToken;
    }

    if (token) {
      const decoded = verifyToken(token);
      const user = await User.findByPk(decoded.id, {
        attributes: { exclude: ["password_hash"] },
      });

      if (user && user.is_active) {
        req.user = user;
        req.token = token;
      }
    }

    next();
  } catch (error) {
    // Ignore errors in optional auth
    next();
  }
};

module.exports = {
  authenticateToken,
  requireRole,
  requireSchoolAccess,
  optionalAuth,
};
