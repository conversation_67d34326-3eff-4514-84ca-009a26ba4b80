import React, { createContext, useContext, useState, ReactNode } from "react";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import Notification, { NotificationProps } from "../components/Notification";

interface NotificationData {
  id: string;
  message: string;
  type: "success" | "error" | "info" | "warning";
  duration?: number;
}

interface NotificationContextType {
  showNotification: (
    message: string,
    type: "success" | "error" | "info" | "warning",
    duration?: number
  ) => void;
  showSuccess: (message: string, duration?: number) => void;
  showError: (message: string, duration?: number) => void;
  showInfo: (message: string, duration?: number) => void;
  showWarning: (message: string, duration?: number) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      "useNotification must be used within a NotificationProvider"
    );
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);

  const showNotification = (
    message: string,
    type: "success" | "error" | "info" | "warning",
    duration = 5000
  ) => {
    const id = Date.now().toString();
    const notification: NotificationData = { id, message, type, duration };

    setNotifications((prev) => [...prev, notification]);
  };

  const removeNotification = (id: string) => {
    setNotifications((prev) =>
      prev.filter((notification) => notification.id !== id)
    );
  };

  const showSuccess = (message: string, duration?: number) =>
    showNotification(message, "success", duration);
  const showError = (message: string, duration?: number) =>
    showNotification(message, "error", duration);
  const showInfo = (message: string, duration?: number) =>
    showNotification(message, "info", duration);
  const showWarning = (message: string, duration?: number) =>
    showNotification(message, "warning", duration);

  return (
    <NotificationContext.Provider
      value={{
        showNotification,
        showSuccess,
        showError,
        showInfo,
        showWarning,
      }}
    >
      {children}

      {/* Render notifications */}
      <div className="fixed top-20 right-4 z-50 space-y-2">
        {notifications.map((notification) => (
          <Notification
            key={notification.id}
            message={notification.message}
            type={notification.type}
            duration={notification.duration}
            onClose={() => removeNotification(notification.id)}
          />
        ))}
      </div>
    </NotificationContext.Provider>
  );
};
