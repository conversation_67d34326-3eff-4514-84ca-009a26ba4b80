const { QueryInterface, DataTypes } = require('sequelize');

/**
 * Migration to add school_id column to persons table
 * This migration will:
 * 1. Add school_id column to persons table
 * 2. Create a default school if none exists
 * 3. Assign all existing persons to the default school
 */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Check if school_id column already exists
      const tableDescription = await queryInterface.describeTable('persons');
      if (tableDescription.school_id) {
        console.log('school_id column already exists, skipping migration');
        await transaction.commit();
        return;
      }

      // Add school_id column (nullable initially)
      await queryInterface.addColumn('persons', 'school_id', {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'schools',
          key: 'id'
        }
      }, { transaction });

      // Check if any schools exist
      const [schools] = await queryInterface.sequelize.query(
        'SELECT * FROM schools LIMIT 1',
        { transaction }
      );

      let defaultSchoolId;
      
      if (schools.length === 0) {
        // Create default school
        const [result] = await queryInterface.sequelize.query(
          `INSERT INTO schools (name, address, phone, email, createdAt, updatedAt) 
           VALUES (?, ?, ?, ?, ?, ?) RETURNING id`,
          {
            replacements: [
              'BIRTA GLOBAL SCHOOL',
              'Aduwa Bridge, Atthi Sadan Road, Birtamode',
              '+977-9852672234',
              '<EMAIL>',
              new Date(),
              new Date()
            ],
            transaction
          }
        );
        defaultSchoolId = result[0].id;
      } else {
        defaultSchoolId = schools[0].id;
      }

      // Update all existing persons to belong to the default school
      await queryInterface.sequelize.query(
        'UPDATE persons SET school_id = ? WHERE school_id IS NULL',
        {
          replacements: [defaultSchoolId],
          transaction
        }
      );

      // Make school_id NOT NULL
      await queryInterface.changeColumn('persons', 'school_id', {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'schools',
          key: 'id'
        }
      }, { transaction });

      await transaction.commit();
      console.log('Successfully added school_id to persons table');
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      await queryInterface.removeColumn('persons', 'school_id', { transaction });
      await transaction.commit();
      console.log('Successfully removed school_id from persons table');
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
