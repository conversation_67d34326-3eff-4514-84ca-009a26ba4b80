import React, { useState, useEffect } from "react";
import { CardRendererProps } from "./types";
import CardHeader from "./CardHeader";
import CardBody from "./CardBody";
import CardFooter from "./CardFooter";
import VerticalCardRenderer from "./VerticalCardRenderer";
import MaiValleyCardRenderer from "./MaiValleyCardRenderer";
import "./Card.css";

const CardRenderer: React.FC<CardRendererProps> = ({
  config,
  mode = "preview",
  className = "",
  onPhotoError,
  onPhotoLoad,
}) => {
  // Use Mai Valley renderer if specified
  if (config.template.renderMode === "mai_valley") {
    return (
      <MaiValleyCardRenderer
        config={config}
        mode={mode}
        className={className}
        onPhotoError={onPhotoError}
        onPhotoLoad={onPhotoLoad}
      />
    );
  }

  // Use vertical renderer if specified
  if (config.template.renderMode === "vertical") {
    return (
      <VerticalCardRenderer
        config={config}
        mode={mode}
        className={className}
        onPhotoError={onPhotoError}
        onPhotoLoad={onPhotoLoad}
      />
    );
  }

  // Use default horizontal renderer
  return (
    <DefaultCardRenderer
      config={config}
      mode={mode}
      className={className}
      onPhotoError={onPhotoError}
      onPhotoLoad={onPhotoLoad}
    />
  );
};

const DefaultCardRenderer: React.FC<CardRendererProps> = ({
  config,
  mode = "preview",
  className = "",
  onPhotoError,
  onPhotoLoad,
}) => {
  const [photoBase64, setPhotoBase64] = useState<string>("");
  const [logoBase64, setLogoBase64] = useState<string>("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadAssets = async () => {
      setLoading(true);

      try {
        // Load photo if available
        if (config.person.photo_path) {
          const photoResponse = await fetch(
            `${
              process.env.REACT_APP_API_URL ||
              "https://print-api.webstudiomatrix.com"
            }/api/cards/photos/${config.person.photo_path}?t=${Date.now()}`
          ).catch(() => null);
          if (photoResponse && photoResponse.ok) {
            const photoBlob = await photoResponse.blob();
            const photoBase64 = await blobToBase64(photoBlob);
            setPhotoBase64(photoBase64);
          }
        }

        // Load school logo if available
        if (config.school.logo_path) {
          const logoResponse = await fetch(
            `${
              process.env.REACT_APP_API_URL ||
              "https://print-api.webstudiomatrix.com"
            }/api/cards/logos/${config.school.logo_path}`
          ).catch(() => null);
          if (logoResponse && logoResponse.ok) {
            const logoBlob = await logoResponse.blob();
            const logoBase64 = await blobToBase64(logoBlob);
            setLogoBase64(logoBase64);
          }
        }
      } catch (error) {
        console.error("Error loading card assets:", error);
      } finally {
        setLoading(false);
      }
    };

    loadAssets();
  }, [config.person.photo_path, config.school.logo_path]);

  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  };

  const cardStyles = {
    "--theme-color": config.template.theme.primaryColor,
    "--theme-color-light": config.template.theme.secondaryColor,
    "--card-bg-color": config.template.theme.backgroundColor,
    width: mode === "print" ? "2.125in" : "325px", // CR80 Standard vertical card width
    height: mode === "print" ? "3.375in" : "500px", // CR80 Standard vertical card height
  } as React.CSSProperties;

  const cardClasses = [
    "card-container",
    `card-${config.template.type}`,
    `layout-${config.template.theme.layout}`,
    mode === "print" ? "print-mode" : "",
    loading ? "card-loading" : "",
    className,
  ]
    .filter(Boolean)
    .join(" ");

  return (
    <div className={cardClasses} style={cardStyles}>
      <CardHeader
        school={config.school}
        template={config.template}
        logoBase64={logoBase64}
      />

      <CardBody
        person={config.person}
        template={config.template}
        photoBase64={photoBase64}
        onPhotoError={onPhotoError}
        onPhotoLoad={onPhotoLoad}
      />

      <CardFooter school={config.school} template={config.template} />
    </div>
  );
};

export default CardRenderer;
