/* eslint-disable */
import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  generateInitials,
  generateInitialsSvg,
  getRoleBasedColors,
} from "../../utils/imageUtils";
import { debugImageIssues, isValidImageUrl } from "../../utils/imageDebugUtils";

interface ProfileImageProps {
  src?: string;
  name: string;
  designation?: string;
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
  shape?: "circle" | "rounded" | "square";
  showInitials?: boolean;
}

const ProfileImage: React.FC<ProfileImageProps> = ({
  src,
  name,
  designation = "",
  className = "",
  size = "md",
  shape = "rounded",
  showInitials = true,
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-16 h-16",
    lg: "w-20 h-20",
    xl: "w-24 h-24",
  };

  const shapeClasses = {
    circle: "rounded-full",
    rounded: "rounded-lg",
    square: "rounded-none",
  };

  // Cleanup function
  const cleanup = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
  }, []);

  // Image loading effect
  useEffect(() => {
    // Reset states when src changes
    setImageError(false);
    setImageLoading(false);
    setImageLoaded(false);
    cleanup();

    if (!src || src.trim() === "") {
      setImageError(true);
      return;
    }

    // Validate URL format first
    if (!isValidImageUrl(src)) {
      console.error(`Invalid image URL for ${name}:`, src);
      setImageError(true);
      return;
    }

    setImageLoading(true);

    // Create a new image for preloading
    const img = new Image();

    const handleLoad = () => {
      cleanup();
      setImageLoading(false);
      setImageLoaded(true);
      setImageError(false);
      console.log(`ProfileImage preload success for ${name}:`, src);
    };

    const handleError = (error: Event | string) => {
      cleanup();
      setImageLoading(false);
      setImageLoaded(false);
      setImageError(true);

      console.warn(`ProfileImage preload failed for ${name}:`, src);

      // Log detailed error information for debugging
      console.warn(`Error details:`, {
        name,
        src,
        error: error instanceof ErrorEvent ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
      });

      // Run comprehensive debugging in development mode
      if (process.env.NODE_ENV === "development") {
        debugImageIssues(src, name).catch((debugError) => {
          console.warn(`Debug analysis failed for ${name}:`, debugError);
        });
      }
    };

    img.onload = handleLoad;
    img.onerror = handleError;

    // Set crossOrigin to handle CORS properly
    img.crossOrigin = "anonymous";

    // Add a timeout to prevent infinite loading state
    const timeoutDuration = process.env.NODE_ENV === "production" ? 8000 : 5000;
    timeoutRef.current = setTimeout(() => {
      console.warn(
        `ProfileImage timeout for ${name} after ${timeoutDuration}ms:`,
        src
      );
      handleError("timeout");
    }, timeoutDuration);

    // Start loading
    img.src = src;

    return cleanup;
  }, [src, name, cleanup]);

  const handleImageLoad = () => {
    console.log(`ProfileImage handleImageLoad success for ${name}:`, src);
    setImageLoading(false);
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = (
    event: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    console.warn(`ProfileImage handleImageError failed for ${name}:`, src);

    // Check if this might be a CORS-related error
    const imgElement = event.currentTarget;
    const isCorsProblem =
      imgElement.naturalWidth === 0 && imgElement.naturalHeight === 0;

    // Log detailed error information
    console.warn(`Image error details:`, {
      name,
      src,
      naturalWidth: imgElement.naturalWidth,
      naturalHeight: imgElement.naturalHeight,
      complete: imgElement.complete,
      currentSrc: imgElement.currentSrc,
      crossOrigin: imgElement.crossOrigin,
      isCorsProblem,
      timestamp: new Date().toISOString(),
    });

    if (isCorsProblem) {
      console.warn(
        `Possible CORS or network error for ${name}. Falling back to initials.`
      );

      // In development, try to provide more debugging info
      if (process.env.NODE_ENV === "development") {
        console.warn(`Debugging tips for ${name}:`, {
          suggestion1: "Check if the image file exists on the server",
          suggestion2: "Verify CORS headers are properly set on the server",
          suggestion3:
            "Check network tab in browser dev tools for detailed error",
          suggestion4: "Ensure the API server is running and accessible",
          imageUrl: src,
        });
      }
    }

    setImageLoading(false);
    setImageLoaded(false);
    setImageError(true);
  };

  const renderPlaceholderSvg = () => {
    const initials = generateInitials(name);
    const colors = getRoleBasedColors(designation);
    const svgSize =
      size === "sm" ? 32 : size === "md" ? 64 : size === "lg" ? 80 : 96;

    const svgDataUrl = generateInitialsSvg(
      initials,
      colors.initialsColor,
      "#FFFFFF",
      svgSize
    );

    // Check if className includes w-full h-full to respect parent container dimensions
    const useParentDimensions =
      className.includes("w-full") && className.includes("h-full");

    // Extract custom border radius from className if present
    const customBorderRadius = className.match(/rounded-\w+/)?.[0] || "";
    const finalShape = customBorderRadius ? "square" : shape;

    const placeholderClasses = useParentDimensions
      ? `w-full h-full ${
          customBorderRadius || shapeClasses[finalShape]
        } object-cover`
      : `${sizeClasses[size]} ${shapeClasses[finalShape]} object-cover ${className}`;

    return (
      <img
        src={svgDataUrl}
        alt={`${name} initials`}
        className={placeholderClasses}
      />
    );
  };

  // Show initials if no image or image failed to load (and showInitials is enabled)
  if ((!src || imageError || (!imageLoaded && !imageLoading)) && showInitials) {
    return renderPlaceholderSvg();
  }

  // If showInitials is false and no image, show empty div
  if (
    (!src || imageError || (!imageLoaded && !imageLoading)) &&
    !showInitials
  ) {
    const useParentDimensions =
      className.includes("w-full") && className.includes("h-full");
    const emptyClasses = useParentDimensions
      ? `w-full h-full ${shapeClasses[shape]} bg-gray-200`
      : `${sizeClasses[size]} ${shapeClasses[shape]} bg-gray-200 ${className}`;

    return <div className={emptyClasses}></div>;
  }

  // Check if className includes w-full h-full to respect parent container dimensions
  const useParentDimensions =
    className.includes("w-full") && className.includes("h-full");

  // Extract custom border radius from className if present
  const customBorderRadius = className.match(/rounded-\w+/)?.[0] || "";
  const finalShape = customBorderRadius ? "square" : shape; // Use square if custom border radius is provided

  const containerClasses = useParentDimensions
    ? className
    : `${sizeClasses[size]} ${className}`;
  const imageClasses = useParentDimensions
    ? `w-full h-full ${
        customBorderRadius || shapeClasses[finalShape]
      } object-cover`
    : `${sizeClasses[size]} ${shapeClasses[finalShape]} object-cover`;

  return (
    <div className={`relative ${containerClasses}`}>
      {imageLoading && (
        <div
          className={`absolute inset-0 ${
            customBorderRadius || shapeClasses[shape]
          } bg-gray-200 flex items-center justify-center z-10`}
        >
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
        </div>
      )}
      {(imageLoaded || !imageLoading) && (
        <img
          ref={imageRef}
          src={src}
          alt={name}
          crossOrigin="anonymous"
          className={`${imageClasses} ${
            imageLoaded && !imageError ? "opacity-100" : "opacity-0"
          } transition-opacity duration-300`}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      )}
    </div>
  );
};

export default ProfileImage;
