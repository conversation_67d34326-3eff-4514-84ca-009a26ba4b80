import {
  PersonData,
  SchoolData,
  CardConfig,
  CardTemplate,
  DEFAULT_THEMES,
  DEFAULT_DIMENSIONS,
} from "./types";

/**
 * Creates a card configuration from person and school data
 */
export const createCardConfig = (
  person: PersonData,
  school: SchoolData,
  customTemplate?: Partial<CardTemplate>
): CardConfig => {
  const defaultTemplate: CardTemplate = {
    type: person.type,
    theme: DEFAULT_THEMES[person.type],
    dimensions: DEFAULT_DIMENSIONS,
    showElements: {
      logo: true,
      photo: true,
      qrCode: false,
      decorativeElements: true,
    },
  };

  const template = customTemplate
    ? { ...defaultTemplate, ...customTemplate }
    : defaultTemplate;

  return {
    person,
    school,
    template,
  };
};

/**
 * Creates a default school configuration
 */
export const createDefaultSchool = (): SchoolData => ({
  name: "BIRTA GLOBAL SCHOOL",
  address: "Aduwa Bridge, Atthi Sadan Road, Birtamode",
  phone: "+977-9852672234",
  email: "<EMAIL>",
});

/**
 * Creates a Mai Valley school configuration
 */
export const createMaiValleySchool = (): SchoolData => ({
  name: "MAI VALLEY BOARDING SCHOOL",
  address: "Birtamode-3, Jhapa",
  phone: "9742930105",
  email: "<EMAIL>",
});

/**
 * Creates a card configuration with Mai Valley design
 */
export const createMaiValleyCardConfig = (
  person: PersonData,
  school?: SchoolData,
  customTemplate?: Partial<CardTemplate>
): CardConfig => {
  const maiValleySchool = school || createMaiValleySchool();

  const maiValleyTemplate: CardTemplate = {
    type: person.type,
    theme: DEFAULT_THEMES[person.type],
    dimensions: DEFAULT_DIMENSIONS,
    showElements: {
      logo: true,
      photo: true,
      qrCode: false,
      decorativeElements: true,
    },
    renderMode: "mai_valley",
  };

  const template = customTemplate
    ? { ...maiValleyTemplate, ...customTemplate }
    : maiValleyTemplate;

  return {
    person,
    school: maiValleySchool,
    template,
  };
};

/**
 * Validates if a person has all required fields for card generation
 */
export const validatePersonForCard = (
  person: PersonData
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!person.name || person.name.trim() === "") {
    errors.push("Person name is required");
  }

  if (!person.person_id || person.person_id.trim() === "") {
    errors.push("Person ID is required");
  }

  if (!["student", "staff", "non_teaching"].includes(person.type)) {
    errors.push("Invalid person type");
  }

  // Type-specific validations
  if (person.type === "student") {
    if (!person.class || person.class.trim() === "") {
      errors.push("Class is required for students");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Gets the appropriate card theme based on person type
 */
export const getCardTheme = (personType: PersonData["type"]) => {
  return DEFAULT_THEMES[personType];
};

/**
 * Converts person data from API format to card format
 */
export const transformPersonData = (apiPerson: any): PersonData => {
  return {
    id: apiPerson.id,
    person_id: apiPerson.person_id,
    name: apiPerson.name,
    type: apiPerson.type,
    class: apiPerson.class,
    section: apiPerson.section,
    roll_number: apiPerson.roll_number,
    parents_name: apiPerson.parents_name,
    contact_no: apiPerson.contact_no,
    photo_path: apiPerson.photo_path,
    department: apiPerson.department,
    address: apiPerson.address,
    csv_batch_id: apiPerson.csv_batch_id,
    created_at: apiPerson.created_at,
    updated_at: apiPerson.updated_at,
  };
};

/**
 * Converts school data from API format to card format
 */
export const transformSchoolData = (apiSchool: any): SchoolData => {
  return {
    id: apiSchool.id,
    name: apiSchool.name,
    address: apiSchool.address,
    phone: apiSchool.phone,
    email: apiSchool.email,
    logo_path: apiSchool.logo_path,
    stamp_path: apiSchool.stamp_path,
    signature_path: apiSchool.signature_path,
    created_at: apiSchool.created_at,
    updated_at: apiSchool.updated_at,
  };
};

/**
 * Generates multiple card configurations for batch processing
 */
export const createBatchCardConfigs = (
  persons: PersonData[],
  school: SchoolData,
  customTemplate?: Partial<CardTemplate>
): CardConfig[] => {
  return persons.map((person) =>
    createCardConfig(person, school, customTemplate)
  );
};

/**
 * Filters persons by type for batch generation
 */
export const filterPersonsByType = (
  persons: PersonData[],
  type: PersonData["type"]
): PersonData[] => {
  return persons.filter((person) => person.type === type);
};
