interface BackgroundShapeProps {
  className?: string;
}

const BackgroundShape = ({ className = "" }: BackgroundShapeProps) => {
  return (
    <div className={`absolute inset-0 ${className}`}>
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width="100%" 
        height="100%" 
        viewBox="0 0 500 800" 
        fill="none"
        className="w-full h-full"
        preserveAspectRatio="none"
      >
        <path 
          d="M861.402 281.977C827.692 176.699 770.203 85.1462 695.563 17.8722C620.923 -49.4018 532.169 -89.6592 439.532 -98.2601C346.894 -106.861 191.674 -68.2146 109.5 -15.4999C54.5 56.5001 0.963547 88.1656 -45.5 186C-91.9635 283.835 -94.1676 409.33 -90.6456 522.226C-87.1236 635.123 -57.4392 744.511 -5.01376 837.784C47.4117 931.057 120.444 1004.42 205.666 1049.41C290.888 1094.4 384.831 1109.2 476.666 1092.08L453.817 910.746C390.085 922.62 324.89 912.354 265.747 881.13C206.604 849.906 155.921 798.995 119.538 734.265C83.1555 669.535 62.555 593.62 60.1108 515.272C57.6666 436.923 73.4782 359.33 105.723 291.434C137.968 223.538 185.334 168.104 242.362 131.52C299.389 94.9371 124.711 107.031 189 113C253.289 118.969 488.5 108.02 555.5 118.5C622.5 128.98 697.424 275.48 720.819 348.542L861.402 281.977Z" 
          fill="#4A6CB3"
        />
      </svg>
    </div>
  );
};

export default BackgroundShape;
