const NodeCache = require("node-cache");
const Redis = require("ioredis");
const { createLogger } = require("../utils/logger");

const logger = createLogger("cache");

/**
 * Multi-layer caching system with in-memory and Redis support
 */
class CacheManager {
  constructor() {
    // In-memory cache for frequently accessed small data
    this.memoryCache = new NodeCache({
      stdTTL: 300, // 5 minutes default TTL
      checkperiod: 60, // Check for expired keys every 60 seconds
      useClones: false // Better performance, but be careful with object mutations
    });

    // Redis cache for larger data and distributed caching
    this.redisCache = null;
    this.initRedis();

    // Cache statistics
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };
  }

  /**
   * Initialize Redis connection
   */
  initRedis() {
    if (process.env.REDIS_URL) {
      try {
        this.redisCache = new Redis(process.env.REDIS_URL, {
          retryDelayOnFailover: 100,
          maxRetriesPerRequest: 3,
          lazyConnect: true,
          keepAlive: 30000,
          connectTimeout: 10000,
          commandTimeout: 5000
        });

        this.redisCache.on("connect", () => {
          logger.info("Redis cache connected successfully");
        });

        this.redisCache.on("error", (err) => {
          logger.error("Redis cache error:", err);
          this.redisCache = null; // Fallback to memory cache only
        });

        this.redisCache.on("close", () => {
          logger.warn("Redis cache connection closed");
        });

      } catch (error) {
        logger.error("Failed to initialize Redis cache:", error);
        this.redisCache = null;
      }
    } else {
      logger.info("No Redis URL provided, using memory cache only");
    }
  }

  /**
   * Generate cache key with namespace
   */
  generateKey(namespace, key, params = {}) {
    const paramString = Object.keys(params).length > 0 
      ? `:${JSON.stringify(params)}` 
      : "";
    return `school_card:${namespace}:${key}${paramString}`;
  }

  /**
   * Get value from cache (tries memory first, then Redis)
   */
  async get(namespace, key, params = {}) {
    const cacheKey = this.generateKey(namespace, key, params);
    
    try {
      // Try memory cache first
      const memoryValue = this.memoryCache.get(cacheKey);
      if (memoryValue !== undefined) {
        this.stats.hits++;
        logger.debug(`Cache hit (memory): ${cacheKey}`);
        return memoryValue;
      }

      // Try Redis cache
      if (this.redisCache) {
        const redisValue = await this.redisCache.get(cacheKey);
        if (redisValue !== null) {
          const parsedValue = JSON.parse(redisValue);
          // Store in memory cache for faster future access
          this.memoryCache.set(cacheKey, parsedValue, 60); // 1 minute in memory
          this.stats.hits++;
          logger.debug(`Cache hit (Redis): ${cacheKey}`);
          return parsedValue;
        }
      }

      this.stats.misses++;
      logger.debug(`Cache miss: ${cacheKey}`);
      return null;

    } catch (error) {
      logger.error(`Cache get error for key ${cacheKey}:`, error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set(namespace, key, value, ttl = 300, params = {}) {
    const cacheKey = this.generateKey(namespace, key, params);
    
    try {
      // Set in memory cache
      this.memoryCache.set(cacheKey, value, Math.min(ttl, 300)); // Max 5 minutes in memory

      // Set in Redis cache
      if (this.redisCache) {
        await this.redisCache.setex(cacheKey, ttl, JSON.stringify(value));
      }

      this.stats.sets++;
      logger.debug(`Cache set: ${cacheKey} (TTL: ${ttl}s)`);
      return true;

    } catch (error) {
      logger.error(`Cache set error for key ${cacheKey}:`, error);
      return false;
    }
  }

  /**
   * Delete from cache
   */
  async delete(namespace, key, params = {}) {
    const cacheKey = this.generateKey(namespace, key, params);
    
    try {
      // Delete from memory cache
      this.memoryCache.del(cacheKey);

      // Delete from Redis cache
      if (this.redisCache) {
        await this.redisCache.del(cacheKey);
      }

      this.stats.deletes++;
      logger.debug(`Cache delete: ${cacheKey}`);
      return true;

    } catch (error) {
      logger.error(`Cache delete error for key ${cacheKey}:`, error);
      return false;
    }
  }

  /**
   * Clear cache by namespace pattern
   */
  async clearNamespace(namespace) {
    try {
      const pattern = `school_card:${namespace}:*`;
      
      // Clear memory cache
      const memoryKeys = this.memoryCache.keys().filter(key => key.startsWith(pattern.replace('*', '')));
      memoryKeys.forEach(key => this.memoryCache.del(key));

      // Clear Redis cache
      if (this.redisCache) {
        const redisKeys = await this.redisCache.keys(pattern);
        if (redisKeys.length > 0) {
          await this.redisCache.del(...redisKeys);
        }
      }

      logger.info(`Cache namespace cleared: ${namespace}`);
      return true;

    } catch (error) {
      logger.error(`Cache clear namespace error for ${namespace}:`, error);
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0;

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      memoryKeys: this.memoryCache.keys().length,
      redisConnected: !!this.redisCache
    };
  }
}

// Create singleton instance
const cacheManager = new CacheManager();

/**
 * Cache middleware factory
 */
const cacheMiddleware = (namespace, ttl = 300, keyGenerator = null) => {
  return async (req, res, next) => {
    // Skip caching for non-GET requests
    if (req.method !== "GET") {
      return next();
    }

    // Generate cache key
    const key = keyGenerator 
      ? keyGenerator(req) 
      : `${req.path}:${JSON.stringify(req.query)}`;

    try {
      // Try to get from cache
      const cachedData = await cacheManager.get(namespace, key);
      
      if (cachedData) {
        res.setHeader("X-Cache", "HIT");
        res.setHeader("X-Cache-TTL", ttl);
        return res.json(cachedData);
      }

      // Cache miss - intercept response to cache it
      const originalSend = res.send;
      res.send = function(data) {
        // Only cache successful responses
        if (res.statusCode === 200 && data) {
          try {
            const jsonData = typeof data === "string" ? JSON.parse(data) : data;
            cacheManager.set(namespace, key, jsonData, ttl);
          } catch (error) {
            logger.error("Failed to cache response:", error);
          }
        }
        
        res.setHeader("X-Cache", "MISS");
        return originalSend.call(this, data);
      };

      next();

    } catch (error) {
      logger.error("Cache middleware error:", error);
      next();
    }
  };
};

/**
 * Cache invalidation middleware
 */
const invalidateCache = (namespaces) => {
  return async (req, res, next) => {
    // Store original send function
    const originalSend = res.send;
    
    res.send = function(data) {
      // Only invalidate cache on successful operations
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Invalidate specified namespaces
        if (Array.isArray(namespaces)) {
          namespaces.forEach(namespace => {
            cacheManager.clearNamespace(namespace);
          });
        } else {
          cacheManager.clearNamespace(namespaces);
        }
      }
      
      return originalSend.call(this, data);
    };
    
    next();
  };
};

module.exports = {
  cacheManager,
  cacheMiddleware,
  invalidateCache
};
