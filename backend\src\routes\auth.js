const express = require("express");
const { body, validationResult } = require("express-validator");
const { Op } = require("sequelize");
const { User, School } = require("../models");
const {
  hashPassword,
  comparePassword,
  generateTokens,
  validatePassword,
} = require("../utils/auth");
const { authenticateToken, requireRole } = require("../middleware/auth");

const router = express.Router();

// Validation rules
const registerValidation = [
  body("username")
    .isLength({ min: 3, max: 50 })
    .withMessage("Username must be between 3 and 50 characters")
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage("Username can only contain letters, numbers, and underscores"),
  body("email").isEmail().withMessage("Must be a valid email address"),
  body("password")
    .isLength({ min: 8 })
    .withMessage("Password must be at least 8 characters long"),
  body("first_name")
    .optional()
    .isLength({ max: 50 })
    .withMessage("First name must be less than 50 characters"),
  body("last_name")
    .optional()
    .isLength({ max: 50 })
    .withMessage("Last name must be less than 50 characters"),
  body("role")
    .optional()
    .isIn(["admin", "user", "school_admin"])
    .withMessage("Role must be admin, user, or school_admin"),
  body("school_id")
    .optional()
    .isInt({ min: 1 })
    .withMessage("School ID must be a positive integer"),
];

const loginValidation = [
  body("username").notEmpty().withMessage("Username is required"),
  body("password").notEmpty().withMessage("Password is required"),
];

// Register new user
router.post("/register", registerValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const {
      username,
      email,
      password,
      first_name,
      last_name,
      role = "user",
      school_id,
    } = req.body;

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        error: "Password does not meet requirements",
        details: passwordValidation.errors,
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [{ username }, { email }],
      },
    });

    if (existingUser) {
      return res.status(409).json({
        error: "User already exists",
        field: existingUser.username === username ? "username" : "email",
      });
    }

    // Validate school_id if provided
    if (school_id) {
      const school = await School.findByPk(school_id);
      if (!school) {
        return res.status(400).json({
          error: "Invalid school ID",
        });
      }
    }

    // Hash password
    const password_hash = await hashPassword(password);

    // Create user
    const user = await User.create({
      username,
      email,
      password_hash,
      first_name,
      last_name,
      role,
      school_id,
    });

    // Generate tokens
    const tokens = generateTokens(user);

    // Set HTTP-only cookie for refresh token
    res.cookie("refreshToken", tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    // Return user data and access token
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      school_id: user.school_id,
      is_active: user.is_active,
      created_at: user.created_at,
    };

    res.status(201).json({
      message: "User registered successfully",
      user: userData,
      accessToken: tokens.accessToken,
    });
  } catch (error) {
    console.error("Registration error:", error);
    res.status(500).json({
      error: "Internal server error during registration",
    });
  }
});

// Login user
router.post("/login", loginValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { username, password } = req.body;

    // Find user by username or email
    const user = await User.findOne({
      where: {
        [Op.or]: [{ username }, { email: username }],
      },
      include: [
        {
          model: School,
          attributes: ["id", "name"],
        },
      ],
    });

    if (!user) {
      return res.status(401).json({
        error: "Invalid credentials",
      });
    }

    if (!user.is_active) {
      return res.status(401).json({
        error: "Account is inactive. Please contact administrator.",
      });
    }

    // Check password
    const isPasswordValid = await comparePassword(password, user.password_hash);
    if (!isPasswordValid) {
      return res.status(401).json({
        error: "Invalid credentials",
      });
    }

    // Update last login
    await user.update({ last_login: new Date() });

    // Generate tokens
    const tokens = generateTokens(user);

    // Set HTTP-only cookie for refresh token
    res.cookie("refreshToken", tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    // Return user data and access token
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      school_id: user.school_id,
      is_active: user.is_active,
      last_login: user.last_login,
      School: user.School,
    };

    res.json({
      message: "Login successful",
      user: userData,
      accessToken: tokens.accessToken,
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      error: "Internal server error during login",
    });
  }
});

// Logout user
router.post("/logout", (req, res) => {
  try {
    // Clear refresh token cookie
    res.clearCookie("refreshToken");
    res.clearCookie("accessToken");

    res.json({
      message: "Logout successful",
    });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({
      error: "Internal server error during logout",
    });
  }
});

// Get current user profile
router.get("/profile", authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ["password_hash"] },
      include: [
        {
          model: School,
          attributes: ["id", "name", "address", "phone", "email"],
        },
      ],
    });

    if (!user) {
      return res.status(404).json({
        error: "User not found",
      });
    }

    res.json({
      user,
    });
  } catch (error) {
    console.error("Profile fetch error:", error);
    res.status(500).json({
      error: "Internal server error while fetching profile",
    });
  }
});

// Update user profile
router.put(
  "/profile",
  authenticateToken,
  [
    body("first_name")
      .optional()
      .isLength({ max: 50 })
      .withMessage("First name must be less than 50 characters"),
    body("last_name")
      .optional()
      .isLength({ max: 50 })
      .withMessage("Last name must be less than 50 characters"),
    body("email")
      .optional()
      .isEmail()
      .withMessage("Must be a valid email address"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation failed",
          details: errors.array(),
        });
      }

      const { first_name, last_name, email } = req.body;
      const userId = req.user.id;

      // Check if email is already taken by another user
      if (email) {
        const existingUser = await User.findOne({
          where: {
            email,
            id: { [Op.ne]: userId },
          },
        });

        if (existingUser) {
          return res.status(409).json({
            error: "Email already taken by another user",
          });
        }
      }

      // Update user
      await User.update(
        { first_name, last_name, email },
        { where: { id: userId } }
      );

      // Fetch updated user
      const updatedUser = await User.findByPk(userId, {
        attributes: { exclude: ["password_hash"] },
        include: [
          {
            model: School,
            attributes: ["id", "name", "address", "phone", "email"],
          },
        ],
      });

      res.json({
        message: "Profile updated successfully",
        user: updatedUser,
      });
    } catch (error) {
      console.error("Profile update error:", error);
      res.status(500).json({
        error: "Internal server error while updating profile",
      });
    }
  }
);

module.exports = router;
