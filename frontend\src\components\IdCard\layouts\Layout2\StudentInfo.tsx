import { StudentData } from "./types";
import { hasValidValue } from "../../utils/fieldValidation";

interface StudentInfoProps {
  data: StudentData;
}

const StudentInfo = ({ data }: StudentInfoProps) => {
  const isStudent = data.personType === "student";
  const fieldConfig = data.fieldVisibilityConfig;

  // Get border and background colors based on person type
  const getPersonTypeStyles = (
    personType: "student" | "staff" | "non_teaching"
  ) => {
    switch (personType) {
      case "student":
        return {
          borderColor: "border-orange-500",
          bgColor: "bg-gradient-to-br from-orange-500 to-orange-600",
          shadowColor: "shadow-orange-200",
        };
      case "staff":
        return {
          borderColor: "border-blue-500",
          bgColor: "bg-gradient-to-br from-blue-500 to-blue-600",
          shadowColor: "shadow-blue-200",
        };
      case "non_teaching":
        return {
          borderColor: "border-green-500",
          bgColor: "bg-gradient-to-br from-green-500 to-green-600",
          shadowColor: "shadow-green-200",
        };
      default:
        return {
          borderColor: "border-orange-500",
          bgColor: "bg-gradient-to-br from-orange-500 to-orange-600",
          shadowColor: "shadow-orange-200",
        };
    }
  };

  const styles = getPersonTypeStyles(data.personType || "student");

  return (
    <div className="px-3 text-center flex-grow flex flex-col justify-between relative h-full">
      {/* Main Info Section */}
      <div className="flex-grow flex flex-col justify-center">
        {/* Name - Configurable */}
        {fieldConfig?.name !== false && hasValidValue(data.studentName) && (
          <h2 className="text-[13px] font-semibold text-gray-800 uppercase mb-1">
            {data.studentName}
          </h2>
        )}

        <div className="text-[10px] w-full leading-[14px] justify-center text-black grid grid-cols-[auto_auto] gap-x-2.5 font-medium  text-left">
          {isStudent ? (
            <>
              {/* Class - Configurable */}
              {fieldConfig?.class !== false && hasValidValue(data.class) && (
                <>
                  <div>Class</div>
                  <div>: {data.class}</div>
                </>
              )}

              {/* Roll Number - Configurable */}
              {fieldConfig?.roll_number !== false &&
                hasValidValue(data.rollNo) && (
                  <>
                    <div>Roll No.</div>
                    <div>: {data.rollNo}</div>
                  </>
                )}

              {/* Note: Layout3 doesn't show parent name by design */}
            </>
          ) : (
            <>
              {/* Roll Number as ID - Configurable */}
              {fieldConfig?.roll_number !== false &&
                hasValidValue(data.rollNo) && (
                  <>
                    <div>ID</div>
                    <div>: {data.rollNo}</div>
                  </>
                )}

              {/* Department - Configurable */}
              {fieldConfig?.department !== false &&
                hasValidValue(data.department) && (
                  <>
                    <div>Department</div>
                    <div>: {data.department || "General"}</div>
                  </>
                )}
            </>
          )}

          {/* Address - Configurable */}
          {fieldConfig?.address !== false && hasValidValue(data.address) && (
            <>
              <div>Address</div>
              <div>: {data.address}</div>
            </>
          )}

          {/* Contact Number - Configurable */}
          {fieldConfig?.contact_no !== false &&
            hasValidValue(data.contactNo) && (
              <>
                <div>{isStudent ? "Guardian No." : "Contact No."}</div>
                <div>: {data.contactNo}</div>
              </>
            )}
        </div>
      </div>

      {/* Principal Signature Section - Bottom Right */}
      <div className="flex justify-end mb-2">
        <div className={`relative ${styles.borderColor} border-0 rounded-lg `}>
          <div className="flex flex-col items-center">
            <div className="flex flex-col items-center justify-center">
              {data.principalSignature ? (
                <img
                  src={data.principalSignature}
                  alt="Principal Signature"
                  className="h-5 object-contain mb-1"
                  onError={(e) => {
                    console.error(
                      "Failed to load principal signature:",
                      data.principalSignature
                    );
                    e.currentTarget.style.display = "none";
                  }}
                />
              ) : (
                <div className="h-4 flex items-center justify-center text-[7px] text-gray-400 mb-1 italic">
                  No Signature
                </div>
              )}
              <span
                className={`w-12 border-t-2 ${styles.borderColor} border-dotted`}
              ></span>
            </div>
            <div className="text-[7px] text-gray-700 font-semibold text-center mt-1 tracking-wide">
              PRINCIPAL
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentInfo;
