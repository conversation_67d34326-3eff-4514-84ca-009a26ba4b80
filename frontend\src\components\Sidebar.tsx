import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { useSchool } from "../contexts/SchoolContext";
import { useAuth } from "../contexts/AuthContext";
import { useNotification } from "../contexts/NotificationContext";

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onToggle }) => {
  const location = useLocation();
  const { schools, selectedSchool, selectSchool, loading } = useSchool();
  const { user, logout } = useAuth();
  const { showSuccess, showError } = useNotification();
  const [showSchoolDropdown, setShowSchoolDropdown] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const allNavigationItems = [
    {
      path: "/",
      label: "Dashboard",
      icon: "🏠",
      description: "Overview and statistics",
      roles: ["admin", "user", "school_admin"],
    },
    {
      path: "/persons",
      label: "Person Management",
      icon: "👥",
      description: "Manage students and staff",
      roles: ["admin", "user", "school_admin"],
    },
    {
      path: "/id-card",
      label: "ID Cards",
      icon: "🆔",
      description: "Generate and print ID cards",
      roles: ["admin", "user", "school_admin"],
    },
    {
      path: "/schools",
      label: "School Management",
      icon: "🏫",
      description: "Manage school information",
      roles: ["admin", "school_admin"],
    },
    {
      path: "/users",
      label: "User Management",
      icon: "👤",
      description: "Create and manage system users",
      roles: ["admin"],
    },
  ];

  // Filter navigation items based on user role
  const navigationItems = allNavigationItems.filter((item) =>
    item.roles.includes(user?.role || "user")
  );

  const isActiveRoute = (path: string) => {
    if (path === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(path);
  };

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await logout();
      showSuccess("Logged out successfully");
    } catch (error) {
      showError("Failed to logout");
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
        fixed top-0 left-0 h-full bg-white shadow-lg z-50 transform transition-transform duration-300 ease-in-out
        ${isOpen ? "translate-x-0" : "-translate-x-full"}
        lg:translate-x-0 lg:static lg:z-auto
        w-80 flex flex-col
      `}
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">ID</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  ID Card System
                </h1>
                <p className="text-sm text-gray-500">Multi-School Management</p>
              </div>
            </div>
            <button
              onClick={onToggle}
              className="lg:hidden p-2 rounded-md hover:bg-gray-100"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* School Selector */}
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <button
              onClick={() => setShowSchoolDropdown(!showSchoolDropdown)}
              disabled={loading}
              className="w-full flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm">🏫</span>
                </div>
                <div className="text-left">
                  <p className="text-sm font-medium text-gray-900">
                    {loading
                      ? "Loading..."
                      : selectedSchool?.name || "Select School"}
                  </p>
                  <p className="text-xs text-gray-500">
                    {schools.length} school{schools.length !== 1 ? "s" : ""}{" "}
                    available
                  </p>
                </div>
              </div>
              <svg
                className={`w-5 h-5 text-gray-400 transition-transform ${
                  showSchoolDropdown ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {/* School Dropdown */}
            {showSchoolDropdown && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                {schools.map((school) => (
                  <button
                    key={school.id}
                    onClick={() => {
                      selectSchool(school);
                      setShowSchoolDropdown(false);
                    }}
                    className={`w-full text-left p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 ${
                      selectedSchool?.id === school.id
                        ? "bg-blue-50 text-blue-700"
                        : "text-gray-900"
                    }`}
                  >
                    <div className="font-medium">{school.name}</div>
                    <div className="text-sm text-gray-500 truncate">
                      {school.address}
                    </div>
                  </button>
                ))}
                {schools.length === 0 && (
                  <div className="p-3 text-center text-gray-500">
                    No schools available
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {navigationItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              onClick={() => {
                onToggle(); // Close sidebar on mobile after navigation
                setShowSchoolDropdown(false);
              }}
              className={`
                flex items-center space-x-3 p-3 rounded-lg transition-colors
                ${
                  isActiveRoute(item.path)
                    ? "bg-blue-100 text-blue-700 border-l-4 border-blue-600"
                    : "text-gray-700 hover:bg-gray-100"
                }
              `}
            >
              <span className="text-xl">{item.icon}</span>
              <div>
                <div className="font-medium">{item.label}</div>
                <div className="text-sm opacity-75">{item.description}</div>
              </div>
            </Link>
          ))}
        </nav>

        {/* User Info & Logout */}
        <div className="p-4 border-t border-gray-200">
          {user && (
            <div className="mb-4">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium text-sm">
                    {user.first_name
                      ? user.first_name.charAt(0).toUpperCase()
                      : user.username.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {user.first_name && user.last_name
                      ? `${user.first_name} ${user.last_name}`
                      : user.username}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {user.role.replace("_", " ").toUpperCase()}
                  </p>
                  {user.School && (
                    <p className="text-xs text-gray-500 truncate">
                      {user.School.name}
                    </p>
                  )}
                </div>
              </div>

              <button
                onClick={handleLogout}
                disabled={isLoggingOut}
                className="w-full mt-3 flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoggingOut ? (
                  <div className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Logging out...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                      />
                    </svg>
                    Logout
                  </div>
                )}
              </button>
            </div>
          )}

          <div className="text-center text-sm text-gray-500">
            <p>© 2024 ID Card System</p>
            <p>Multi-School Management</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
