import React, { useState, useEffect, useCallback } from "react";
import axios from "axios";
import { useSchool } from "../contexts/SchoolContext";
import { PersonUpload } from "../components/PersonUpload";
import { PersonList } from "../components/PersonList";
import { Breadcrumb, useBreadcrumb, PageHeader } from "../components/common";
import { Icon } from "@iconify/react/dist/iconify.js";
import { personService, Person } from "../services/personService";
import { useNotification } from "../contexts/NotificationContext";

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

const PersonsPage: React.FC = () => {
  const { selectedSchool } = useSchool();
  const [persons, setPersons] = useState<Person[]>([]);
  const [loading, setLoading] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 1000,
    total: 0,
    pages: 0,
  });
  const { createBreadcrumbs } = useBreadcrumb();
  const { showNotification } = useNotification();

  const fetchPersons = useCallback(async () => {
    if (!selectedSchool) return;

    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/api/persons`, {
        params: {
          school_id: selectedSchool.id,
          limit: 1000, // Set a high limit to get all students
        },
      });
      console.log(
        `Fetched ${response.data.persons?.length || 0} persons from API`
      );
      console.log("API Response:", response.data);
      setPersons(response.data.persons || []);
    } catch (error) {
      console.error("Error fetching persons:", error);
    } finally {
      setLoading(false);
    }
  }, [selectedSchool]);

  useEffect(() => {
    if (selectedSchool) {
      fetchPersons();
    }
  }, [selectedSchool, fetchPersons]);

  const handleUploadSuccess = () => {
    fetchPersons();
    setShowUploadModal(false);
  };

  const handleUploadClick = () => {
    setShowUploadModal(true);
  };

  const handlePersonSelect = (person: Person) => {
    // This could navigate to a person detail page or open a modal
    console.log("Selected person:", person);
  };

  const handlePersonDelete = (person: Person) => {
    // Refresh the persons list after deletion
    fetchPersons();
  };

  const handleDeleteAllData = async () => {
    if (!selectedSchool) return;

    const confirmMessage = `Are you sure you want to delete ALL data for ${selectedSchool.name}?\n\nThis will permanently delete:\n• All students, staff, and non-teaching staff\n• All uploaded photos\n• All associated records\n\nThis action cannot be undone!`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      setDeleting(true);
      const response = await personService.deleteAllSchoolData(
        selectedSchool.id
      );

      showNotification(
        `Successfully deleted all data for ${selectedSchool.name}. ${response.data.deletedCount} persons and ${response.data.deletedPhotos} photos removed.`,
        "success"
      );

      // Refresh the persons list
      await fetchPersons();
    } catch (error: any) {
      console.error("Error deleting school data:", error);
      showNotification(
        error.response?.data?.error || "Failed to delete school data",
        "error"
      );
    } finally {
      setDeleting(false);
    }
  };

  if (!selectedSchool) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-6xl mb-4">👥</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No School Selected
          </h2>
          <p className="text-gray-600">
            Please select a school from the sidebar to manage persons.
          </p>
        </div>
      </div>
    );
  }

  // Create breadcrumb items
  const breadcrumbItems = createBreadcrumbs(
    { label: "Dashboard", path: "/", icon: "mdi:home" },
    { label: "Person Management", icon: "mdi:account-group" }
  );

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbItems} />

      {/* Header with Person List */}
      <PageHeader
        title="Person Management"
        description={`Manage students and staff for ${selectedSchool.name}`}
        buttonText="Upload Persons"
        onButtonClick={handleUploadClick}
      >
        {/* Stats Section */}
        <div className="mt-6 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {persons.length}
                </div>
                <div className="text-sm text-gray-500">Total Persons</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {persons.filter((p) => p.type === "student").length}
                </div>
                <div className="text-sm text-gray-500">Students</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {persons.filter((p) => p.type === "staff").length}
                </div>
                <div className="text-sm text-gray-500">Staff</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={fetchPersons}
                className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                disabled={loading}
              >
                <Icon
                  icon="mdi:refresh"
                  className={`w-4 h-4 ${loading ? "animate-spin" : ""}`}
                />
                <span>Refresh</span>
              </button>
              <button
                onClick={handleDeleteAllData}
                className="flex items-center space-x-2 px-3 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
                disabled={loading || deleting}
              >
                <Icon
                  icon="mdi:delete-forever"
                  className={`w-4 h-4 ${deleting ? "animate-pulse" : ""}`}
                />
                <span>{deleting ? "Deleting..." : "Delete All Data"}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Person List */}
        <div className="mt-6">
          <PersonList
            persons={persons}
            loading={loading}
            onPersonSelect={handlePersonSelect}
            onRefresh={fetchPersons}
            onPersonDelete={handlePersonDelete}
          />
        </div>
      </PageHeader>

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Upload Persons
              </h2>
              <button
                onClick={() => setShowUploadModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <Icon icon="mdi:close" className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <PersonUpload
                onUploadSuccess={handleUploadSuccess}
                selectedSchoolId={selectedSchool.id}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PersonsPage;
