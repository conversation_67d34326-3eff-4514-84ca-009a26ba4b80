const multer = require("multer");
const path = require("path");
const fs = require("fs");

// Ensure upload directories exist
const uploadPaths = {
  csv: path.join(__dirname, "../../uploads/csv"),
  photos: path.join(__dirname, "../../uploads/photos"),
  schoolAssets: path.join(__dirname, "../../uploads/school-assets"),
};

Object.values(uploadPaths).forEach((dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Multer error handling middleware
const handleMulterError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    console.error("Multer error:", err);

    switch (err.code) {
      case "LIMIT_FILE_SIZE":
        return res.status(400).json({
          error: "File too large",
          code: "FILE_TOO_LARGE",
          maxSize: "100MB",
        });
      case "LIMIT_FILE_COUNT":
        return res.status(400).json({
          error: "Too many files",
          code: "TOO_MANY_FILES",
        });
      case "LIMIT_FIELD_KEY":
        return res.status(400).json({
          error: "Field name too long",
          code: "FIELD_NAME_TOO_LONG",
        });
      case "LIMIT_FIELD_VALUE":
        return res.status(400).json({
          error: "Field value too long",
          code: "FIELD_VALUE_TOO_LONG",
        });
      case "LIMIT_FIELD_COUNT":
        return res.status(400).json({
          error: "Too many fields",
          code: "TOO_MANY_FIELDS",
        });
      case "LIMIT_UNEXPECTED_FILE":
        return res.status(400).json({
          error:
            "Unexpected file field. Please check the field name matches the expected field.",
          code: "LIMIT_UNEXPECTED_FILE",
          expectedField: req.route?.path?.includes("photo")
            ? "photo"
            : req.route?.path?.includes("csv")
            ? "csv"
            : req.route?.path?.includes("logo")
            ? "logo"
            : req.route?.path?.includes("stamp")
            ? "stamp"
            : req.route?.path?.includes("signature")
            ? "signature"
            : "unknown",
        });
      default:
        return res.status(400).json({
          error: "File upload error",
          code: "UPLOAD_ERROR",
          details: err.message,
        });
    }
  }

  // Handle other file filter errors
  if (err.message && err.message.includes("Only")) {
    return res.status(400).json({
      error: err.message,
      code: "INVALID_FILE_TYPE",
    });
  }

  next(err);
};

// Storage configuration for CSV files
const csvStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadPaths.csv);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, `${uniqueSuffix}-${file.originalname}`);
  },
});

// Storage configuration for photos
const photoStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadPaths.photos);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    cb(null, `${uniqueSuffix}${ext}`);
  },
});

// Storage configuration for school assets
const schoolAssetStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadPaths.schoolAssets);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
  },
});

// File filters
const csvFileFilter = (req, file, cb) => {
  if (
    file.mimetype === "text/csv" ||
    path.extname(file.originalname).toLowerCase() === ".csv"
  ) {
    cb(null, true);
  } else {
    cb(new Error("Only CSV files are allowed"), false);
  }
};

const imageFileFilter = (req, file, cb) => {
  const allowedMimes = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error("Only image files (JPEG, PNG, GIF) are allowed"), false);
  }
};

// Upload configurations
const csvUpload = multer({
  storage: csvStorage,
  fileFilter: csvFileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
});

const photoUpload = multer({
  storage: photoStorage,
  fileFilter: imageFileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB per photo
  },
});

const schoolAssetUpload = multer({
  storage: schoolAssetStorage,
  fileFilter: imageFileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
});

module.exports = {
  csvUpload,
  photoUpload,
  schoolAssetUpload,
  uploadPaths,
  handleMulterError,
};
