import React from 'react';
import ProfileImage from '../common/ProfileImage';
import { getPhotoUrl, getLogoUrl, getPrincipalSignatureUrl } from '../../utils/dataMapping';

/**
 * Test component to verify that image loading works correctly
 * and no external URLs cause CORS errors
 */
const ImageLoadingTest: React.FC = () => {
  // Test cases for different scenarios
  const testCases = [
    {
      name: "Empty photo path",
      photoPath: "",
      expectedBehavior: "Should show initials"
    },
    {
      name: "Null photo path", 
      photoPath: null,
      expectedBehavior: "Should show initials"
    },
    {
      name: "Undefined photo path",
      photoPath: undefined,
      expectedBehavior: "Should show initials"
    },
    {
      name: "Valid local photo path",
      photoPath: "processed_1751819043822-401503959.png",
      expectedBehavior: "Should load from API or show initials if not found"
    },
    {
      name: "External URL (should be blocked)",
      photoPath: "https://i.pinimg.com/736x/93/f4/0e/93f40ec756290812571be534e12bcfe7.jpg",
      expectedBehavior: "Should show initials (external URL blocked)"
    }
  ];

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-bold mb-6">Image Loading Test</h1>
      <p className="text-gray-600 mb-8">
        This test verifies that ProfileImage component handles different photo path scenarios correctly
        and that external URLs are blocked to prevent CORS errors.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {testCases.map((testCase, index) => {
          const photoUrl = getPhotoUrl(testCase.photoPath as any);
          
          return (
            <div key={index} className="bg-white p-4 rounded-lg shadow">
              <h3 className="font-semibold mb-2">{testCase.name}</h3>
              <p className="text-sm text-gray-600 mb-4">{testCase.expectedBehavior}</p>
              
              <div className="mb-4">
                <ProfileImage
                  src={photoUrl}
                  name="Test User"
                  designation="student"
                  size="lg"
                  shape="rounded"
                  showInitials={true}
                />
              </div>
              
              <div className="text-xs text-gray-500">
                <p><strong>Input:</strong> {JSON.stringify(testCase.photoPath)}</p>
                <p><strong>Generated URL:</strong> {photoUrl || "(empty)"}</p>
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h2 className="font-semibold text-blue-800 mb-2">Expected Results:</h2>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• All test cases should show initials (no CORS errors)</li>
          <li>• External URLs should be blocked and logged to console</li>
          <li>• Valid local paths should attempt to load from API</li>
          <li>• Empty/null/undefined paths should show initials immediately</li>
        </ul>
      </div>
    </div>
  );
};

export default ImageLoadingTest;
