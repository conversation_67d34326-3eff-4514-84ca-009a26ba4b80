import React from "react";

export type TableData<T = Record<string, any>> = {
  _id: string;
} & T;

export type ColumnConfig<T> = {
  key: string;
  header: string;
  width?: string;
  render?: (value: any, row: TableData<T>) => React.ReactNode;
  sortable?: boolean;
  className?: string;
};

export type PaginationDetails = {
  currentPage: number;
  limit: number;
  totalCount?: number;
  totalPages?: number;
};

export type SortConfig = {
  key: string;
  direction: "asc" | "desc";
};

export type FilterConfig = {
  key: string;
  value: any;
  operator?:
    | "equals"
    | "contains"
    | "startsWith"
    | "endsWith"
    | "gt"
    | "lt"
    | "gte"
    | "lte";
};

export type TableState = {
  selectedRows: Set<string>;
  sortConfig?: SortConfig;
  filters: FilterConfig[];
  searchQuery: string;
};

export type TableActionType =
  | "view"
  | "edit"
  | "delete"
  | "toggle"
  | "print"
  | "custom";

export interface TableActionConfig<T> {
  type: TableActionType;
  icon?: string;
  color?: string;
  title?: string;
  onClick?: (row: TableData<T>) => void;
  show?: boolean | ((row: TableData<T>) => boolean);
  disabled?: boolean | ((row: TableData<T>) => boolean);
}

export interface MasterTableProps<T extends Record<string, any>> {
  data: TableData<T>[];
  columns: ColumnConfig<T>[];
  loading?: boolean;

  // Selection
  onRowSelect?: (selectedIds: string[]) => void;
  showSelectAll?: boolean;

  // Actions
  actions?: TableActionConfig<T>[];
  showActions?: boolean;

  // Row interactions
  onRowClick?: (row: TableData<T>) => void;

  // Styling
  showBg?: boolean;
  hasPadding?: boolean;
  className?: string;

  // Pagination
  showPagination?: boolean;
  paginationDetails?: PaginationDetails;
  onPageChange?: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;

  // Empty state
  emptyStateMessage?: string;
  emptyStateIcon?: string;

  // Sorting
  sortable?: boolean;
  onSort?: (key: string, direction: "asc" | "desc") => void;
  sortConfig?: SortConfig;

  // Filtering & Search
  searchable?: boolean;
  searchPlaceholder?: string;
  onSearch?: (query: string) => void;
  filters?: FilterConfig[];
  onFilter?: (filters: FilterConfig[]) => void;

  // Selection modes
  selectionMode?: "single" | "multiple";
  bulkActions?: TableActionConfig<T>[];

  // Row interactions
  onRowDoubleClick?: (row: TableData<T>) => void;
  rowClassName?: (row: TableData<T>) => string;

  // Styling variants
  size?: "sm" | "md" | "lg";
  variant?: "default" | "striped" | "bordered";

  // Empty state enhancement
  emptyStateAction?: {
    label: string;
    onClick: () => void;
  };

  // Performance
  virtualized?: boolean;
  rowHeight?: number;
  maxHeight?: number;

  // Accessibility
  ariaLabel?: string;
  ariaDescription?: string;

  // Advanced features
  resizable?: boolean;
  reorderable?: boolean;
  exportable?: boolean;
  onExport?: (format: "csv" | "xlsx" | "pdf") => void;

  // Loading states
  loadingRows?: number;
  skeleton?: boolean;
}

export interface TableActionProps<T extends Record<string, any>> {
  row: TableData<T>;
  actions: TableActionConfig<T>[];
  className?: string;
}

// Default action configurations
export const DEFAULT_ACTIONS = {
  view: {
    type: "view" as TableActionType,
    icon: "mdi:eye-outline",
    color: "#2275FC",
    title: "View",
  },
  edit: {
    type: "edit" as TableActionType,
    icon: "lucide:pen-line",
    color: "#F6B827",
    title: "Edit",
  },
  delete: {
    type: "delete" as TableActionType,
    icon: "material-symbols:delete-outline-rounded",
    color: "#FF0000",
    title: "Delete",
  },
  print: {
    type: "print" as TableActionType,
    icon: "material-symbols:print",
    color: "#FF4D4D",
    title: "Print",
  },
  toggle: {
    type: "toggle" as TableActionType,
    title: "Toggle Status",
  },
} as const;
