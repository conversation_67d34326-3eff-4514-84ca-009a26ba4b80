import React, { useState } from "react";
import axios from "axios";
import { generateMultipleCardsPDF } from "../utils/pdfGenerator";
import {
  createCardConfig,
  createDefaultSchool,
  createMaiValleyCardConfig,
} from "./Card";
import { Person } from "../services/personService";

interface Stats {
  total: number;
  breakdown: {
    student?: number;
    staff?: number;
    non_teaching?: number;
  };
}

interface StatisticsProps {
  stats: Stats | null;
  persons: Person[];
}

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

export const Statistics: React.FC<StatisticsProps> = ({ stats, persons }) => {
  const [selectedDesign, setSelectedDesign] = useState<
    "vertical" | "mai_valley"
  >("vertical");

  const downloadBatchPDF = async (
    type: "student" | "staff" | "non_teaching"
  ) => {
    try {
      // Filter persons by type
      const filteredPersons = persons.filter((person) => person.type === type);

      if (filteredPersons.length === 0) {
        alert(`No ${type} records found`);
        return;
      }

      // Get school data
      const schoolResponse = await axios.get(`${API_BASE_URL}/api/school/all`);
      const school =
        schoolResponse.data.length > 0
          ? schoolResponse.data[0]
          : createDefaultSchool();

      // Create card configs for all persons of this type
      const cardConfigs = filteredPersons.map((person) => {
        if (selectedDesign === "mai_valley") {
          return createMaiValleyCardConfig(
            { ...person, csv_batch_id: person.csv_batch_id ?? 0 },
            school
          );
        } else {
          return createCardConfig(
            { ...person, csv_batch_id: person.csv_batch_id ?? 0 },
            school,
            {
              type: person.type,
              theme: {
                primaryColor:
                  person.type === "student"
                    ? "#FF6B35"
                    : person.type === "staff"
                    ? "#4A90E2"
                    : "#4CAF50",
                secondaryColor:
                  person.type === "student"
                    ? "#FF8A65"
                    : person.type === "staff"
                    ? "#64B5F6"
                    : "#66BB6A",
                backgroundColor: "#FFFFFF",
                textColor: "#000000",
                layout: "standard",
              },
              dimensions: {
                width: 325,
                height: 500,
                printWidth: "54mm",
                printHeight: "85.6mm",
              },
              showElements: {
                logo: true,
                photo: true,
                qrCode: false,
                decorativeElements: true,
              },
              renderMode: "vertical",
            }
          );
        }
      });

      // Generate PDF with multiple cards
      const pdfBytes = await generateMultipleCardsPDF(cardConfigs);

      // Create download link
      const blob = new Blob([pdfBytes], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${type}_cards.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading batch PDF:", error);
      alert("Failed to download batch PDF");
    }
  };

  const getPhotoStats = () => {
    const withPhoto = persons.filter((p) => p.photo_path).length;
    const withoutPhoto = persons.length - withPhoto;
    return { withPhoto, withoutPhoto };
  };

  const getClassBreakdown = () => {
    const students = persons.filter((p) => p.type === "student");
    const classMap: { [key: string]: number } = {};

    students.forEach((student) => {
      if (student.class) {
        classMap[student.class] = (classMap[student.class] || 0) + 1;
      }
    });

    return Object.entries(classMap)
      .map(([className, count]) => ({
        class: className,
        count,
      }))
      .sort((a, b) => a.class.localeCompare(b.class));
  };

  const photoStats = getPhotoStats();
  const classBreakdown = getClassBreakdown();

  const StatCard: React.FC<{
    title: string;
    value: number;
    color: string;
    icon: string;
    subtitle?: string;
  }> = ({ title, value, color, icon, subtitle }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 ${color} rounded-md p-3`}>
          <span className="text-2xl">{icon}</span>
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-500 truncate">
              {title}
            </dt>
            <dd className="text-3xl font-semibold text-gray-900">{value}</dd>
            {subtitle && <dd className="text-sm text-gray-600">{subtitle}</dd>}
          </dl>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-6">
          Statistics Overview
        </h2>

        {/* Main Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Total Persons"
            value={stats?.total || 0}
            color="bg-gray-500"
            icon="👥"
          />
          <StatCard
            title="Students"
            value={stats?.breakdown.student || 0}
            color="bg-birta-orange"
            icon="🎓"
          />
          <StatCard
            title="Teaching Staff"
            value={stats?.breakdown.staff || 0}
            color="bg-birta-blue"
            icon="👨‍🏫"
          />
          <StatCard
            title="Non-Teaching Staff"
            value={stats?.breakdown.non_teaching || 0}
            color="bg-birta-green"
            icon="👷"
          />
        </div>

        {/* Photo Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white border rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Photo Upload Status
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">With Photos</span>
                <div className="flex items-center">
                  <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{
                        width: `${
                          persons.length > 0
                            ? (photoStats.withPhoto / persons.length) * 100
                            : 0
                        }%`,
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {photoStats.withPhoto} (
                    {persons.length > 0
                      ? Math.round(
                          (photoStats.withPhoto / persons.length) * 100
                        )
                      : 0}
                    %)
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Without Photos</span>
                <div className="flex items-center">
                  <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                    <div
                      className="bg-red-500 h-2 rounded-full"
                      style={{
                        width: `${
                          persons.length > 0
                            ? (photoStats.withoutPhoto / persons.length) * 100
                            : 0
                        }%`,
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {photoStats.withoutPhoto} (
                    {persons.length > 0
                      ? Math.round(
                          (photoStats.withoutPhoto / persons.length) * 100
                        )
                      : 0}
                    %)
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Type Distribution Chart */}
          <div className="bg-white border rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Type Distribution
            </h3>
            <div className="space-y-3">
              {stats &&
                Object.entries(stats.breakdown).map(([type, count]) => {
                  const percentage =
                    stats.total > 0
                      ? Math.round((count / stats.total) * 100)
                      : 0;
                  const colors = {
                    student: "bg-birta-orange",
                    staff: "bg-birta-blue",
                    non_teaching: "bg-birta-green",
                  };
                  const labels = {
                    student: "Students",
                    staff: "Teaching Staff",
                    non_teaching: "Non-Teaching Staff",
                  };

                  return (
                    <div
                      key={type}
                      className="flex items-center justify-between"
                    >
                      <span className="text-sm text-gray-600">
                        {labels[type as keyof typeof labels]}
                      </span>
                      <div className="flex items-center">
                        <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                          <div
                            className={`${
                              colors[type as keyof typeof colors]
                            } h-2 rounded-full`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-gray-900">
                          {count} ({percentage}%)
                        </span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>

        {/* Class Breakdown */}
        {classBreakdown.length > 0 && (
          <div className="bg-white border rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Student Class Distribution
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {classBreakdown.map(({ class: className, count }) => (
                <div
                  key={className}
                  className="text-center p-3 bg-orange-50 rounded-lg"
                >
                  <div className="text-2xl font-bold text-birta-orange">
                    {count}
                  </div>
                  <div className="text-sm text-gray-600">{className}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="bg-gray-50 rounded-lg p-6 mt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Quick Actions
          </h3>

          {/* Design Selection */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-700 mb-3">
              Card Design
            </h4>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="design"
                  value="vertical"
                  checked={selectedDesign === "vertical"}
                  onChange={(e) =>
                    setSelectedDesign(
                      e.target.value as "vertical" | "mai_valley"
                    )
                  }
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">Vertical Design</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="design"
                  value="mai_valley"
                  checked={selectedDesign === "mai_valley"}
                  onChange={(e) =>
                    setSelectedDesign(
                      e.target.value as "vertical" | "mai_valley"
                    )
                  }
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">Mai Valley Design</span>
              </label>
            </div>
          </div>

          {/* PDF Download */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">
              PDF Download
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <button
                onClick={() => downloadBatchPDF("student")}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm flex items-center justify-center space-x-2"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <span>📄 Download Student Cards PDF</span>
              </button>
              <button
                onClick={() => downloadBatchPDF("staff")}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm flex items-center justify-center space-x-2"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <span>📄 Download Staff Cards PDF</span>
              </button>
              <button
                onClick={() => downloadBatchPDF("non_teaching")}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm flex items-center justify-center space-x-2"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <span>📄 Download Non-Teaching PDF</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
