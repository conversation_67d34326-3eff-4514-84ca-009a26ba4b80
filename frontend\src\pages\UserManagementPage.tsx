import React, { useState } from "react";
import { Breadcrumb, useBreadcrumb, PageHeader } from "../components/common";
import CreateUserForm from "../components/UserManagement/CreateUserForm";
import { useAuth } from "../contexts/AuthContext";
import { useNotification } from "../contexts/NotificationContext";
import { Icon } from "@iconify/react/dist/iconify.js";

const UserManagementPage: React.FC = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [creatingQuickUser, setCreatingQuickUser] = useState<string | null>(
    null
  );
  const { createBreadcrumbs } = useBreadcrumb();
  const { register } = useAuth();
  const { showSuccess, showError } = useNotification();

  // Create breadcrumb items
  const breadcrumbItems = createBreadcrumbs(
    { label: "Dashboard", path: "/", icon: "mdi:home" },
    { label: "User Management", icon: "mdi:account-cog" }
  );

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
  };

  const handleCreateCancel = () => {
    setShowCreateForm(false);
  };

  const handleQuickUserCreate = async (userType: string) => {
    setCreatingQuickUser(userType);

    try {
      const userData = {
        username: `test${userType.toLowerCase().replace("_", "")}`,
        email: `${userType.toLowerCase().replace("_", "")}@test.com`,
        password: "TestPass123!",
        first_name: "Test",
        last_name:
          userType === "admin"
            ? "Admin"
            : userType === "school_admin"
            ? "School Admin"
            : "User",
        role: userType as "admin" | "user" | "school_admin",
      };

      await register(userData);
      showSuccess(`Test ${userType.replace("_", " ")} created successfully!`);
    } catch (error: any) {
      showError(error.message || `Failed to create test ${userType}`);
    } finally {
      setCreatingQuickUser(null);
    }
  };

  const quickCreateTestUsers = [
    {
      name: "Test Admin",
      username: "testadmin",
      email: "<EMAIL>",
      role: "admin" as const,
      description: "Full system administrator with all permissions",
    },
    {
      name: "Test School Admin",
      username: "testschooladmin",
      email: "<EMAIL>",
      role: "school_admin" as const,
      description: "School administrator with school-specific permissions",
    },
    {
      name: "Test User",
      username: "testuser",
      email: "<EMAIL>",
      role: "user" as const,
      description: "Regular user with basic permissions",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbItems} />

      {/* Page Header */}
      <PageHeader
        title="User Management"
        description="Create and manage system users for testing and administration"
        buttonText="+ Create User"
        onButtonClick={() => setShowCreateForm(true)}
      />

      {/* Quick Test Users Section */}
      {!showCreateForm && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Quick Test User Creation
          </h3>
          <p className="text-gray-600 mb-6">
            Create common test users quickly with predefined settings. All users
            will have the password:{" "}
            <code className="bg-gray-100 px-2 py-1 rounded text-sm">
              TestPass123!
            </code>
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {quickCreateTestUsers.map((user) => (
              <div
                key={user.username}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">{user.name}</h4>
                    <p className="text-sm text-gray-500">@{user.username}</p>
                  </div>
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      user.role === "admin"
                        ? "bg-red-100 text-red-800"
                        : user.role === "school_admin"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-green-100 text-green-800"
                    }`}
                  >
                    {user.role.replace("_", " ")}
                  </span>
                </div>

                <p className="text-sm text-gray-600 mb-4">{user.description}</p>

                <div className="space-y-2 text-xs text-gray-500">
                  <div className="flex items-center">
                    <Icon icon="mdi:email" className="w-4 h-4 mr-2" />
                    {user.email}
                  </div>
                  <div className="flex items-center">
                    <Icon icon="mdi:key" className="w-4 h-4 mr-2" />
                    TestPass123!
                  </div>
                </div>

                <button
                  onClick={() => handleQuickUserCreate(user.role)}
                  disabled={creatingQuickUser === user.role}
                  className="w-full mt-4 px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {creatingQuickUser === user.role ? (
                    <div className="flex items-center justify-center">
                      <Icon
                        icon="mdi:loading"
                        className="w-4 h-4 mr-2 animate-spin"
                      />
                      Creating...
                    </div>
                  ) : (
                    `Create ${user.name}`
                  )}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Create User Form */}
      {showCreateForm && (
        <CreateUserForm
          onSuccess={handleCreateSuccess}
          onCancel={handleCreateCancel}
        />
      )}

      {/* Instructions Section */}
      {!showCreateForm && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <Icon
              icon="mdi:information"
              className="w-6 h-6 text-blue-600 mr-3 mt-0.5"
            />
            <div>
              <h3 className="text-lg font-medium text-blue-900 mb-2">
                User Management Instructions
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>
                  • <strong>Admin:</strong> Full system access, can manage all
                  schools and users
                </p>
                <p>
                  • <strong>School Admin:</strong> Can manage their assigned
                  school's data and users
                </p>
                <p>
                  • <strong>User:</strong> Basic access to view and manage
                  assigned school data
                </p>
                <p>
                  • All test users are created with strong passwords for
                  security
                </p>
                <p>
                  • Users can be assigned to specific schools during creation
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagementPage;
