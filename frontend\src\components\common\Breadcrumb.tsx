import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";

export interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: string;
  isActive?: boolean;
  onClick?: () => void;
  disabled?: boolean;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  separator?: string;
  showHome?: boolean;
  homeIcon?: string;
  className?: string;
  autoGenerate?: boolean;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "pills" | "arrows";
  maxItems?: number;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items = [],
  separator = "mdi:chevron-right",
  showHome = true,
  homeIcon = "mdi:home",
  className = "",
  autoGenerate = false,
}) => {
  const location = useLocation();

  // Auto-generate breadcrumbs from current path
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split("/").filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    if (showHome) {
      breadcrumbs.push({
        label: "Dashboard",
        path: "/",
        icon: homeIcon,
        isActive: location.pathname === "/",
      });
    }

    // Route mapping for better labels
    const routeLabels: Record<string, string> = {
      persons: "Person Management",
      schools: "School Management",
      "id-card": "ID Cards",
      auth: "Authentication",
    };

    pathSegments.forEach((segment, index) => {
      const path = "/" + pathSegments.slice(0, index + 1).join("/");
      const isLast = index === pathSegments.length - 1;

      breadcrumbs.push({
        label:
          routeLabels[segment] ||
          segment.charAt(0).toUpperCase() + segment.slice(1),
        path: isLast ? undefined : path,
        isActive: isLast,
      });
    });

    return breadcrumbs;
  };

  const breadcrumbItems = autoGenerate ? generateBreadcrumbs() : items;

  if (breadcrumbItems.length === 0) {
    return null;
  }

  return (
    <nav
      className={`flex items-center space-x-2 text-sm ${className}`}
      aria-label="Breadcrumb"
    >
      <ol className="flex items-center space-x-2">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <Icon
                icon={separator}
                className="text-gray-400 mx-2"
                fontSize={16}
              />
            )}

            {item.path && !item.isActive ? (
              <Link
                to={item.path}
                className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
              >
                {item.icon && <Icon icon={item.icon} fontSize={16} />}
                <span>{item.label}</span>
              </Link>
            ) : (
              <span
                className={`flex items-center space-x-1 ${
                  item.isActive ? "text-gray-900 font-medium" : "text-gray-600"
                }`}
              >
                {item.icon && <Icon icon={item.icon} fontSize={16} />}
                <span>{item.label}</span>
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

// Hook for easy breadcrumb management
export const useBreadcrumb = () => {
  const location = useLocation();

  const createBreadcrumb = (
    label: string,
    path?: string,
    icon?: string
  ): BreadcrumbItem => ({
    label,
    path,
    icon,
    isActive: !path || location.pathname === path,
  });

  const createBreadcrumbs = (
    ...items: Array<{
      label: string;
      path?: string;
      icon?: string;
    }>
  ): BreadcrumbItem[] => {
    return items.map((item, index) => ({
      ...item,
      isActive: index === items.length - 1,
    }));
  };

  return {
    createBreadcrumb,
    createBreadcrumbs,
    currentPath: location.pathname,
  };
};

export default Breadcrumb;
