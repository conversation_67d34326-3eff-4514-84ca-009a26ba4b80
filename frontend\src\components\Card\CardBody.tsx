import React from 'react';
import { CardBodyProps, CARD_LABELS } from './types';
import CardPhoto from './CardPhoto';

const CardBody: React.FC<CardBodyProps> = ({
  person,
  template,
  photoBase64,
  onPhotoError,
  onPhotoLoad
}) => {
  const renderPersonDetails = () => {
    const details = [];

    if (person.type === 'student') {
      if (person.class) {
        details.push(
          <div key="class" className="detail-row">
            <span className="detail-label">Class</span>
            <span className="detail-value">: {person.class}</span>
          </div>
        );
      }
      
      if (person.roll_number) {
        details.push(
          <div key="roll" className="detail-row">
            <span className="detail-label">Roll No.</span>
            <span className="detail-value">: {person.roll_number}</span>
          </div>
        );
      }
      
      if (person.parents_name) {
        details.push(
          <div key="parents" className="detail-row">
            <span className="detail-label">Parents Name</span>
            <span className="detail-value">: {person.parents_name}</span>
          </div>
        );
      }
    } else {
      // For staff and non-teaching staff
      details.push(
        <div key="id" className="detail-row">
          <span className="detail-label">Employee ID</span>
          <span className="detail-value">: {person.person_id}</span>
        </div>
      );
      
      if (person.class) {
        details.push(
          <div key="department" className="detail-row">
            <span className="detail-label">Department</span>
            <span className="detail-value">: {person.class}</span>
          </div>
        );
      }
    }

    if (person.contact_no) {
      details.push(
        <div key="contact" className="detail-row">
          <span className="detail-label">Contact No.</span>
          <span className="detail-value">: {person.contact_no}</span>
        </div>
      );
    }

    return details;
  };

  return (
    <div className="card-content">
      {template.showElements.photo && (
        <div className="photo-section">
          <CardPhoto
            person={person}
            photoBase64={photoBase64}
            onError={onPhotoError}
            onLoad={onPhotoLoad}
          />
        </div>
      )}
      
      <div className="info-section">
        <div>
          <div className="person-name">{person.name}</div>
          <div className="person-type">
            {CARD_LABELS[person.type]}
          </div>
        </div>
        
        <div className="person-details">
          {renderPersonDetails()}
        </div>
      </div>
    </div>
  );
};

export default CardBody;
