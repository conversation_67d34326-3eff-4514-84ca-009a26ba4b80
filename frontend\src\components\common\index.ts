// Common component exports
export { default as ProfileImage } from "./ProfileImage";
export { default as EditablePersonAvatar } from "./EditablePersonAvatar";
export { default as Breadcrumb, useBreadcrumb } from "./Breadcrumb";
export { default as PageHeader } from "./PageHeader";
export { default as Header } from "./Header";

// Type exports
export type { BreadcrumbItem } from "./Breadcrumb";
export type { PageHeaderProps } from "./PageHeader";
export type { HeaderProps } from "./Header";

// Re-export enhanced table components for easy access (without MasterTable to avoid conflict)
export { EnhancedMasterTable } from "../Table/EnhancedMasterTable";
export { default as TableAction } from "../Table/TableAction";
export { default as Pagination } from "../Table/Pagination";

// Export table types
export type {
  MasterTableProps,
  TableData,
  ColumnConfig,
  TableActionConfig,
  SortConfig,
  FilterConfig,
  TableState,
} from "../Table/types";
