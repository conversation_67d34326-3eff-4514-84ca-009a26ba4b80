'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // First, add a new numeric person_id_numeric column
      await queryInterface.addColumn('persons', 'person_id_numeric', {
        type: Sequelize.INTEGER,
        allowNull: true,
        autoIncrement: false,
      }, { transaction });

      // Create a sequence for auto-incrementing person IDs per school
      // We'll use a simple approach: max existing ID + 1 for each school
      const schools = await queryInterface.sequelize.query(
        'SELECT DISTINCT school_id FROM persons WHERE school_id IS NOT NULL',
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      // For each school, assign numeric IDs starting from 1
      for (const school of schools) {
        const persons = await queryInterface.sequelize.query(
          'SELECT id FROM persons WHERE school_id = :schoolId ORDER BY created_at ASC',
          { 
            replacements: { schoolId: school.school_id },
            type: Sequelize.QueryTypes.SELECT,
            transaction 
          }
        );

        // Assign sequential numeric IDs
        for (let i = 0; i < persons.length; i++) {
          await queryInterface.sequelize.query(
            'UPDATE persons SET person_id_numeric = :numericId WHERE id = :personId',
            {
              replacements: { 
                numericId: i + 1,
                personId: persons[i].id 
              },
              transaction
            }
          );
        }
      }

      // Now make the numeric column NOT NULL
      await queryInterface.changeColumn('persons', 'person_id_numeric', {
        type: Sequelize.INTEGER,
        allowNull: false,
        autoIncrement: false,
      }, { transaction });

      // Create a composite unique index on school_id + person_id_numeric
      await queryInterface.addIndex('persons', ['school_id', 'person_id_numeric'], {
        unique: true,
        name: 'persons_school_id_person_id_numeric_unique',
        transaction
      });

      // Rename the old person_id column to person_id_legacy
      await queryInterface.renameColumn('persons', 'person_id', 'person_id_legacy', { transaction });

      // Rename the new numeric column to person_id
      await queryInterface.renameColumn('persons', 'person_id_numeric', 'person_id', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Reverse the changes
      await queryInterface.renameColumn('persons', 'person_id', 'person_id_numeric', { transaction });
      await queryInterface.renameColumn('persons', 'person_id_legacy', 'person_id', { transaction });
      
      // Remove the unique index
      await queryInterface.removeIndex('persons', 'persons_school_id_person_id_numeric_unique', { transaction });
      
      // Remove the numeric column
      await queryInterface.removeColumn('persons', 'person_id_numeric', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
