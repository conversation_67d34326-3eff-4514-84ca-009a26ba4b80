import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { School } from "../types";
import { schoolService } from "../../../services/schoolService";

interface ValidityDateModalProps {
  isVisible: boolean;
  school: School | null;
  onClose: () => void;
  onSuccess: () => void;
}

const ValidityDateModal: React.FC<ValidityDateModalProps> = ({
  isVisible,
  school,
  onClose,
  onSuccess,
}) => {
  const [validityDate, setValidityDate] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isVisible && school) {
      // Set current validity date or default to 2 years from now
      if (school.validity_date) {
        setValidityDate(school.validity_date);
      } else {
        const defaultDate = new Date();
        defaultDate.setFullYear(defaultDate.getFullYear() + 2);
        setValidityDate(defaultDate.toISOString().split('T')[0]);
      }
      setError(null);
    } else {
      setValidityDate("");
      setError(null);
    }
  }, [isVisible, school]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!school || !validityDate) return;

    setLoading(true);
    setError(null);

    try {
      await schoolService.updateValidityDate(school.id, validityDate);
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error("Failed to update validity date:", error);
      setError(
        error.response?.data?.error || 
        "Failed to update validity date. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Update Validity Date
          </h2>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <Icon icon="mdi:close" className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6">
          {school && (
            <div className="mb-4">
              <p className="text-sm text-gray-600">
                Update validity date for <span className="font-medium">{school.name}</span>
              </p>
            </div>
          )}

          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <div className="mb-6">
            <label htmlFor="validity_date" className="block text-sm font-medium text-gray-700 mb-2">
              Validity Date
            </label>
            <input
              type="date"
              id="validity_date"
              value={validityDate}
              onChange={(e) => setValidityDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
              disabled={loading}
              min={new Date().toISOString().split('T')[0]} // Prevent past dates
            />
            <p className="mt-1 text-xs text-gray-500">
              This date will appear on all ID cards as "Valid Upto"
            </p>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !validityDate}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              {loading && (
                <Icon icon="mdi:loading" className="w-4 h-4 mr-2 animate-spin" />
              )}
              {loading ? "Updating..." : "Update Validity Date"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ValidityDateModal;
