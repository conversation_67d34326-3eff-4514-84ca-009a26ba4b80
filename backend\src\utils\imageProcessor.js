const sharp = require('sharp');
const path = require('path');
const fs = require('fs');

/**
 * Image processing utilities using Sharp
 */
class ImageProcessor {
  /**
   * Process uploaded image with optimization
   * @param {string} inputPath - Path to input image
   * @param {string} outputPath - Path to save processed image
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} Processing result
   */
  static async processImage(inputPath, outputPath, options = {}) {
    try {
      const {
        width = 800,
        height = 800,
        quality = 90,
        format = 'jpeg',
        crop = null,
        brightness = 0,
        contrast = 0,
        saturation = 0
      } = options;

      let pipeline = sharp(inputPath);

      // Get image metadata
      const metadata = await pipeline.metadata();
      
      // Apply cropping if specified
      if (crop && crop.x !== undefined && crop.y !== undefined && crop.width && crop.height) {
        pipeline = pipeline.extract({
          left: Math.round(crop.x),
          top: Math.round(crop.y),
          width: Math.round(crop.width),
          height: Math.round(crop.height)
        });
      }

      // Resize image while maintaining aspect ratio
      pipeline = pipeline.resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true
      });

      // Apply brightness, contrast, and saturation adjustments
      if (brightness !== 0 || contrast !== 0 || saturation !== 0) {
        // Convert percentage values to Sharp's expected format
        const brightnessMultiplier = 1 + (brightness / 100);
        const contrastMultiplier = 1 + (contrast / 100);
        const saturationMultiplier = 1 + (saturation / 100);

        pipeline = pipeline.modulate({
          brightness: brightnessMultiplier,
          saturation: saturationMultiplier
        });

        // Apply contrast using linear transformation
        if (contrast !== 0) {
          pipeline = pipeline.linear(contrastMultiplier, -(128 * contrastMultiplier) + 128);
        }
      }

      // Set output format and quality
      if (format === 'jpeg') {
        pipeline = pipeline.jpeg({ quality, progressive: true });
      } else if (format === 'png') {
        pipeline = pipeline.png({ quality, progressive: true });
      } else if (format === 'webp') {
        pipeline = pipeline.webp({ quality });
      }

      // Save processed image
      await pipeline.toFile(outputPath);

      // Get output file stats
      const outputStats = fs.statSync(outputPath);
      const outputMetadata = await sharp(outputPath).metadata();

      return {
        success: true,
        originalSize: metadata.size,
        processedSize: outputStats.size,
        originalDimensions: {
          width: metadata.width,
          height: metadata.height
        },
        processedDimensions: {
          width: outputMetadata.width,
          height: outputMetadata.height
        },
        compressionRatio: ((metadata.size - outputStats.size) / metadata.size * 100).toFixed(2),
        format: outputMetadata.format
      };

    } catch (error) {
      console.error('Image processing error:', error);
      throw new Error(`Image processing failed: ${error.message}`);
    }
  }

  /**
   * Create thumbnail from image
   * @param {string} inputPath - Path to input image
   * @param {string} outputPath - Path to save thumbnail
   * @param {number} size - Thumbnail size (square)
   * @returns {Promise<Object>} Processing result
   */
  static async createThumbnail(inputPath, outputPath, size = 150) {
    try {
      await sharp(inputPath)
        .resize(size, size, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({ quality: 80 })
        .toFile(outputPath);

      return {
        success: true,
        thumbnailPath: outputPath,
        size: size
      };
    } catch (error) {
      console.error('Thumbnail creation error:', error);
      throw new Error(`Thumbnail creation failed: ${error.message}`);
    }
  }

  /**
   * Validate image file
   * @param {string} filePath - Path to image file
   * @returns {Promise<Object>} Validation result
   */
  static async validateImage(filePath) {
    try {
      const metadata = await sharp(filePath).metadata();
      
      const validFormats = ['jpeg', 'jpg', 'png', 'webp', 'gif'];
      const isValidFormat = validFormats.includes(metadata.format.toLowerCase());
      
      const maxDimension = 10000; // 10k pixels max
      const isValidSize = metadata.width <= maxDimension && metadata.height <= maxDimension;
      
      return {
        valid: isValidFormat && isValidSize,
        format: metadata.format,
        width: metadata.width,
        height: metadata.height,
        size: metadata.size,
        errors: [
          ...(!isValidFormat ? [`Invalid format: ${metadata.format}. Supported: ${validFormats.join(', ')}`] : []),
          ...(!isValidSize ? [`Image too large: ${metadata.width}x${metadata.height}. Max: ${maxDimension}x${maxDimension}`] : [])
        ]
      };
    } catch (error) {
      return {
        valid: false,
        errors: [`Invalid image file: ${error.message}`]
      };
    }
  }

  /**
   * Get image information
   * @param {string} filePath - Path to image file
   * @returns {Promise<Object>} Image information
   */
  static async getImageInfo(filePath) {
    try {
      const metadata = await sharp(filePath).metadata();
      const stats = fs.statSync(filePath);
      
      return {
        format: metadata.format,
        width: metadata.width,
        height: metadata.height,
        channels: metadata.channels,
        density: metadata.density,
        hasAlpha: metadata.hasAlpha,
        fileSize: stats.size,
        aspectRatio: (metadata.width / metadata.height).toFixed(2)
      };
    } catch (error) {
      throw new Error(`Failed to get image info: ${error.message}`);
    }
  }

  /**
   * Optimize image for web
   * @param {string} inputPath - Path to input image
   * @param {string} outputPath - Path to save optimized image
   * @param {Object} options - Optimization options
   * @returns {Promise<Object>} Optimization result
   */
  static async optimizeForWeb(inputPath, outputPath, options = {}) {
    const {
      maxWidth = 1200,
      maxHeight = 1200,
      quality = 85,
      progressive = true
    } = options;

    return this.processImage(inputPath, outputPath, {
      width: maxWidth,
      height: maxHeight,
      quality,
      format: 'jpeg'
    });
  }
}

module.exports = ImageProcessor;
