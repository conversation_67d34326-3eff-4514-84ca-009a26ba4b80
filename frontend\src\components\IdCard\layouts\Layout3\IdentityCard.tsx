import CardHeader from "./CardHeader";
import StudentInfo from "./StudentInfo";
import CardFooter from "./CardFooter";
import { BgColorType, StudentData } from "./types";

interface IdentityCardProps {
  bgColor?: BgColorType;
  data: StudentData;
}

const IdentityCard = ({ bgColor, data }: IdentityCardProps) => {
  // Auto-determine color based on person type if not explicitly provided
  const getColorByPersonType = (
    personType: "student" | "staff" | "non_teaching"
  ): BgColorType => {
    switch (personType) {
      case "student":
        return "orange"; // Orange for students
      case "staff":
        return "blue"; // Blue for teaching staff
      case "non_teaching":
        return "green"; // Green for non-teaching staff
      default:
        return "orange";
    }
  };

  const finalBgColor =
    bgColor || getColorByPersonType(data.personType || "staff");

  return (
    <div className="w-[204px] h-[324px] bg-white shadow-lg  overflow-hidden font-sans text-black flex flex-col">
      <CardHeader
        bgColor={finalBgColor}
        logo={data.companyLogo}
        studentImage={data.image}
        studentName={data.studentName}
        personType={data.personType}
      />
      <StudentInfo data={data} />
      <CardFooter bgColor={finalBgColor} data={data} />
    </div>
  );
};

export default IdentityCard;
