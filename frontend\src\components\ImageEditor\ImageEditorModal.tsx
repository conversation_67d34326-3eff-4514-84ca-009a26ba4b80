import React, { useState, useEffect, useCallback, useRef } from "react";
import { Icon } from "@iconify/react";
import CropControls from "./CropControls";
import BrightnessControls from "./BrightnessControls";
import {
  CropArea,
  ImageFilters,
  loadImage,
  processImage,
  calculateAspectRatioCrop,
  validateCropArea,
} from "../../utils/imageProcessing";

interface ImageEditorModalProps {
  file: File;
  isOpen: boolean;
  onClose: () => void;
  onSave: (processedFile: File) => void;
  aspectRatio?: number;
  maxWidth?: number;
  maxHeight?: number;
}

type EditorTab = "crop" | "adjust" | "change";

const ASPECT_RATIOS = [
  { label: "Free", value: undefined },
  { label: "1:1", value: 1 },
  { label: "4:3", value: 4 / 3 },
  { label: "3:4", value: 3 / 4 },
  { label: "16:9", value: 16 / 9 },
  { label: "9:16", value: 9 / 16 },
];

const ImageEditorModal: React.FC<ImageEditorModalProps> = ({
  file,
  isOpen,
  onClose,
  onSave,
  aspectRatio: defaultAspectRatio,
  maxWidth = 800,
  maxHeight = 800,
}) => {
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [cropArea, setCropArea] = useState<CropArea>({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });
  const [filters, setFilters] = useState<ImageFilters>({
    brightness: 0,
    contrast: 0,
    saturation: 0,
  });
  const [activeTab, setActiveTab] = useState<EditorTab>("crop");
  const [aspectRatio, setAspectRatio] = useState<number | undefined>(
    defaultAspectRatio
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);

  // New photo upload states
  const [newPhotoFile, setNewPhotoFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load image when file changes
  useEffect(() => {
    if (file && isOpen) {
      loadImage(file)
        .then((img) => {
          setImage(img);

          // Initialize crop area
          const initialCrop = aspectRatio
            ? calculateAspectRatioCrop(
                img.naturalWidth,
                img.naturalHeight,
                aspectRatio
              )
            : {
                x: img.naturalWidth * 0.1,
                y: img.naturalHeight * 0.1,
                width: img.naturalWidth * 0.8,
                height: img.naturalHeight * 0.8,
              };

          setCropArea(
            validateCropArea(initialCrop, img.naturalWidth, img.naturalHeight)
          );

          // Create initial preview URL
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          if (ctx) {
            canvas.width = img.naturalWidth;
            canvas.height = img.naturalHeight;
            ctx.drawImage(img, 0, 0);
            setPreviewUrl(canvas.toDataURL("image/jpeg", 0.9));
          }
        })
        .catch((error) => {
          console.error("Failed to load image:", error);
        });
    }
  }, [file, isOpen, aspectRatio]);

  // Update preview when crop or filters change
  useEffect(() => {
    if (image && previewCanvasRef.current) {
      updatePreview();
    }
  }, [image, cropArea, filters]);

  const updatePreview = useCallback(async () => {
    if (!image || !previewCanvasRef.current) return;

    try {
      const result = await processImage(image, cropArea, filters, 300, 300);
      const ctx = previewCanvasRef.current.getContext("2d");
      if (ctx) {
        previewCanvasRef.current.width = 300;
        previewCanvasRef.current.height = 300;
        ctx.clearRect(0, 0, 300, 300);
        ctx.drawImage(result.canvas, 0, 0, 300, 300);
      }
    } catch (error) {
      console.error("Failed to update preview:", error);
    }
  }, [image, cropArea, filters]);

  const handleCropChange = useCallback(
    (newCropArea: CropArea) => {
      if (!image) return;
      const validatedCrop = validateCropArea(
        newCropArea,
        image.naturalWidth,
        image.naturalHeight
      );
      setCropArea(validatedCrop);
    },
    [image]
  );

  const handleAspectRatioChange = useCallback(
    (newAspectRatio: number | undefined) => {
      setAspectRatio(newAspectRatio);

      if (newAspectRatio && image) {
        const newCrop = calculateAspectRatioCrop(
          cropArea.width,
          cropArea.height,
          newAspectRatio
        );
        const adjustedCrop = {
          x: cropArea.x + (cropArea.width - newCrop.width) / 2,
          y: cropArea.y + (cropArea.height - newCrop.height) / 2,
          width: newCrop.width,
          height: newCrop.height,
        };
        handleCropChange(adjustedCrop);
      }
    },
    [image, cropArea, handleCropChange]
  );

  const handleFiltersReset = useCallback(() => {
    setFilters({ brightness: 0, contrast: 0, saturation: 0 });
  }, []);

  // New photo upload handlers
  const handleFileSelect = useCallback((selectedFile: File) => {
    if (selectedFile && selectedFile.type.startsWith("image/")) {
      setNewPhotoFile(selectedFile);
      // Switch to the new photo and reset to crop tab
      loadImage(selectedFile)
        .then((newImage) => {
          setImage(newImage);
          // Reset crop area to full image
          setCropArea({
            x: 0,
            y: 0,
            width: newImage.naturalWidth,
            height: newImage.naturalHeight,
          });
          // Reset filters
          setFilters({ brightness: 0, contrast: 0, saturation: 0 });
          // Switch to crop tab
          setActiveTab("crop");
        })
        .catch((error) => {
          console.error("Error loading new image:", error);
        });
    }
  }, []);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const handleSave = useCallback(async () => {
    if (!image) return;

    setIsProcessing(true);
    try {
      // Calculate output dimensions while maintaining aspect ratio
      let outputWidth = Math.min(cropArea.width, maxWidth);
      let outputHeight = Math.min(cropArea.height, maxHeight);

      const cropAspectRatio = cropArea.width / cropArea.height;
      if (outputWidth / outputHeight > cropAspectRatio) {
        outputWidth = outputHeight * cropAspectRatio;
      } else {
        outputHeight = outputWidth / cropAspectRatio;
      }

      const result = await processImage(
        image,
        cropArea,
        filters,
        outputWidth,
        outputHeight
      );

      // Convert blob to File
      const processedFile = new File([result.blob], file.name, {
        type: "image/jpeg",
        lastModified: Date.now(),
      });

      onSave(processedFile);
    } catch (error) {
      console.error("Failed to process image:", error);
    } finally {
      setIsProcessing(false);
    }
  }, [image, cropArea, filters, file.name, maxWidth, maxHeight, onSave]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900 flex items-center space-x-2">
            <Icon icon="mdi:image-edit" className="w-6 h-6" />
            <span>Edit Image</span>
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <Icon icon="mdi:close" className="w-6 h-6" />
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab("crop")}
            className={`px-6 py-3 font-medium text-sm transition-colors ${
              activeTab === "crop"
                ? "text-blue-600 border-b-2 border-blue-600 bg-blue-50"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            <Icon icon="mdi:crop" className="w-4 h-4 inline mr-2" />
            Crop
          </button>
          <button
            onClick={() => setActiveTab("adjust")}
            className={`px-6 py-3 font-medium text-sm transition-colors ${
              activeTab === "adjust"
                ? "text-blue-600 border-b-2 border-blue-600 bg-blue-50"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            <Icon icon="mdi:tune" className="w-4 h-4 inline mr-2" />
            Adjust
          </button>
          <button
            onClick={() => setActiveTab("change")}
            className={`px-6 py-3 font-medium text-sm transition-colors ${
              activeTab === "change"
                ? "text-blue-600 border-b-2 border-blue-600 bg-blue-50"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            <Icon icon="mdi:image-plus" className="w-4 h-4 inline mr-2" />
            Change Photo
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Main Editor Area */}
          <div className="flex-1 p-4 overflow-auto">
            {image && (
              <>
                {activeTab === "crop" && (
                  <div className="space-y-4">
                    {/* Aspect Ratio Controls */}
                    <div className="flex items-center space-x-2 mb-4">
                      <span className="text-sm font-medium text-gray-700">
                        Aspect Ratio:
                      </span>
                      {ASPECT_RATIOS.map((ratio) => (
                        <button
                          key={ratio.label}
                          onClick={() => handleAspectRatioChange(ratio.value)}
                          className={`px-3 py-1 text-xs rounded-md transition-colors ${
                            aspectRatio === ratio.value
                              ? "bg-blue-600 text-white"
                              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                          }`}
                        >
                          {ratio.label}
                        </button>
                      ))}
                    </div>

                    <CropControls
                      image={image}
                      cropArea={cropArea}
                      onCropChange={handleCropChange}
                      aspectRatio={aspectRatio}
                      showGrid={true}
                      className="max-h-96"
                    />
                  </div>
                )}

                {activeTab === "adjust" && (
                  <div className="space-y-4">
                    <BrightnessControls
                      filters={filters}
                      onFiltersChange={setFilters}
                      onReset={handleFiltersReset}
                    />
                  </div>
                )}

                {activeTab === "change" && (
                  <div className="space-y-4">
                    <div className="text-center">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Upload New Photo
                      </h3>
                      <p className="text-sm text-gray-600 mb-6">
                        Select a new photo to replace the current one. You can
                        then crop and adjust it as needed.
                      </p>
                    </div>

                    {/* File Upload Area */}
                    <div
                      className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                        dragActive
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-300 hover:border-gray-400"
                      }`}
                      onDragEnter={handleDragEnter}
                      onDragLeave={handleDragLeave}
                      onDragOver={handleDragOver}
                      onDrop={handleDrop}
                    >
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleFileInputChange}
                        className="hidden"
                      />

                      <div className="space-y-4">
                        <div className="mx-auto w-16 h-16 text-gray-400">
                          <Icon
                            icon="mdi:cloud-upload"
                            className="w-full h-full"
                          />
                        </div>

                        <div>
                          <button
                            onClick={() => fileInputRef.current?.click()}
                            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
                          >
                            Choose New Photo
                          </button>
                          <p className="mt-2 text-sm text-gray-500">
                            or drag and drop a photo here
                          </p>
                        </div>

                        <p className="text-xs text-gray-400">
                          Supports: JPG, PNG, GIF, WebP (Max 10MB)
                        </p>
                      </div>
                    </div>

                    {newPhotoFile && (
                      <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Icon
                            icon="mdi:check-circle"
                            className="w-5 h-5 text-green-600"
                          />
                          <div>
                            <p className="text-sm font-medium text-green-800">
                              New photo selected: {newPhotoFile.name}
                            </p>
                            <p className="text-xs text-green-600">
                              Switch to Crop tab to adjust the new photo
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>

          {/* Preview Panel */}
          <div className="w-80 border-l border-gray-200 p-4 bg-gray-50">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Preview
            </h3>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <canvas
                ref={previewCanvasRef}
                className="w-full h-auto max-w-full rounded-md"
                style={{ aspectRatio: "1" }}
              />

              {image && (
                <div className="mt-4 text-sm text-gray-600 space-y-1">
                  <div>
                    Original: {image.naturalWidth} × {image.naturalHeight}
                  </div>
                  <div>
                    Crop: {Math.round(cropArea.width)} ×{" "}
                    {Math.round(cropArea.height)}
                  </div>
                  <div className="text-xs text-gray-500">
                    Position: ({Math.round(cropArea.x)},{" "}
                    {Math.round(cropArea.y)})
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>

          <button
            onClick={handleSave}
            disabled={isProcessing || !image}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            {isProcessing ? (
              <>
                <Icon icon="mdi:loading" className="w-4 h-4 animate-spin" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <Icon icon="mdi:check" className="w-4 h-4" />
                <span>Apply Changes</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImageEditorModal;
