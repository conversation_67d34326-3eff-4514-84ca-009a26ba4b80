<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;f73c67f7-96a0-4c11-96ab-9d59fe1060b0&quot;,&quot;conversations&quot;:{&quot;f73c67f7-96a0-4c11-96ab-9d59fe1060b0&quot;:{&quot;id&quot;:&quot;f73c67f7-96a0-4c11-96ab-9d59fe1060b0&quot;,&quot;createdAtIso&quot;:&quot;2025-08-11T05:17:09.967Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-11T06:00:37.884Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;6c696846-fe12-46b0-9ffb-8a73dea3b751&quot;,&quot;request_message&quot;:&quot;for layout 4 i want to add a new field for students .ie date of birth, and recheck the valid date and i want to use nepali roman number for valid date &lt;style&gt;\r\n@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&amp;display=swap');\r\n&lt;/style&gt;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;for layout 4 i want to add a new field for students .ie date of birth, and recheck the valid date and i want to use nepali roman number for valid date &lt;style&gt;\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&amp;display=swap');\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;&lt;/style&gt;&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;for layout 4 i want to add a new field for students .ie date of birth, and recheck the valid date and i want to use nepali roman number for valid date &lt;style&gt;\r\n@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&amp;display=swap');\r\n&lt;/style&gt;&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Desktop/school_card_app/school-card-app&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Desktop/school_card_app/school-card-app&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-11T05:29:44.394Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-e773bf4a-ec7e-4753-bbc2-f266de6f2c54&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasAgentOnboarded&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;da854d55-4522-4edd-8d3b-6691aebfe9b7&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>