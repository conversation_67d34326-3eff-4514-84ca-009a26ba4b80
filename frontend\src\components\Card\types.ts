// Card component type definitions

export interface PersonData {
  id: number;
  person_id: string;
  name: string;
  type: "student" | "staff" | "non_teaching";
  class?: string;
  section?: string;
  roll_number?: string;
  parents_name?: string;
  contact_no?: string;
  photo_path?: string;
  department?: string; // For staff and non_teaching
  address?: string; // For all types
  date_of_birth?: string;
  csv_batch_id: number;
  created_at: string;
  updated_at: string;
}

export interface SchoolData {
  id?: number;
  name: string;
  address: string;
  phone: string;
  email: string;
  logo_path?: string;
  stamp_path?: string;
  signature_path?: string;
  validity_date?: string;
  created_at?: string;
  updated_at?: string;
}

export interface CardTheme {
  primaryColor: string;
  secondaryColor: string;
  textColor: string;
  backgroundColor: string;
  layout: "standard" | "compact" | "detailed";
}

export interface CardDimensions {
  width: number;
  height: number;
  printWidth: string; // "54mm" (2.125 inches - vertical width)
  printHeight: string; // "85.6mm" (3.375 inches - vertical height)
}

export interface CardTemplate {
  type: "student" | "staff" | "non_teaching";
  theme: CardTheme;
  dimensions: CardDimensions;
  showElements: {
    logo: boolean;
    photo: boolean;
    qrCode: boolean;
    decorativeElements: boolean;
  };
  renderMode?: "default" | "vertical" | "mai_valley";
}

export interface CardConfig {
  person: PersonData;
  school: SchoolData;
  template: CardTemplate;
}

export interface CardRendererProps {
  config: CardConfig;
  mode: "preview" | "print";
  className?: string;
  onPhotoError?: () => void;
  onPhotoLoad?: () => void;
}

export interface CardHeaderProps {
  school: SchoolData;
  template: CardTemplate;
  logoBase64?: string;
}

export interface CardBodyProps {
  person: PersonData;
  template: CardTemplate;
  photoBase64?: string;
  onPhotoError?: () => void;
  onPhotoLoad?: () => void;
}

export interface CardFooterProps {
  school: SchoolData;
  template: CardTemplate;
}

export interface CardPhotoProps {
  person: PersonData;
  photoBase64?: string;
  className?: string;
  onError?: () => void;
  onLoad?: () => void;
}

// Utility type for card colors - Birta Global School Template
export const CARD_COLORS = {
  student: "#4CAF50", // Green
  staff: "#4A90E2", // Blue
  non_teaching: "#FF6B35", // Orange
} as const;

// Utility type for card labels
export const CARD_LABELS = {
  student: "Student",
  staff: "Teaching Staff",
  non_teaching: "Non-Teaching Staff",
} as const;

// Default card dimensions (CR80 standard - vertical orientation matching frontend)
export const DEFAULT_DIMENSIONS: CardDimensions = {
  width: 325,
  height: 500,
  printWidth: "54mm", // 2.125 inches (vertical width)
  printHeight: "85.6mm", // 3.375 inches (vertical height)
};

// Default themes for each card type
export const DEFAULT_THEMES: Record<PersonData["type"], CardTheme> = {
  student: {
    primaryColor: CARD_COLORS.student,
    secondaryColor: "#66BB6A",
    textColor: "#333333",
    backgroundColor: "#FFFFFF",
    layout: "standard",
  },
  staff: {
    primaryColor: CARD_COLORS.staff,
    secondaryColor: "#64B5F6",
    textColor: "#333333",
    backgroundColor: "#FFFFFF",
    layout: "standard",
  },
  non_teaching: {
    primaryColor: CARD_COLORS.non_teaching,
    secondaryColor: "#FF8A65",
    textColor: "#333333",
    backgroundColor: "#FFFFFF",
    layout: "standard",
  },
};
