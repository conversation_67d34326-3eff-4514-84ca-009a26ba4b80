import { useState } from "react";
import axios from "axios";
import { SchoolFormData, School } from "../types";
import { useSchool } from "../../../contexts/SchoolContext";
import { useNotification } from "../../../contexts/NotificationContext";
import { processImageFile } from "../../../utils/imageUtils";

const initialFormData: SchoolFormData = {
  name: "",
  address: "",
  phone: "",
  email: "",
};

export const useSchoolForm = () => {
  const { createSchool, updateSchool, refreshSchools } = useSchool();
  const { showNotification } = useNotification();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingSchool, setEditingSchool] = useState<School | null>(null);
  const [formData, setFormData] = useState<SchoolFormData>(initialFormData);
  const [loading, setLoading] = useState(false);

  const API_BASE_URL =
    process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

  const uploadSchoolLogo = async (schoolId: number, file: File) => {
    // Process the image with validation and compression
    const result = await processImageFile(file, {
      maxSizeMB: 2,
      maxWidth: 1024,
      maxHeight: 1024,
      quality: 0.8,
      autoCompress: true,
    });

    if (!result.isValid) {
      throw new Error(result.error || "Invalid image file");
    }

    if (!result.file) {
      throw new Error("No file to upload");
    }

    const formData = new FormData();
    formData.append("logo", result.file);

    await axios.post(`${API_BASE_URL}/api/school/${schoolId}/logo`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  };

  const resetForm = () => {
    setFormData(initialFormData);
    setShowCreateForm(false);
    setEditingSchool(null);
  };

  const handleSubmit = async (e: React.FormEvent, logoFile?: File) => {
    e.preventDefault();
    setLoading(true);

    try {
      let school;
      if (editingSchool) {
        school = await updateSchool(editingSchool.id, formData);
        showNotification("School updated successfully", "success");
      } else {
        school = await createSchool(formData);
        showNotification("School created successfully", "success");
      }

      // Upload logo if provided
      if (logoFile && school) {
        await uploadSchoolLogo(school.id, logoFile);
      }

      resetForm();
      refreshSchools();
    } catch (error: any) {
      showNotification(error.message || "Failed to save school", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (school: School) => {
    setFormData({
      name: school.name,
      address: school.address,
      phone: school.phone,
      email: school.email || "",
    });
    setEditingSchool(school);
    setShowCreateForm(true);
  };

  const showForm = () => {
    setShowCreateForm(true);
  };

  return {
    showCreateForm,
    editingSchool,
    formData,
    loading,
    setFormData,
    handleSubmit,
    handleEdit,
    resetForm,
    showForm,
  };
};
