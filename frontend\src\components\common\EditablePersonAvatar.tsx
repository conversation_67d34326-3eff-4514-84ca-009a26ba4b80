import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { Person } from "../../services/personService";

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

interface EditablePersonAvatarProps {
  person: Person;
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  showEditIcon?: boolean;
  onEditClick?: (person: Person) => void;
  onClick?: (person: Person) => void;
  editIconPosition?: "center" | "corner";
  editIconStyle?: "overlay" | "badge";
}

const EditablePersonAvatar: React.FC<EditablePersonAvatarProps> = ({
  person,
  size = "md",
  className = "",
  showEditIcon = false,
  onEditClick,
  onClick,
  editIconPosition = "center",
  editIconStyle = "overlay",
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  const [isHovered, setIsHovered] = useState(false);

  const sizeClasses = {
    sm: "w-8 h-8 text-xs",
    md: "w-10 h-10 text-sm",
    lg: "w-16 h-16 text-lg",
    xl: "w-20 h-20 text-xl",
  };

  const editIconSizes = {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
    xl: "w-6 h-6",
  };

  const badgeIconSizes = {
    sm: "w-4 h-4",
    md: "w-5 h-5",
    lg: "w-6 h-6",
    xl: "w-7 h-7",
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "student":
        return "bg-blue-500";
      case "staff":
        return "bg-green-500";
      case "non_teaching":
        return "bg-purple-500";
      default:
        return "bg-gray-500";
    }
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onEditClick) {
      onEditClick(person);
    }
  };

  const handleAvatarClick = (e: React.MouseEvent) => {
    if (onClick && !isHovered && showEditIcon) {
      // Only trigger onClick if not hovering over edit icon
      onClick(person);
    } else if (onClick && !showEditIcon) {
      onClick(person);
    }
  };

  const renderEditIcon = () => {
    if (!showEditIcon || !onEditClick) return null;

    if (editIconStyle === "badge") {
      return (
        <div
          className="absolute -bottom-1 -right-1 bg-blue-600 rounded-full p-1 cursor-pointer hover:bg-blue-700 transition-colors shadow-lg border-2 border-white"
          onClick={handleEditClick}
          title="Edit photo"
        >
          <Icon
            icon={person.photo_path ? "mdi:image-edit" : "mdi:camera-plus"}
            className={`${badgeIconSizes[size]} text-white`}
          />
        </div>
      );
    }

    // Overlay style
    return (
      <div
        className={`absolute inset-0 rounded-full bg-black bg-opacity-50 flex items-center justify-center cursor-pointer transition-opacity duration-200 ${
          isHovered ? "opacity-100" : "opacity-0"
        }`}
        onClick={handleEditClick}
        title={person.photo_path ? "Edit photo" : "Add photo"}
      >
        <Icon
          icon={person.photo_path ? "mdi:image-edit" : "mdi:camera-plus"}
          className={`${editIconSizes[size]} text-white`}
        />
      </div>
    );
  };

  // Show initials if no photo or image failed to load
  if (!person.photo_path || imageError) {
    return (
      <div
        className={`${
          sizeClasses[size]
        } rounded-full flex items-center justify-center text-white font-medium ${getTypeColor(
          person.type
        )} ${className} relative group ${onClick ? "cursor-pointer" : ""}`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleAvatarClick}
      >
        {person.name.charAt(0).toUpperCase()}
        {renderEditIcon()}
      </div>
    );
  }

  return (
    <div
      className={`${
        sizeClasses[size]
      } rounded-full overflow-hidden ${className} relative group ${
        onClick ? "cursor-pointer" : ""
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleAvatarClick}
    >
      {imageLoading && (
        <div
          className={`absolute inset-0 ${
            sizeClasses[size]
          } rounded-full flex items-center justify-center text-white font-medium ${getTypeColor(
            person.type
          )}`}
        >
          {person.name.charAt(0).toUpperCase()}
        </div>
      )}
      <img
        src={`${API_BASE_URL}/api/cards/photos/${
          person.photo_path
        }?t=${Date.now()}`}
        alt={person.name}
        className={`w-full h-full object-cover ${
          imageLoading ? "opacity-0" : "opacity-100"
        } transition-opacity duration-300`}
        onError={handleImageError}
        onLoad={handleImageLoad}
      />
      {!imageLoading && renderEditIcon()}
    </div>
  );
};

export default EditablePersonAvatar;
