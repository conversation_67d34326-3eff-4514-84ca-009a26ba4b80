import React from "react";
import { Icon } from "@iconify/react";
import { ImageFilters } from "../../utils/imageProcessing";

interface BrightnessControlsProps {
  filters: ImageFilters;
  onFiltersChange: (filters: ImageFilters) => void;
  onReset: () => void;
  className?: string;
}

interface SliderControlProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  icon: string;
  unit?: string;
}

const SliderControl: React.FC<SliderControlProps> = ({
  label,
  value,
  onChange,
  min = -100,
  max = 100,
  step = 1,
  icon,
  unit = "",
}) => {
  const percentage = ((value - min) / (max - min)) * 100;

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Icon icon={icon} className="w-4 h-4 text-gray-600" />
          <label className="text-sm font-medium text-gray-700">{label}</label>
        </div>
        <span className="text-sm text-gray-500 min-w-[3rem] text-right">
          {value > 0 ? "+" : ""}
          {value}
          {unit}
        </span>
      </div>

      <div className="relative">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          style={{
            background: `linear-gradient(to right, #e5e7eb 0%, #e5e7eb ${percentage}%, #3b82f6 ${percentage}%, #3b82f6 100%)`,
          }}
        />

        {/* Center marker */}
        <div
          className="absolute top-1/2 w-0.5 h-4 bg-gray-400 transform -translate-y-1/2 pointer-events-none"
          style={{ left: "50%" }}
        />
      </div>
    </div>
  );
};

const BrightnessControls: React.FC<BrightnessControlsProps> = ({
  filters,
  onFiltersChange,
  onReset,
  className = "",
}) => {
  const handleBrightnessChange = (brightness: number) => {
    onFiltersChange({ ...filters, brightness });
  };

  const handleContrastChange = (contrast: number) => {
    onFiltersChange({ ...filters, contrast });
  };

  const handleSaturationChange = (saturation: number) => {
    onFiltersChange({ ...filters, saturation });
  };

  const hasChanges =
    filters.brightness !== 0 ||
    filters.contrast !== 0 ||
    filters.saturation !== 0;

  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
          <Icon icon="mdi:tune" className="w-5 h-5" />
          <span>Adjustments</span>
        </h3>

        {hasChanges && (
          <button
            onClick={onReset}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center space-x-1"
          >
            <Icon icon="mdi:refresh" className="w-4 h-4" />
            <span>Reset</span>
          </button>
        )}
      </div>

      <div className="space-y-6">
        <SliderControl
          label="Brightness"
          value={filters.brightness}
          onChange={handleBrightnessChange}
          icon="mdi:brightness-6"
        />

        <SliderControl
          label="Contrast"
          value={filters.contrast}
          onChange={handleContrastChange}
          icon="mdi:contrast"
        />

        <SliderControl
          label="Saturation"
          value={filters.saturation}
          onChange={handleSaturationChange}
          icon="mdi:palette"
        />
      </div>

      {/* Preset buttons */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">
          Quick Presets
        </h4>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() =>
              onFiltersChange({ brightness: 20, contrast: 10, saturation: 15 })
            }
            className="px-3 py-2 text-xs bg-yellow-50 text-yellow-700 rounded-md hover:bg-yellow-100 transition-colors"
          >
            <Icon icon="mdi:weather-sunny" className="w-4 h-4 mx-auto mb-1" />
            Bright
          </button>

          <button
            onClick={() =>
              onFiltersChange({ brightness: -10, contrast: 25, saturation: 20 })
            }
            className="px-3 py-2 text-xs bg-purple-50 text-purple-700 rounded-md hover:bg-purple-100 transition-colors"
          >
            <Icon
              icon="mdi:image-filter-vintage"
              className="w-4 h-4 mx-auto mb-1"
            />
            Vivid
          </button>

          <button
            onClick={() =>
              onFiltersChange({
                brightness: -15,
                contrast: 15,
                saturation: -20,
              })
            }
            className="px-3 py-2 text-xs bg-gray-50 text-gray-700 rounded-md hover:bg-gray-100 transition-colors"
          >
            <Icon
              icon="mdi:image-filter-black-white"
              className="w-4 h-4 mx-auto mb-1"
            />
            Muted
          </button>

          <button
            onClick={() =>
              onFiltersChange({
                brightness: 10,
                contrast: -10,
                saturation: -30,
              })
            }
            className="px-3 py-2 text-xs bg-orange-50 text-orange-700 rounded-md hover:bg-orange-100 transition-colors"
          >
            <Icon
              icon="mdi:image-filter-vintage"
              className="w-4 h-4 mx-auto mb-1"
            />
            Vintage
          </button>
        </div>
      </div>

      {/* Filter summary */}
      {hasChanges && (
        <div className="mt-4 p-3 bg-blue-50 rounded-md">
          <div className="flex items-center space-x-2 text-sm text-blue-800">
            <Icon icon="mdi:information" className="w-4 h-4" />
            <span>
              {filters.brightness !== 0 &&
                `Brightness: ${filters.brightness > 0 ? "+" : ""}${
                  filters.brightness
                } `}
              {filters.contrast !== 0 &&
                `Contrast: ${filters.contrast > 0 ? "+" : ""}${
                  filters.contrast
                } `}
              {filters.saturation !== 0 &&
                `Saturation: ${filters.saturation > 0 ? "+" : ""}${
                  filters.saturation
                }`}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default BrightnessControls;
