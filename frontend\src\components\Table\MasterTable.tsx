import React, { useState, useMemo } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { MasterTableProps, TableData, ColumnConfig } from "./types";
import TableAction from "./TableAction";
import Pagination from "./Pagination";

export const MasterTable = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  onRowSelect,
  showSelectAll = true,
  actions = [],
  showActions = true,
  onRowClick,
  showBg = true,
  hasPadding = true,
  className = "",
  showPagination = false,
  paginationDetails,
  onPageChange,
  onItemsPerPageChange,
  emptyStateMessage = "No data was found",
  emptyStateIcon = "mdi:database-off",
  sortable = false,
  onSort,
  sortConfig,
}: MasterTableProps<T>) => {
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());

  const handleRowSelect = (row: TableData<T>) => {
    const newSelectedRows = new Set(selectedRows);
    if (newSelectedRows.has(row._id)) {
      newSelectedRows.delete(row._id);
    } else {
      newSelectedRows.add(row._id);
    }
    setSelectedRows(newSelectedRows);
    onRowSelect?.(Array.from(newSelectedRows));
  };

  const handleSelectAll = () => {
    if (selectedRows.size === data.length) {
      setSelectedRows(new Set());
      onRowSelect?.([]);
    } else {
      const allIds = data.map((row) => row._id);
      setSelectedRows(new Set(allIds));
      onRowSelect?.(allIds);
    }
  };

  const handleSort = (key: string) => {
    if (!sortable || !onSort) return;

    const direction =
      sortConfig?.key === key && sortConfig?.direction === "asc"
        ? "desc"
        : "asc";
    onSort(key, direction);
  };

  const renderSortIcon = (column: ColumnConfig<T>) => {
    if (!sortable || !column.sortable) return null;

    const isActive = sortConfig?.key === column.key;
    const direction = sortConfig?.direction;

    return (
      <Icon
        icon={
          isActive
            ? direction === "asc"
              ? "mdi:arrow-up"
              : "mdi:arrow-down"
            : "mdi:unfold-more-horizontal"
        }
        className={`ml-1 transition-colors ${
          isActive ? "text-blue-600" : "text-gray-400"
        }`}
        fontSize={14}
      />
    );
  };

  const LoadingState = () => (
    <tr>
      <td
        colSpan={
          columns.length +
          (showSelectAll ? 1 : 0) +
          (showActions && actions.length > 0 ? 1 : 0)
        }
        className="text-center py-12"
      >
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-500">Loading...</p>
        </div>
      </td>
    </tr>
  );

  const EmptyState = () => (
    <tr>
      <td
        colSpan={
          columns.length +
          (showSelectAll ? 1 : 0) +
          (showActions && actions.length > 0 ? 1 : 0)
        }
        className="text-center py-12"
      >
        <div className="flex flex-col items-center">
          <Icon
            icon={emptyStateIcon}
            className="text-gray-400 mb-4"
            fontSize={48}
          />
          <p className="text-gray-500">{emptyStateMessage}</p>
        </div>
      </td>
    </tr>
  );

  const visibleActions = useMemo(
    () => actions.filter((action) => action.show !== false),
    [actions]
  );

  return (
    <div className={`flex flex-col w-full ${className}`}>
      <div className="w-full overflow-auto">
        <div className="min-w-full max-w-screen-md w-full h-fit">
          <section className="rounded-lg w-full">
            <table className="min-w-full w-full">
              <thead className={showBg ? "bg-gray-50" : ""}>
                <tr className="justify-between">
                  {showSelectAll && (
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium"
                    >
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded"
                        checked={
                          data.length > 0 && selectedRows.size === data.length
                        }
                        onChange={handleSelectAll}
                        aria-label="Select all rows"
                      />
                    </th>
                  )}

                  {columns.map((column, index) => (
                    <th
                      key={`${column.key}-${index}`}
                      scope="col"
                      className={`${
                        hasPadding ? "px-6" : ""
                      } py-3 text-left text-sm font-semibold text-gray-900 tracking-wider ${
                        column.width ? `w-[${column.width}]` : ""
                      } ${
                        sortable && column.sortable
                          ? "cursor-pointer hover:bg-gray-100"
                          : ""
                      } ${column.className || ""}`}
                      onClick={() =>
                        sortable && column.sortable && handleSort(column.key)
                      }
                    >
                      <div className="flex items-center">
                        {column.header}
                        {renderSortIcon(column)}
                      </div>
                    </th>
                  ))}

                  {showActions && visibleActions.length > 0 && (
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-sm font-semibold text-gray-900 tracking-wider"
                    >
                      Actions
                    </th>
                  )}
                </tr>
              </thead>

              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <LoadingState />
                ) : data.length === 0 ? (
                  <EmptyState />
                ) : (
                  data.map((row, index) => (
                    <tr
                      key={`${row._id}-${index}`}
                      className={`${
                        index % 2 === 0 && showBg ? "bg-gray-50" : ""
                      } ${
                        onRowClick ? "cursor-pointer hover:bg-blue-50" : ""
                      } transition-colors`}
                      onClick={() => onRowClick?.(row)}
                    >
                      {showSelectAll && (
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded"
                            checked={selectedRows.has(row._id)}
                            onChange={(e) => {
                              e.stopPropagation();
                              handleRowSelect(row);
                            }}
                            aria-label={`Select row ${row._id}`}
                          />
                        </td>
                      )}

                      {columns.map((column, colIndex) => (
                        <td
                          key={`${row._id}-${colIndex}`}
                          className={`${
                            hasPadding ? "px-6" : ""
                          } py-4 whitespace-nowrap text-sm text-gray-900 ${
                            column.className || ""
                          }`}
                        >
                          {column.render
                            ? column.render(row[column.key], row)
                            : typeof row[column.key] === "boolean"
                            ? row[column.key]
                              ? "Yes"
                              : "No"
                            : row[column.key] ?? "N/A"}
                        </td>
                      ))}

                      {showActions && visibleActions.length > 0 && (
                        <TableAction row={row} actions={visibleActions} />
                      )}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </section>
        </div>
      </div>

      {showPagination && paginationDetails && (
        <section className="flex flex-col bg-white pt-6" id="pagination">
          <Pagination
            currentPage={paginationDetails.currentPage}
            itemsPerPage={paginationDetails.limit}
            totalItems={paginationDetails.totalCount || 0}
            onItemsPerPageChange={onItemsPerPageChange}
            onPageChange={onPageChange}
          />
        </section>
      )}
    </div>
  );
};

export default MasterTable;
