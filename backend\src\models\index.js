const School = require("./School");
const Person = require("./Person");
const CsvBatch = require("./CsvBatch");
const User = require("./User");

// Existing relationships
CsvBatch.hasMany(Person, { foreignKey: "csv_batch_id" });
Person.belongsTo(CsvBatch, { foreignKey: "csv_batch_id" });

// School relationships
School.hasMany(Person, { foreignKey: "school_id" });
Person.belongsTo(School, { foreignKey: "school_id" });

// User relationships
School.hasMany(User, { foreignKey: "school_id" });
User.belongsTo(School, { foreignKey: "school_id" });

module.exports = { School, Person, CsvBatch, User };
