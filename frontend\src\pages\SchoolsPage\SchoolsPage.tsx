import React, { useState } from "react";
import { useSchool } from "../../contexts/SchoolContext";
import { SchoolForm, SchoolsList } from "./components";
import SignatureUploadModal from "./components/SignatureUploadModal";
import ValidityDateModal from "./components/ValidityDateModal";
import { useSchoolForm, useSchoolOperations } from "./hooks";
import { Breadcrumb, useBreadcrumb, PageHeader } from "../../components/common";
import { School } from "./types";

const SchoolsPage: React.FC = () => {
  const { schools, refreshSchools } = useSchool();
  const { createBreadcrumbs } = useBreadcrumb();
  const [signatureUploadModal, setSignatureUploadModal] = useState<{
    isVisible: boolean;
    school: School | null;
  }>({
    isVisible: false,
    school: null,
  });

  const [validityDateModal, setValidityDateModal] = useState<{
    isVisible: boolean;
    school: School | null;
  }>({
    isVisible: false,
    school: null,
  });

  const {
    showCreateForm,
    editingSchool,
    formData,
    loading,
    setFormData,
    handleSubmit,
    handleEdit,
    resetForm,
    showForm,
  } = useSchoolForm();

  const { uploadingLogo, handleDelete, handleLogoUpload } =
    useSchoolOperations();

  // Signature upload handlers
  const handleSignatureUpload = (school: School) => {
    setSignatureUploadModal({
      isVisible: true,
      school,
    });
  };

  const handleSignatureUploadSuccess = () => {
    refreshSchools();
  };

  const handleSignatureUploadClose = () => {
    setSignatureUploadModal({
      isVisible: false,
      school: null,
    });
  };

  // Validity date update handlers
  const handleValidityDateUpdate = (school: School) => {
    setValidityDateModal({
      isVisible: true,
      school,
    });
  };

  const handleValidityDateUpdateSuccess = () => {
    refreshSchools();
  };

  const handleValidityDateUpdateClose = () => {
    setValidityDateModal({
      isVisible: false,
      school: null,
    });
  };

  // Create breadcrumb items
  const breadcrumbItems = createBreadcrumbs(
    { label: "Dashboard", path: "/", icon: "mdi:home" },
    { label: "School Management", icon: "mdi:school" }
  );

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbItems} />

      <PageHeader
        title="School Management"
        description="Manage schools in your ID card system"
        buttonText="+ Add School"
        onButtonClick={showForm}
      />

      <SchoolForm
        isVisible={showCreateForm}
        editingSchool={editingSchool}
        formData={formData}
        loading={loading}
        onSubmit={handleSubmit}
        onCancel={resetForm}
        onFormDataChange={setFormData}
      />

      <SchoolsList
        schools={schools}
        uploadingLogo={uploadingLogo}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onLogoUpload={handleLogoUpload}
        onSignatureUpload={handleSignatureUpload}
        onValidityDateUpdate={handleValidityDateUpdate}
        onCreateFirst={showForm}
      />

      {/* Signature Upload Modal */}
      <SignatureUploadModal
        isVisible={signatureUploadModal.isVisible}
        school={signatureUploadModal.school}
        onClose={handleSignatureUploadClose}
        onSuccess={handleSignatureUploadSuccess}
      />

      {/* Validity Date Update Modal */}
      <ValidityDateModal
        isVisible={validityDateModal.isVisible}
        school={validityDateModal.school}
        onClose={handleValidityDateUpdateClose}
        onSuccess={handleValidityDateUpdateSuccess}
      />
    </div>
  );
};

export default SchoolsPage;
