const { QueryInterface, DataTypes } = require('sequelize');

/**
 * Migration to add validity_date column to schools table
 * This migration will:
 * 1. Add validity_date column to schools table
 * 2. Set a default validity date for existing schools
 */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Check if validity_date column already exists
      const tableDescription = await queryInterface.describeTable('schools');
      if (tableDescription.validity_date) {
        console.log('validity_date column already exists, skipping migration');
        await transaction.commit();
        return;
      }

      // Add validity_date column
      await queryInterface.addColumn('schools', 'validity_date', {
        type: DataTypes.DATEONLY,
        allowNull: true,
        comment: 'Validity date for ID cards issued by this school'
      }, { transaction });

      // Set default validity date for existing schools (2 years from now)
      const defaultValidityDate = new Date();
      defaultValidityDate.setFullYear(defaultValidityDate.getFullYear() + 2);
      const formattedDate = defaultValidityDate.toISOString().split('T')[0];

      await queryInterface.sequelize.query(
        'UPDATE schools SET validity_date = ? WHERE validity_date IS NULL',
        {
          replacements: [formattedDate],
          transaction
        }
      );

      await transaction.commit();
      console.log('Successfully added validity_date to schools table');
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      await queryInterface.removeColumn('schools', 'validity_date', { transaction });
      await transaction.commit();
      console.log('Successfully removed validity_date from schools table');
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
