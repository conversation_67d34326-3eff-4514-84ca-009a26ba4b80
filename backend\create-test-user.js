const { User, School } = require("./src/models");
const sequelize = require("./src/config/database");
const { hashPassword } = require("./src/utils/auth");

async function createTestUser() {
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log("Database connection established.");

    // Check if admin user already exists
    const existingAdmin = await User.findOne({
      where: { username: "admin" },
    });

    if (existingAdmin) {
      console.log("Admin user already exists:");
      console.log("Username: admin");
      console.log("Email:", existingAdmin.email);
      console.log("Role:", existingAdmin.role);
    } else {
      // Get or create default school
      let school = await School.findOne();
      if (!school) {
        school = await School.create({
          name: "BIRTA GLOBAL SCHOOL",
          address: "Aduwa Bridge, Atthi Sadan Road, Birtamode",
          phone: "+977-9852672234",
          email: "<EMAIL>",
        });
        console.log("Created default school:", school.name);
      }

      // Hash password for admin
      const adminPasswordHash = await hashPassword("admin123");

      // Create admin user
      const adminUser = await User.create({
        username: "admin",
        email: "<EMAIL>",
        password_hash: adminPasswordHash,
        first_name: "Admin",
        last_name: "User",
        role: "admin",
        school_id: school.id,
        is_active: true,
      });

      console.log("Admin user created successfully!");
      console.log("Username: admin");
      console.log("Password: admin123");
      console.log("Email:", adminUser.email);
      console.log("Role:", adminUser.role);
      console.log("School ID:", adminUser.school_id);
    }

    // Check if rai user already exists
    const existingRai = await User.findOne({
      where: { username: "rai" },
    });

    if (existingRai) {
      console.log("\nRai admin user already exists:");
      console.log("Username: rai");
      console.log("Email:", existingRai.email);
      console.log("Role:", existingRai.role);
    } else {
      // Get school (should exist by now)
      const school = await School.findOne();

      // Hash password for rai
      const raiPasswordHash = await hashPassword("rai123");

      // Create rai admin user
      const raiUser = await User.create({
        username: "rai",
        email: "<EMAIL>",
        password_hash: raiPasswordHash,
        first_name: "Rai",
        last_name: "Admin",
        role: "admin",
        school_id: school.id,
        is_active: true,
      });

      console.log("\nRai admin user created successfully!");
      console.log("Username: rai");
      console.log("Password: rai123");
      console.log("Email:", raiUser.email);
      console.log("Role:", raiUser.role);
      console.log("School ID:", raiUser.school_id);
    }

    await sequelize.close();
    console.log("\nDatabase connection closed.");
    process.exit(0);
  } catch (error) {
    console.error("Error creating users:", error);
    await sequelize.close();
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  createTestUser();
}

module.exports = { createTestUser };
