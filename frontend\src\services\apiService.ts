import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { ApiCache } from "./cache";
import { RequestQueue } from "./requestQueue";
import { debounce } from "lodash";

/**
 * Production-grade API service with caching, request optimization, and error handling
 */
export class ApiService {
  private axiosInstance: AxiosInstance;
  private cache: ApiCache;
  private requestQueue: RequestQueue;
  private baseURL: string;
  private retryAttempts: number = 3;
  private retryDelay: number = 1000;

  constructor() {
    this.baseURL =
      process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";
    this.cache = new ApiCache();
    this.requestQueue = new RequestQueue();

    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: 30000, // 30 seconds timeout
      withCredentials: true,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    });

    this.setupInterceptors();
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem("accessToken");
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request ID for tracing
        config.headers["X-Request-ID"] = this.generateRequestId();

        // Log request in development
        if (process.env.NODE_ENV === "development") {
          console.log(
            `🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`,
            config
          );
        }

        return config;
      },
      (error) => {
        console.error("Request interceptor error:", error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => {
        // Log response in development
        if (process.env.NODE_ENV === "development") {
          console.log(
            `✅ API Response: ${response.config.method?.toUpperCase()} ${
              response.config.url
            }`,
            response
          );
        }

        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle token expiration
        if (
          error.response?.status === 401 &&
          error.response?.data?.code === "TOKEN_EXPIRED"
        ) {
          localStorage.removeItem("accessToken");
          delete this.axiosInstance.defaults.headers.common["Authorization"];

          // Redirect to login or trigger auth refresh
          window.dispatchEvent(new CustomEvent("auth:token-expired"));
          return Promise.reject(error);
        }

        // Retry logic for network errors and 5xx errors
        if (this.shouldRetry(error) && !originalRequest._retry) {
          originalRequest._retry = true;
          originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;

          if (originalRequest._retryCount <= this.retryAttempts) {
            const delay =
              this.retryDelay * Math.pow(2, originalRequest._retryCount - 1); // Exponential backoff
            await this.delay(delay);

            console.warn(
              `Retrying request (${originalRequest._retryCount}/${this.retryAttempts}):`,
              originalRequest.url
            );
            return this.axiosInstance(originalRequest);
          }
        }

        // Log error
        console.error(
          `❌ API Error: ${error.config?.method?.toUpperCase()} ${
            error.config?.url
          }`,
          error
        );

        return Promise.reject(this.normalizeError(error));
      }
    );
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if request should be retried
   */
  private shouldRetry(error: any): boolean {
    return (
      !error.response || // Network error
      error.response.status >= 500 || // Server error
      error.code === "ECONNABORTED" || // Timeout
      error.code === "NETWORK_ERROR"
    );
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Normalize error response
   */
  private normalizeError(error: any): ApiError {
    return {
      message:
        error.response?.data?.error ||
        error.message ||
        "An unexpected error occurred",
      code: error.response?.data?.code || "UNKNOWN_ERROR",
      status: error.response?.status || 0,
      details: error.response?.data?.details,
      requestId: error.config?.headers?.["X-Request-ID"],
    };
  }

  /**
   * GET request with caching support
   */
  async get<T = any>(
    url: string,
    config?: AxiosRequestConfig & {
      cache?: boolean;
      cacheTTL?: number;
      cacheKey?: string;
    }
  ): Promise<ApiResponse<T>> {
    const {
      cache = true,
      cacheTTL = 300,
      cacheKey,
      ...axiosConfig
    } = config || {};
    const finalCacheKey =
      cacheKey || `${url}:${JSON.stringify(axiosConfig.params || {})}`;

    // Try cache first
    if (cache) {
      const cachedData = this.cache.get<T>(finalCacheKey);
      if (cachedData) {
        return {
          data: cachedData,
          cached: true,
          timestamp: Date.now(),
        };
      }
    }

    try {
      const response = await this.axiosInstance.get<T>(url, axiosConfig);

      // Cache successful response
      if (cache && response.status === 200) {
        this.cache.set(finalCacheKey, response.data, cacheTTL);
      }

      return {
        data: response.data,
        cached: false,
        timestamp: Date.now(),
        headers: response.headers,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * POST request with request deduplication
   */
  async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig & { dedupe?: boolean }
  ): Promise<ApiResponse<T>> {
    const { dedupe = false, ...axiosConfig } = config || {};

    if (dedupe) {
      const requestKey = `POST:${url}:${JSON.stringify(data)}`;
      return this.requestQueue.add(requestKey, async () => {
        const response = await this.axiosInstance.post<T>(
          url,
          data,
          axiosConfig
        );
        // Invalidate related cache entries
        this.cache.invalidatePattern(url);
        return {
          data: response.data,
          cached: false,
          timestamp: Date.now(),
          headers: response.headers,
        };
      });
    }

    const response = await this.axiosInstance.post<T>(url, data, axiosConfig);

    // Invalidate related cache entries
    this.cache.invalidatePattern(url);

    return {
      data: response.data,
      cached: false,
      timestamp: Date.now(),
      headers: response.headers,
    };
  }

  /**
   * PUT request
   */
  async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.put<T>(url, data, config);

    // Invalidate related cache entries
    this.cache.invalidatePattern(url);

    return {
      data: response.data,
      cached: false,
      timestamp: Date.now(),
      headers: response.headers,
    };
  }

  /**
   * DELETE request
   */
  async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.delete<T>(url, config);

    // Invalidate related cache entries
    this.cache.invalidatePattern(url);

    return {
      data: response.data,
      cached: false,
      timestamp: Date.now(),
      headers: response.headers,
    };
  }

  /**
   * Batch requests with concurrency control
   */
  async batch<T = any>(
    requests: BatchRequest[],
    options: { concurrency?: number; failFast?: boolean } = {}
  ): Promise<BatchResponse<T>[]> {
    const { concurrency = 5, failFast = false } = options;
    const results: BatchResponse<T>[] = [];

    // Process requests in batches
    for (let i = 0; i < requests.length; i += concurrency) {
      const batch = requests.slice(i, i + concurrency);
      const batchPromises = batch.map(async (request, index) => {
        try {
          const response = await this.axiosInstance.request(request.config);
          return {
            success: true,
            data: response.data,
            index: i + index,
            requestId: request.id,
          };
        } catch (error) {
          if (failFast) {
            throw error;
          }
          return {
            success: false,
            error: this.normalizeError(error),
            index: i + index,
            requestId: request.id,
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Upload file with progress tracking
   */
  async uploadFile<T = any>(
    url: string,
    file: File,
    options: {
      onProgress?: (progress: number) => void;
      additionalData?: Record<string, any>;
      fieldName?: string;
    } = {}
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append(options.fieldName || "file", file);

    // Add additional data if provided
    if (options.additionalData) {
      Object.entries(options.additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    const response = await this.axiosInstance.post<T>(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        if (options.onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          options.onProgress(progress);
        }
      },
    });

    return {
      data: response.data,
      cached: false,
      timestamp: Date.now(),
      headers: response.headers,
    };
  }

  /**
   * Clear cache
   */
  clearCache(pattern?: string): void {
    if (pattern) {
      this.cache.invalidatePattern(pattern);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): any {
    return this.cache.getStats();
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.get("/health", { cache: false });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get base URL for constructing asset URLs
   */
  getBaseURL(): string {
    return this.baseURL;
  }
}

// Types
export interface ApiResponse<T = any> {
  data: T;
  cached: boolean;
  timestamp: number;
  headers?: any;
}

export interface ApiError {
  message: string;
  code: string;
  status: number;
  details?: any;
  requestId?: string;
}

export interface BatchRequest {
  id: string;
  config: AxiosRequestConfig;
}

export interface BatchResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  index: number;
  requestId: string;
}

// Create singleton instance
export const apiService = new ApiService();
