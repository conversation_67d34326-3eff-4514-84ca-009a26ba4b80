import { apiService, ApiResponse } from "./apiService";

/**
 * School service with optimized API calls and caching
 */
export class SchoolService {
  /**
   * Get all schools with caching
   */
  async getSchools(): Promise<ApiResponse<School[]>> {
    return apiService.get<School[]>("/api/school/all", {
      cache: true,
      cacheTTL: 600, // 10 minutes cache - schools don't change frequently
      cacheKey: "schools:all",
    });
  }

  /**
   * Get single school by ID with caching
   */
  async getSchool(id: number): Promise<ApiResponse<School>> {
    return apiService.get<School>(`/api/school/${id}`, {
      cache: true,
      cacheTTL: 600, // 10 minutes cache
      cacheKey: `school:${id}`,
    });
  }

  /**
   * Get school configuration (legacy endpoint)
   */
  async getSchoolConfig(): Promise<ApiResponse<School>> {
    return apiService.get<School>("/api/school/config", {
      cache: true,
      cacheTTL: 300, // 5 minutes cache
      cacheKey: "school:config",
    });
  }

  /**
   * Create new school
   */
  async createSchool(
    schoolData: CreateSchoolData
  ): Promise<ApiResponse<School>> {
    const response = await apiService.post<School>("/api/school", schoolData, {
      dedupe: true, // Prevent duplicate submissions
    });

    // Invalidate schools cache
    apiService.clearCache("schools:*");
    apiService.clearCache("school:*");

    return response;
  }

  /**
   * Update school
   */
  async updateSchool(
    id: number,
    schoolData: UpdateSchoolData
  ): Promise<ApiResponse<School>> {
    const response = await apiService.put<School>(
      `/api/school/${id}`,
      schoolData
    );

    // Invalidate related cache
    apiService.clearCache(`school:${id}`);
    apiService.clearCache("schools:*");
    apiService.clearCache("school:config");

    return response;
  }

  /**
   * Delete school
   */
  async deleteSchool(id: number): Promise<ApiResponse<void>> {
    const response = await apiService.delete<void>(`/api/school/${id}`);

    // Invalidate related cache
    apiService.clearCache(`school:${id}`);
    apiService.clearCache("schools:*");
    apiService.clearCache("school:config");

    return response;
  }

  /**
   * Upload school logo
   */
  async uploadLogo(
    schoolId: number,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{ logo_path: string }>> {
    const response = await apiService.uploadFile<{ logo_path: string }>(
      `/api/school/${schoolId}/logo`,
      file,
      { onProgress, fieldName: "logo" }
    );

    // Invalidate school cache
    apiService.clearCache(`school:${schoolId}`);
    apiService.clearCache("schools:*");

    return response;
  }

  /**
   * Upload school stamp
   */
  async uploadStamp(
    schoolId: number,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{ stamp_path: string }>> {
    const response = await apiService.uploadFile<{ stamp_path: string }>(
      `/api/school/${schoolId}/stamp`,
      file,
      { onProgress, fieldName: "stamp" }
    );

    // Invalidate school cache
    apiService.clearCache(`school:${schoolId}`);
    apiService.clearCache("schools:*");

    return response;
  }

  /**
   * Upload principal signature
   */
  async uploadSignature(
    schoolId: number,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{ signature_path: string }>> {
    const response = await apiService.uploadFile<{ signature_path: string }>(
      `/api/school/${schoolId}/signature`,
      file,
      { onProgress, fieldName: "signature" }
    );

    // Invalidate school cache
    apiService.clearCache(`school:${schoolId}`);
    apiService.clearCache("schools:*");

    return response;
  }

  /**
   * Update validity date for a school
   */
  async updateValidityDate(
    schoolId: number,
    validityDate: string
  ): Promise<ApiResponse<{ validity_date: string }>> {
    const response = await apiService.put<{ validity_date: string }>(
      `/api/school/${schoolId}/validity`,
      { validity_date: validityDate }
    );

    // Invalidate school cache
    apiService.clearCache(`school:${schoolId}`);
    apiService.clearCache("schools:*");

    return response;
  }

  /**
   * Get school statistics
   */
  async getSchoolStats(schoolId: number): Promise<ApiResponse<SchoolStats>> {
    return apiService.get<SchoolStats>(`/api/school/${schoolId}/stats`, {
      cache: true,
      cacheTTL: 180, // 3 minutes cache for stats
      cacheKey: `school-stats:${schoolId}`,
    });
  }

  /**
   * Get school logo URL with fallback
   */
  getSchoolLogoUrl(school: School): string {
    if (school.logo_path) {
      // Use the proper API endpoint with CORS headers instead of direct uploads path
      return `${this.getBaseUrl()}/api/cards/logos/${school.logo_path}`;
    }
    return "/images/default-school-logo.png";
  }

  /**
   * Get school stamp URL with fallback
   */
  getSchoolStampUrl(school: School): string {
    if (school.stamp_path) {
      // Use the proper API endpoint with CORS headers instead of direct uploads path
      return `${this.getBaseUrl()}/api/cards/logos/${school.stamp_path}`;
    }
    return "/images/default-school-stamp.png";
  }

  /**
   * Get principal signature URL with fallback
   */
  getPrincipalSignatureUrl(school: School): string {
    if (school.signature_path) {
      // Use the proper API endpoint with CORS headers instead of direct uploads path
      return `${this.getBaseUrl()}/api/cards/logos/${school.signature_path}`;
    }
    return "/images/default-signature.png";
  }

  /**
   * Validate school data
   */
  validateSchoolData(
    data: CreateSchoolData | UpdateSchoolData
  ): ValidationResult {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push("School name is required");
    }

    if (data.name && data.name.length > 100) {
      errors.push("School name must not exceed 100 characters");
    }

    if (!data.address || data.address.trim().length === 0) {
      errors.push("School address is required");
    }

    if (data.address && data.address.length > 500) {
      errors.push("School address must not exceed 500 characters");
    }

    if (!data.phone || data.phone.trim().length === 0) {
      errors.push("School phone is required");
    }

    if (data.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(data.phone)) {
      errors.push("Invalid phone number format");
    }

    if (data.email && data.email.trim().length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        errors.push("Invalid email format");
      }
      if (data.email.length > 255) {
        errors.push("Email must not exceed 255 characters");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Search schools (if needed for multi-tenant scenarios)
   */
  async searchSchools(query: string): Promise<ApiResponse<School[]>> {
    return apiService.get<School[]>("/api/school/search", {
      params: { q: query },
      cache: true,
      cacheTTL: 300,
      cacheKey: `school-search:${query}`,
    });
  }

  /**
   * Get base URL for asset URLs
   */
  private getBaseUrl(): string {
    return (
      process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com"
    );
  }

  /**
   * Clear all school-related caches
   */
  clearCache(): void {
    apiService.clearCache("school*");
  }

  /**
   * Get service statistics
   */
  getStats(): SchoolServiceStats {
    return {
      cache: apiService.getCacheStats(),
    };
  }
}

// Types
export interface School {
  id: number;
  name: string;
  address: string;
  phone: string;
  email?: string;
  logo_path?: string;
  stamp_path?: string;
  signature_path?: string;
  validity_date?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSchoolData {
  name: string;
  address: string;
  phone: string;
  email?: string;
}

export interface UpdateSchoolData extends Partial<CreateSchoolData> {}

export interface SchoolStats {
  totalPersons: number;
  personsByType: {
    student: number;
    staff: number;
    non_teaching: number;
  };
  recentActivity: {
    date: string;
    count: number;
  }[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface SchoolServiceStats {
  cache: any;
}

// Create singleton instance
export const schoolService = new SchoolService();
