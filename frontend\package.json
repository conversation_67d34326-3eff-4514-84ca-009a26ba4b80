{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@iconify/react": "^6.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.13", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-image-crop": "^8.1.6", "@types/react-router-dom": "^5.3.3", "axios": "^1.10.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "pdf-lib": "^1.17.1", "pdf2pic": "^3.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-image-crop": "^11.0.10", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "cross-env ESLINT_NO_DEV_ERRORS=true react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"cross-env": "^7.0.3"}}