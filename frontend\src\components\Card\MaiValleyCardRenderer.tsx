import React, { useState, useEffect } from "react";
import { CardRendererProps } from "./types";
import ProfileImage from "../common/ProfileImage";

const MaiValleyCardRenderer: React.FC<CardRendererProps> = ({
  config,
  mode = "preview",
  className = "",
  onPhotoError,
  onPhotoLoad,
}) => {
  const [photoBase64, setPhotoBase64] = useState<string>("");
  const [logoBase64, setLogoBase64] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [photoError, setPhotoError] = useState(false);

  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  };

  useEffect(() => {
    const loadAssets = async () => {
      setLoading(true);
      setPhotoError(false);

      try {
        // Load photo if available
        if (config.person.photo_path) {
          const photoResponse = await fetch(
            `${
              process.env.REACT_APP_API_URL ||
              "https://print-api.webstudiomatrix.com"
            }/api/cards/photos/${config.person.photo_path}?t=${Date.now()}`
          ).catch(() => null);
          if (photoResponse && photoResponse.ok) {
            const photoBlob = await photoResponse.blob();
            const photoBase64 = await blobToBase64(photoBlob);
            setPhotoBase64(photoBase64);
            setPhotoError(false);
          } else {
            console.warn("Failed to load photo for:", config.person.name);
            setPhotoError(true);
          }
        }

        // Load school logo if available
        if (config.school.logo_path) {
          const logoResponse = await fetch(
            `${
              process.env.REACT_APP_API_URL ||
              "https://print-api.webstudiomatrix.com"
            }/api/cards/logos/${config.school.logo_path}?t=${Date.now()}`
          ).catch(() => null);
          if (logoResponse && logoResponse.ok) {
            const logoBlob = await logoResponse.blob();
            const logoBase64 = await blobToBase64(logoBlob);
            setLogoBase64(logoBase64);
          }
        }
      } catch (error) {
        console.error("Error loading assets:", error);
      } finally {
        setLoading(false);
      }
    };

    loadAssets();
  }, [config.person.photo_path, config.school.logo_path, config.person.name]);

  // Default placeholder SVG for when no photo is available
  const defaultPhotoSvg = `data:image/svg+xml;base64,${btoa(`
    <svg width="128" height="160" viewBox="0 0 128 160" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="128" height="160" rx="8" fill="#E5E7EB"/>
      <circle cx="64" cy="60" r="20" fill="#9CA3AF"/>
      <path d="M44 120c0-11.046 8.954-20 20-20s20 8.954 20 20v20H44v-20z" fill="#9CA3AF"/>
    </svg>
  `)}`;

  // Default placeholder SVG for when no logo is available
  const defaultLogoSvg = `data:image/svg+xml;base64,${btoa(`
    <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="64" height="64" rx="32" fill="#E5E7EB"/>
      <path d="M20 25h24v14H20z" fill="#9CA3AF"/>
      <path d="M20 39h24v5H20z" fill="#9CA3AF"/>
    </svg>
  `)}`;

  const photoSrc = photoBase64 || defaultPhotoSvg;
  const logoSrc = logoBase64 || defaultLogoSvg;

  if (loading) {
    return (
      <div
        className={`w-80 h-[600px] bg-white shadow-lg rounded overflow-hidden font-sans border ${className} flex items-center justify-center`}>
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  return (
    <div
      className={`w-80 bg-white border-4 rounded-lg overflow-hidden shadow-lg font-sans ${className}`}>
      {/* Header Section */}
      <div className="bg-[#2d3091] px-4 py-3 text-white relative">
        <div className="flex items-center gap-3 mb-2">
          {/* School Logo */}
          <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center flex-shrink-0">
            <div className="relative w-full h-full">
              <img
                src={logoSrc}
                alt="School Logo"
                className="absolute inset-0 object-cover rounded-full w-full h-full overflow-hidden"
                onError={() => console.warn("Logo failed to load")}
              />
            </div>
          </div>

          {/* School Info */}
          <div className="flex-1">
            <div className="text-xs italic mb-1 text-center">
              "Leading towards brightness"
            </div>
            <div className="text-4xl font-black text-center">
              {config.school.name.split(" ")[0] || "MAI VALLEY"}
            </div>
            <div className="text-xl text-center whitespace-normal font-semibold -mt-2">
              {config.school.name.split(" ").slice(1).join(" ") ||
                "BOARDING SCHOOL"}
            </div>
            <div className="text-[#eee231] text-xs mt-1">
              {config.school.address} | {config.school.phone}
            </div>
          </div>
        </div>
      </div>

      {/* Green Stripe */}
      <div className="h-2 bg-[#00a54f]"></div>

      {/* ID Card Label */}
      <div className="bg-[#2d3091] w-fit mx-auto px-6 rounded-b-md text-white text-center py-2">
        <div className="text-xl font-bold">ID CARD</div>
      </div>

      {/* Main Content Area */}
      <div className="bg-white px-6 py-8 relative min-h-64">
        {/* Left Decorative Pattern */}
        <div className="absolute left-4 top-10 w-16 h-16">
          <svg
            width="100"
            height="100"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg">
            <polygon
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
              fill="transparent"
              stroke="#2d3091"
              strokeWidth="3"
            />
          </svg>
        </div>
        <div className="absolute left-4 top-20 w-16 h-16">
          <svg
            width="100"
            height="100"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg">
            <polygon
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
              fill="white"
              stroke="#00a54f"
              strokeWidth="3"
            />
          </svg>
        </div>

        {/* Photo Placeholder */}
        <div className="mx-auto w-32 h-40 bg-gray-200 border-4 border-[#2d3091] rounded-lg overflow-hidden">
          {photoBase64 && !photoError ? (
            <img
              src={photoSrc}
              alt="Person"
              className="object-cover w-full h-full"
              onError={() => {
                console.warn("Image failed to render for:", config.person.name);
                setPhotoError(true);
                onPhotoError?.();
              }}
              onLoad={onPhotoLoad}
            />
          ) : (
            <ProfileImage
              src=""
              name={config.person.name}
              designation={config.person.type}
              className="w-full h-full"
              shape="rounded"
              showInitials={true}
            />
          )}
        </div>

        {/* Right Decorative Pattern */}
        <div className="absolute right-4 top-10 w-16 h-16">
          <svg
            width="100"
            height="100"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg">
            <polygon
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
              fill="transparent"
              stroke="#2d3091"
              strokeWidth="3"
            />
          </svg>
        </div>
        <div className="absolute right-4 top-20 w-16 h-16">
          <svg
            width="100"
            height="100"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg">
            <polygon
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25"
              fill="transparent"
              stroke="#00a54f"
              strokeWidth="3"
            />
          </svg>
        </div>

        {/* Person Information */}
        <div className="mt-6 text-center space-y-2">
          <div className="text-lg font-bold text-[#2d3091]">
            {config.person.name}
          </div>
          {config.person.type === "student" && (
            <>
              {config.person.class && (
                <div className="text-sm">Class: {config.person.class}</div>
              )}
              {config.person.section && (
                <div className="text-sm">Section: {config.person.section}</div>
              )}
              {config.person.roll_number && (
                <div className="text-sm">
                  Roll No: {config.person.roll_number}
                </div>
              )}
            </>
          )}
          {(config.person.type === "staff" ||
            config.person.type === "non_teaching") && (
            <>
              {config.person.department && (
                <div className="text-sm">
                  Department: {config.person.department}
                </div>
              )}
            </>
          )}
          <div className="text-sm">ID: {config.person.person_id}</div>
        </div>
      </div>

      {/* Footer Section */}
      <div className="bg-[#2d3091] text-white flex">
        <div className="flex-1 px-4 py-3">
          <div className="text-sm font-medium">Validity Upto : 2083/03/30</div>
        </div>
        <div className="bg-[#00a54f] px-6 py-3 relative">
          <div className="text-sm font-medium text-center">Principal</div>
          <div className="mt-1 border-b border-green-700 w-16"></div>
        </div>
      </div>
    </div>
  );
};

export default MaiValleyCardRenderer;
