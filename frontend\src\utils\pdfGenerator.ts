/* eslint-disable @typescript-eslint/no-unused-vars */
import { PDFDocument, rgb, StandardFonts } from "pdf-lib";
import { CardConfig } from "../components/Card/types";

// Card dimensions in points (1 inch = 72 points)
// CR80 Standard: 2.125" × 3.375" (54mm × 85.6mm) - VERTICAL orientation matching frontend
const CARD_WIDTH = (5.4 / 2.54) * 72; // 152.88 points (54mm = 2.125" width)
const CARD_HEIGHT = (8.56 / 2.54) * 72; // 242.65 points (85.6mm = 3.375" height)

// Layout constants matching frontend (scaled to PDF dimensions)
const SCALE_FACTOR = CARD_WIDTH / 325; // Scale from 325px frontend to actual card width
const HEADER_HEIGHT = 120 * SCALE_FACTOR; // 120px scaled

const PHOTO_SIZE = 100 * SCALE_FACTOR; // 100px photo scaled
const LOGO_SIZE = 48 * SCALE_FACTOR; // 48px logo scaled (w-12 = 48px)

// Color mapping matching frontend exactly
const getCardColors = (type: string) => {
  switch (type) {
    case "student":
      return {
        bg: rgb(22 / 255, 163 / 255, 74 / 255), // bg-green-600 (#16A34A)
        border: rgb(22 / 255, 163 / 255, 74 / 255),
      };
    case "staff":
      return {
        bg: rgb(37 / 255, 99 / 255, 235 / 255), // bg-blue-600 (#2563EB)
        border: rgb(37 / 255, 99 / 255, 235 / 255),
      };
    case "non_teaching":
      return {
        bg: rgb(234 / 255, 88 / 255, 12 / 255), // bg-orange-600 (#EA580C)
        border: rgb(234 / 255, 88 / 255, 12 / 255),
      };
    default:
      return {
        bg: rgb(22 / 255, 163 / 255, 74 / 255), // Default to green
        border: rgb(22 / 255, 163 / 255, 74 / 255),
      };
  }
};

// Helper function to load image from URL and convert to bytes
const loadImageFromUrl = async (url: string): Promise<Uint8Array | null> => {
  try {
    const response = await fetch(url);
    if (!response.ok) return null;
    const arrayBuffer = await response.arrayBuffer();
    return new Uint8Array(arrayBuffer);
  } catch (error) {
    console.warn("Failed to load image:", error);
    return null;
  }
};

export const generateCardPDF = async (
  config: CardConfig
): Promise<Uint8Array> => {
  // Check if this is a Mai Valley design
  if (config.template.renderMode === "mai_valley") {
    return await generateMaiValleyCardPDF(config);
  }

  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([CARD_WIDTH, CARD_HEIGHT]);

  const { bg: bgColor, border: borderColor } = getCardColors(
    config.person.type
  );

  // Load fonts
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
  const regularFont = await pdfDoc.embedFont(StandardFonts.Helvetica);

  // Card background (white)
  page.drawRectangle({
    x: 0,
    y: 0,
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    color: rgb(1, 1, 1), // White background
  });

  // Draw the curved top section (header) - approximated with rectangle and rounded bottom
  page.drawRectangle({
    x: 0,
    y: CARD_HEIGHT - HEADER_HEIGHT,
    width: CARD_WIDTH,
    height: HEADER_HEIGHT,
    color: bgColor,
  });

  // Load and draw school logo in top left
  try {
    let logoBytes: Uint8Array | null = null;

    if (config.school.logo_path) {
      const logoUrl = `${
        process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com"
      }/api/cards/logos/${config.school.logo_path}`;
      logoBytes = await loadImageFromUrl(logoUrl);
    }

    if (logoBytes) {
      try {
        let logoImage;
        try {
          logoImage = await pdfDoc.embedPng(logoBytes);
        } catch {
          logoImage = await pdfDoc.embedJpg(logoBytes);
        }

        page.drawImage(logoImage, {
          x: 15,
          y: CARD_HEIGHT - HEADER_HEIGHT + 25,
          width: LOGO_SIZE,
          height: LOGO_SIZE,
        });
      } catch (error) {
        console.warn("Failed to embed logo image:", error);
      }
    }
  } catch (error) {
    console.warn("Failed to load logo:", error);
  }

  // Load and draw person photo (circular, overlapping header)
  try {
    let photoBytes: Uint8Array | null = null;

    if (config.person.photo_path) {
      const photoUrl = `${
        process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com"
      }/api/cards/photos/${config.person.photo_path}`;
      photoBytes = await loadImageFromUrl(photoUrl);
    }

    if (photoBytes) {
      try {
        let photoImage;
        try {
          photoImage = await pdfDoc.embedPng(photoBytes);
        } catch {
          photoImage = await pdfDoc.embedJpg(photoBytes);
        }

        const photoX = (CARD_WIDTH - PHOTO_SIZE) / 2;
        const photoY = CARD_HEIGHT - HEADER_HEIGHT - 30;

        page.drawImage(photoImage, {
          x: photoX,
          y: photoY,
          width: PHOTO_SIZE,
          height: PHOTO_SIZE,
        });
      } catch (error) {
        console.warn("Failed to embed photo image:", error);
      }
    }
  } catch (error) {
    console.warn("Failed to load photo:", error);
  }

  // Draw person information in the middle section
  const nameY = 120;
  page.drawText(config.person.name, {
    x: (CARD_WIDTH - config.person.name.length * 6) / 2,
    y: nameY,
    size: 12,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  // Draw footer section
  const footerHeight = 40;
  page.drawRectangle({
    x: 0,
    y: 0,
    width: CARD_WIDTH,
    height: footerHeight,
    color: bgColor,
  });

  page.drawText(config.school.name.toUpperCase(), {
    x: (CARD_WIDTH - config.school.name.length * 4) / 2,
    y: 25,
    size: 8,
    font: boldFont,
    color: rgb(1, 1, 1),
  });

  return await pdfDoc.save();
};

// Generate PDF with multiple cards
export const generateMultipleCardsPDF = async (
  configs: CardConfig[]
): Promise<Uint8Array> => {
  const pdfDoc = await PDFDocument.create();

  // A4 page dimensions in points
  const pageWidth = 595.28; // 210mm
  const pageHeight = 841.89; // 297mm

  // Calculate how many cards fit per page
  const marginX = 20;
  const marginY = 20;
  const spacingX = 10;
  const spacingY = 10;

  const cardsPerRow = Math.floor(
    (pageWidth - 2 * marginX + spacingX) / (CARD_WIDTH + spacingX)
  );
  const cardsPerCol = Math.floor(
    (pageHeight - 2 * marginY + spacingY) / (CARD_HEIGHT + spacingY)
  );
  const cardsPerPage = cardsPerRow * cardsPerCol;

  let currentPage = pdfDoc.addPage([pageWidth, pageHeight]);
  let cardCount = 0;

  // Load fonts once
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
  const regularFont = await pdfDoc.embedFont(StandardFonts.Helvetica);

  for (const config of configs) {
    // Add new page if needed
    if (cardCount > 0 && cardCount % cardsPerPage === 0) {
      currentPage = pdfDoc.addPage([pageWidth, pageHeight]);
    }

    const cardIndex = cardCount % cardsPerPage;
    const row = Math.floor(cardIndex / cardsPerRow);
    const col = cardIndex % cardsPerRow;

    const x = marginX + col * (CARD_WIDTH + spacingX);
    const y =
      pageHeight - marginY - (row + 1) * (CARD_HEIGHT + spacingY) + spacingY;

    await drawSingleCardOnPage(
      currentPage,
      config,
      x,
      y,
      boldFont,
      regularFont
    );

    cardCount++;
  }

  return await pdfDoc.save();
};

// Helper function to draw a single card at specific position on a page
const drawSingleCardOnPage = async (
  page: any,
  config: CardConfig,
  x: number,
  y: number,
  boldFont: any,
  regularFont: any
) => {
  // Check if this is a Mai Valley design
  if (config.template.renderMode === "mai_valley") {
    await drawMaiValleyCardOnPage(page, config, x, y, boldFont, regularFont);
    return;
  }

  const { bg: bgColor, border: borderColor } = getCardColors(
    config.person.type
  );

  // Card background
  page.drawRectangle({
    x,
    y,
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    color: rgb(1, 1, 1),
    borderColor: rgb(0.8, 0.8, 0.8),
    borderWidth: 1,
  });

  // Top colored section
  const headerHeight = 86;
  page.drawRectangle({
    x,
    y: y + CARD_HEIGHT - headerHeight,
    width: CARD_WIDTH,
    height: headerHeight,
    color: bgColor,
  });

  // Load and draw school logo
  try {
    let logoBytes: Uint8Array | null = null;

    if (config.school.logo_path) {
      const logoUrl = `${
        process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com"
      }/api/cards/logos/${config.school.logo_path}`;
      logoBytes = await loadImageFromUrl(logoUrl);
    }

    if (logoBytes) {
      try {
        let logoImage;
        try {
          logoImage = await page.doc.embedPng(logoBytes);
        } catch {
          logoImage = await page.doc.embedJpg(logoBytes);
        }

        const logoSize = 35;
        page.drawImage(logoImage, {
          x: x + 15,
          y: y + CARD_HEIGHT - headerHeight + 25,
          width: logoSize,
          height: logoSize,
        });
      } catch (error) {
        console.warn("Failed to embed logo image:", error);
      }
    }
  } catch (error) {
    console.warn("Failed to load logo:", error);
  }

  // Load and draw person photo
  try {
    let photoBytes: Uint8Array | null = null;

    if (config.person.photo_path) {
      const photoUrl = `${
        process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com"
      }/api/cards/photos/${config.person.photo_path}`;
      photoBytes = await loadImageFromUrl(photoUrl);
    }

    if (photoBytes) {
      try {
        let photoImage;
        try {
          photoImage = await page.doc.embedPng(photoBytes);
        } catch {
          photoImage = await page.doc.embedJpg(photoBytes);
        }

        const photoSize = 72;
        const photoX = x + (CARD_WIDTH - photoSize) / 2;
        const photoY = y + CARD_HEIGHT - headerHeight - 30;

        // Draw white circle background
        page.drawCircle({
          x: photoX + photoSize / 2,
          y: photoY + photoSize / 2,
          size: photoSize / 2 + 3,
          color: rgb(1, 1, 1),
        });

        // Draw colored border
        page.drawCircle({
          x: photoX + photoSize / 2,
          y: photoY + photoSize / 2,
          size: photoSize / 2 + 3,
          borderColor: borderColor,
          borderWidth: 3,
        });

        // Draw photo
        page.drawImage(photoImage, {
          x: photoX,
          y: photoY,
          width: photoSize,
          height: photoSize,
        });
      } catch (error) {
        console.warn("Failed to embed photo image:", error);
      }
    } else {
      // Draw placeholder circle
      const photoSize = 72;
      const photoX = x + (CARD_WIDTH - photoSize) / 2;
      const photoY = y + CARD_HEIGHT - headerHeight - 30;

      page.drawCircle({
        x: photoX + photoSize / 2,
        y: photoY + photoSize / 2,
        size: photoSize / 2,
        color: rgb(0.9, 0.9, 0.9),
        borderColor: borderColor,
        borderWidth: 3,
      });
    }
  } catch (error) {
    console.warn("Failed to load photo:", error);
  }

  // Continue with the rest of the card content...
  await drawCardText(page, config, x, y, boldFont, regularFont, bgColor);
};

// Helper function to draw card text content
const drawCardText = async (
  page: any,
  config: CardConfig,
  x: number,
  y: number,
  boldFont: any,
  regularFont: any,
  bgColor: any
) => {
  // Person name (centered)
  const nameY = y + 120;
  const nameWidth = boldFont.widthOfTextAtSize(config.person.name, 14);
  page.drawText(config.person.name, {
    x: x + (CARD_WIDTH - nameWidth) / 2,
    y: nameY,
    size: 14,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  // Person type (centered)
  const typeLabels = {
    student: "STUDENT",
    staff: "STAFF",
    non_teaching: "NON-TEACHING STAFF",
  };

  const typeText =
    typeLabels[config.person.type as keyof typeof typeLabels] || "MEMBER";
  const typeWidth = boldFont.widthOfTextAtSize(typeText, 10);
  page.drawText(typeText, {
    x: x + (CARD_WIDTH - typeWidth) / 2,
    y: nameY - 20,
    size: 10,
    font: boldFont,
    color: rgb(0.4, 0.4, 0.4),
  });

  // Person details
  let detailY = nameY - 45;
  const lineHeight = 12;

  const details = [];

  if (config.person.type === "student") {
    if (config.person.class) {
      details.push(`Class: ${config.person.class}`);
    }
    if (config.person.roll_number) {
      details.push(`Roll No.: ${config.person.roll_number}`);
    }
    if (config.person.parents_name) {
      details.push(`Parent Name: ${config.person.parents_name}`);
    }
  } else {
    if (config.person.roll_number) {
      details.push(`ID: ${config.person.roll_number}`);
    }
    if (config.person.department || config.person.class) {
      details.push(
        `Department: ${config.person.department || config.person.class}`
      );
    }
  }

  if (config.person.address) {
    details.push(`Address: ${config.person.address}`);
  }
  if (config.person.contact_no) {
    details.push(`Contact No.: ${config.person.contact_no}`);
  }

  details.forEach((detail) => {
    page.drawText(detail, {
      x: x + 10,
      y: detailY,
      size: 8,
      font: regularFont,
      color: rgb(0, 0, 0),
      maxWidth: CARD_WIDTH - 20,
    });
    detailY -= lineHeight;
  });

  // Footer with school details
  const footerHeight = 50;
  page.drawRectangle({
    x,
    y,
    width: CARD_WIDTH,
    height: footerHeight,
    color: bgColor,
  });

  // School name (centered)
  const schoolNameText = config.school.name.toUpperCase();
  const schoolNameWidth = boldFont.widthOfTextAtSize(schoolNameText, 10);
  page.drawText(schoolNameText, {
    x: x + (CARD_WIDTH - schoolNameWidth) / 2,
    y: y + footerHeight - 15,
    size: 10,
    font: boldFont,
    color: rgb(1, 1, 1),
  });

  // School address (centered)
  const addressWidth = regularFont.widthOfTextAtSize(config.school.address, 7);
  page.drawText(config.school.address, {
    x: x + (CARD_WIDTH - addressWidth) / 2,
    y: y + footerHeight - 28,
    size: 7,
    font: regularFont,
    color: rgb(1, 1, 1),
  });

  // School contact (centered)
  const contactText = `Contact No. : ${config.school.phone}`;
  const contactWidth = regularFont.widthOfTextAtSize(contactText, 7);
  page.drawText(contactText, {
    x: x + (CARD_WIDTH - contactWidth) / 2,
    y: y + footerHeight - 40,
    size: 7,
    font: regularFont,
    color: rgb(1, 1, 1),
  });
};

// Generate Mai Valley card PDF
const generateMaiValleyCardPDF = async (
  config: CardConfig
): Promise<Uint8Array> => {
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([CARD_WIDTH, CARD_HEIGHT]);

  // Load fonts
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
  const regularFont = await pdfDoc.embedFont(StandardFonts.Helvetica);

  // Mai Valley colors
  const maiValleyBlue = rgb(45 / 255, 48 / 255, 145 / 255); // #2d3091
  const maiValleyGreen = rgb(0 / 255, 165 / 255, 79 / 255); // #00a54f
  const maiValleyYellow = rgb(238 / 255, 226 / 255, 49 / 255); // #eee231

  // Card background (white)
  page.drawRectangle({
    x: 0,
    y: 0,
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    color: rgb(1, 1, 1),
  });

  // Header section (blue background)
  const headerHeight = 60;
  page.drawRectangle({
    x: 0,
    y: CARD_HEIGHT - headerHeight,
    width: CARD_WIDTH,
    height: headerHeight,
    color: maiValleyBlue,
  });

  // Green stripe
  page.drawRectangle({
    x: 0,
    y: CARD_HEIGHT - headerHeight - 4,
    width: CARD_WIDTH,
    height: 4,
    color: maiValleyGreen,
  });

  // ID Card label
  const labelWidth = 60;
  const labelHeight = 15;
  page.drawRectangle({
    x: (CARD_WIDTH - labelWidth) / 2,
    y: CARD_HEIGHT - headerHeight - 20,
    width: labelWidth,
    height: labelHeight,
    color: maiValleyBlue,
  });

  page.drawText("ID CARD", {
    x: (CARD_WIDTH - labelWidth) / 2 + 10,
    y: CARD_HEIGHT - headerHeight - 15,
    size: 10,
    font: boldFont,
    color: rgb(1, 1, 1),
  });

  // Draw school logo in header (circular white background)
  const logoSize = 25;
  page.drawCircle({
    x: 25,
    y: CARD_HEIGHT - headerHeight / 2,
    size: logoSize / 2,
    color: rgb(1, 1, 1),
  });

  // Load and draw school logo
  try {
    let logoBytes: Uint8Array | null = null;

    if (config.school.logo_path) {
      const logoUrl = `${
        process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com"
      }/api/cards/logos/${config.school.logo_path}`;
      logoBytes = await loadImageFromUrl(logoUrl);
    }

    if (logoBytes) {
      try {
        let logoImage;
        try {
          logoImage = await pdfDoc.embedPng(logoBytes);
        } catch {
          logoImage = await pdfDoc.embedJpg(logoBytes);
        }

        page.drawImage(logoImage, {
          x: 25 - logoSize / 2,
          y: CARD_HEIGHT - headerHeight / 2 - logoSize / 2,
          width: logoSize,
          height: logoSize,
        });
      } catch (error) {
        console.warn("Failed to embed logo image:", error);
      }
    }
  } catch (error) {
    console.warn("Failed to load logo:", error);
  }

  // School name and info in header
  const schoolNameParts = config.school.name.split(" ");
  const firstPart = schoolNameParts[0] || "MAI VALLEY";
  const secondPart = schoolNameParts.slice(1).join(" ") || "BOARDING SCHOOL";

  page.drawText('"Leading towards brightness"', {
    x: 60,
    y: CARD_HEIGHT - 15,
    size: 6,
    font: regularFont,
    color: rgb(1, 1, 1),
  });

  page.drawText(firstPart, {
    x: 60,
    y: CARD_HEIGHT - 30,
    size: 14,
    font: boldFont,
    color: rgb(1, 1, 1),
  });

  page.drawText(secondPart, {
    x: 60,
    y: CARD_HEIGHT - 45,
    size: 10,
    font: boldFont,
    color: rgb(1, 1, 1),
  });

  page.drawText(`${config.school.address} | ${config.school.phone}`, {
    x: 60,
    y: CARD_HEIGHT - 55,
    size: 6,
    font: regularFont,
    color: maiValleyYellow,
  });

  // Photo placeholder
  const photoSize = 50;
  const photoX = (CARD_WIDTH - photoSize) / 2;
  const photoY = CARD_HEIGHT - 140;

  page.drawRectangle({
    x: photoX,
    y: photoY,
    width: photoSize,
    height: photoSize * 1.25, // 4:5 ratio
    color: rgb(0.9, 0.9, 0.9),
    borderColor: maiValleyBlue,
    borderWidth: 2,
  });

  // Load and draw person photo
  try {
    let photoBytes: Uint8Array | null = null;

    if (config.person.photo_path) {
      const photoUrl = `${
        process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com"
      }/api/cards/photos/${config.person.photo_path}`;
      photoBytes = await loadImageFromUrl(photoUrl);
    }

    if (photoBytes) {
      try {
        let photoImage;
        try {
          photoImage = await pdfDoc.embedPng(photoBytes);
        } catch {
          photoImage = await pdfDoc.embedJpg(photoBytes);
        }

        page.drawImage(photoImage, {
          x: photoX,
          y: photoY,
          width: photoSize,
          height: photoSize * 1.25,
        });
      } catch (error) {
        console.warn("Failed to embed photo image:", error);
      }
    }
  } catch (error) {
    console.warn("Failed to load photo:", error);
  }

  // Person information
  const infoY = photoY - 30;
  page.drawText(config.person.name, {
    x: (CARD_WIDTH - config.person.name.length * 4) / 2,
    y: infoY,
    size: 12,
    font: boldFont,
    color: maiValleyBlue,
  });

  let currentY = infoY - 15;
  if (config.person.type === "student") {
    if (config.person.class) {
      page.drawText(`Class: ${config.person.class}`, {
        x: (CARD_WIDTH - `Class: ${config.person.class}`.length * 3) / 2,
        y: currentY,
        size: 8,
        font: regularFont,
        color: rgb(0, 0, 0),
      });
      currentY -= 12;
    }
    if (config.person.roll_number) {
      page.drawText(`Roll No: ${config.person.roll_number}`, {
        x:
          (CARD_WIDTH - `Roll No: ${config.person.roll_number}`.length * 3) / 2,
        y: currentY,
        size: 8,
        font: regularFont,
        color: rgb(0, 0, 0),
      });
      currentY -= 12;
    }
  }

  page.drawText(`ID: ${config.person.person_id}`, {
    x: (CARD_WIDTH - `ID: ${config.person.person_id}`.length * 3) / 2,
    y: currentY,
    size: 8,
    font: regularFont,
    color: rgb(0, 0, 0),
  });

  // Footer section
  const footerHeight = 25;
  page.drawRectangle({
    x: 0,
    y: 0,
    width: CARD_WIDTH * 0.7,
    height: footerHeight,
    color: maiValleyBlue,
  });

  page.drawRectangle({
    x: CARD_WIDTH * 0.7,
    y: 0,
    width: CARD_WIDTH * 0.3,
    height: footerHeight,
    color: maiValleyGreen,
  });

  page.drawText("Validity Upto : 2083/03/30", {
    x: 10,
    y: 10,
    size: 7,
    font: regularFont,
    color: rgb(1, 1, 1),
  });

  page.drawText("Principal", {
    x: CARD_WIDTH * 0.75,
    y: 10,
    size: 7,
    font: regularFont,
    color: rgb(1, 1, 1),
  });

  return await pdfDoc.save();
};

// Helper function to draw a Mai Valley card at specific position on a page
const drawMaiValleyCardOnPage = async (
  page: any,
  config: CardConfig,
  x: number,
  y: number,
  boldFont: any,
  regularFont: any
) => {
  // Mai Valley colors
  const maiValleyBlue = rgb(45 / 255, 48 / 255, 145 / 255); // #2d3091
  const maiValleyGreen = rgb(0 / 255, 165 / 255, 79 / 255); // #00a54f
  const maiValleyYellow = rgb(238 / 255, 226 / 255, 49 / 255); // #eee231

  // Card background (white)
  page.drawRectangle({
    x,
    y,
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    color: rgb(1, 1, 1),
    borderColor: rgb(0.8, 0.8, 0.8),
    borderWidth: 1,
  });

  // Header section (blue background)
  const headerHeight = 60;
  page.drawRectangle({
    x,
    y: y + CARD_HEIGHT - headerHeight,
    width: CARD_WIDTH,
    height: headerHeight,
    color: maiValleyBlue,
  });

  // Green stripe
  page.drawRectangle({
    x,
    y: y + CARD_HEIGHT - headerHeight - 4,
    width: CARD_WIDTH,
    height: 4,
    color: maiValleyGreen,
  });

  // ID Card label
  const labelWidth = 60;
  const labelHeight = 15;
  page.drawRectangle({
    x: x + (CARD_WIDTH - labelWidth) / 2,
    y: y + CARD_HEIGHT - headerHeight - 20,
    width: labelWidth,
    height: labelHeight,
    color: maiValleyBlue,
  });

  page.drawText("ID CARD", {
    x: x + (CARD_WIDTH - labelWidth) / 2 + 10,
    y: y + CARD_HEIGHT - headerHeight - 15,
    size: 10,
    font: boldFont,
    color: rgb(1, 1, 1),
  });

  // Draw school logo in header (circular white background)
  const logoSize = 25;
  page.drawCircle({
    x: x + 25,
    y: y + CARD_HEIGHT - headerHeight / 2,
    size: logoSize / 2,
    color: rgb(1, 1, 1),
  });

  // Load and draw school logo
  try {
    let logoBytes: Uint8Array | null = null;

    if (config.school.logo_path) {
      const logoUrl = `${
        process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com"
      }/api/cards/photos/${config.school.logo_path}`;
      logoBytes = await loadImageFromUrl(logoUrl);
    }

    if (logoBytes) {
      try {
        let logoImage;
        try {
          logoImage = await page.doc.embedPng(logoBytes);
        } catch {
          logoImage = await page.doc.embedJpg(logoBytes);
        }

        page.drawImage(logoImage, {
          x: x + 25 - logoSize / 2,
          y: y + CARD_HEIGHT - headerHeight / 2 - logoSize / 2,
          width: logoSize,
          height: logoSize,
        });
      } catch (error) {
        console.warn("Failed to embed logo image:", error);
      }
    }
  } catch (error) {
    console.warn("Failed to load logo:", error);
  }

  // School name and info in header
  const schoolNameParts = config.school.name.split(" ");
  const firstPart = schoolNameParts[0] || "MAI VALLEY";
  const secondPart = schoolNameParts.slice(1).join(" ") || "BOARDING SCHOOL";

  page.drawText('"Leading towards brightness"', {
    x: x + 60,
    y: y + CARD_HEIGHT - 15,
    size: 6,
    font: regularFont,
    color: rgb(1, 1, 1),
  });

  page.drawText(firstPart, {
    x: x + 60,
    y: y + CARD_HEIGHT - 30,
    size: 14,
    font: boldFont,
    color: rgb(1, 1, 1),
  });

  page.drawText(secondPart, {
    x: x + 60,
    y: y + CARD_HEIGHT - 45,
    size: 10,
    font: boldFont,
    color: rgb(1, 1, 1),
  });

  page.drawText(`${config.school.address} | ${config.school.phone}`, {
    x: x + 60,
    y: y + CARD_HEIGHT - 55,
    size: 6,
    font: regularFont,
    color: maiValleyYellow,
  });

  // Photo placeholder
  const photoSize = 50;
  const photoX = x + (CARD_WIDTH - photoSize) / 2;
  const photoY = y + CARD_HEIGHT - 140;

  page.drawRectangle({
    x: photoX,
    y: photoY,
    width: photoSize,
    height: photoSize * 1.25, // 4:5 ratio
    color: rgb(0.9, 0.9, 0.9),
    borderColor: maiValleyBlue,
    borderWidth: 2,
  });

  // Load and draw person photo
  try {
    let photoBytes: Uint8Array | null = null;

    if (config.person.photo_path) {
      const photoUrl = `${
        process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com"
      }/api/cards/photos/${config.person.photo_path}`;
      photoBytes = await loadImageFromUrl(photoUrl);
    }

    if (photoBytes) {
      try {
        let photoImage;
        try {
          photoImage = await page.doc.embedPng(photoBytes);
        } catch {
          photoImage = await page.doc.embedJpg(photoBytes);
        }

        page.drawImage(photoImage, {
          x: photoX,
          y: photoY,
          width: photoSize,
          height: photoSize * 1.25,
        });
      } catch (error) {
        console.warn("Failed to embed photo image:", error);
      }
    }
  } catch (error) {
    console.warn("Failed to load photo:", error);
  }

  // Person information
  const infoY = photoY - 30;
  page.drawText(config.person.name, {
    x: x + (CARD_WIDTH - config.person.name.length * 4) / 2,
    y: infoY,
    size: 12,
    font: boldFont,
    color: maiValleyBlue,
  });

  let currentY = infoY - 15;
  if (config.person.type === "student") {
    if (config.person.class) {
      page.drawText(`Class: ${config.person.class}`, {
        x: x + (CARD_WIDTH - `Class: ${config.person.class}`.length * 3) / 2,
        y: currentY,
        size: 8,
        font: regularFont,
        color: rgb(0, 0, 0),
      });
      currentY -= 12;
    }
    if (config.person.roll_number) {
      page.drawText(`Roll No: ${config.person.roll_number}`, {
        x:
          x +
          (CARD_WIDTH - `Roll No: ${config.person.roll_number}`.length * 3) / 2,
        y: currentY,
        size: 8,
        font: regularFont,
        color: rgb(0, 0, 0),
      });
      currentY -= 12;
    }
  }

  page.drawText(`ID: ${config.person.person_id}`, {
    x: x + (CARD_WIDTH - `ID: ${config.person.person_id}`.length * 3) / 2,
    y: currentY,
    size: 8,
    font: regularFont,
    color: rgb(0, 0, 0),
  });

  // Footer section
  const footerHeight = 25;
  page.drawRectangle({
    x,
    y,
    width: CARD_WIDTH * 0.7,
    height: footerHeight,
    color: maiValleyBlue,
  });

  page.drawRectangle({
    x: x + CARD_WIDTH * 0.7,
    y,
    width: CARD_WIDTH * 0.3,
    height: footerHeight,
    color: maiValleyGreen,
  });

  page.drawText("Validity Upto : 2083/03/30", {
    x: x + 10,
    y: y + 10,
    size: 7,
    font: regularFont,
    color: rgb(1, 1, 1),
  });

  page.drawText("Principal", {
    x: x + CARD_WIDTH * 0.75,
    y: y + 10,
    size: 7,
    font: regularFont,
    color: rgb(1, 1, 1),
  });
};
