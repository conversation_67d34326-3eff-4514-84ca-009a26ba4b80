import React from "react";

interface LoadingCardProps {
  layout?: "layout1" | "layout2" | "layout3" | "layout4";
}

const LoadingCard: React.FC<LoadingCardProps> = ({ layout = "layout1" }) => {
  if (layout === "layout4") {
    // Layout4 loading card - credit card style
    return (
      <div className="w-[204px] h-[324px] bg-white shadow-lg overflow-hidden font-sans text-black relative animate-pulse">
        {/* Blue curved background placeholder */}
        <div className="absolute inset-0 bg-gray-400 w-[40%]"></div>

        {/* School logo placeholder in top-left blue area */}
        <div className="absolute top-2 left-2 z-10">
          <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
        </div>

        {/* School info placeholder in top-right */}
        <div className="absolute top-2 right-2 z-10 text-right space-y-1">
          <div className="h-3 bg-gray-300 rounded w-20"></div>
          <div className="h-2 bg-gray-300 rounded w-16"></div>
          <div className="h-2 bg-gray-300 rounded w-14"></div>
        </div>

        {/* Large "B" placeholder in blue area - positioned lower */}
        <div className="absolute left-6 top-[60%] transform -translate-y-1/2 z-10">
          <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
        </div>

        {/* Main content area */}
        <div className="relative z-10 pt-16 px-3 pb-3 h-full flex flex-col">
          {/* Photo placeholder */}
          <div className="flex justify-center mb-4">
            <div className="w-20 h-24 bg-gray-300"></div>
          </div>

          {/* Name placeholder */}
          <div className="text-center mb-3">
            <div className="h-4 bg-gray-300 rounded w-3/4 mx-auto"></div>
          </div>

          {/* Info lines */}
          <div className="space-y-1 flex-grow">
            <div className="flex">
              <div className="h-3 bg-gray-300 rounded w-16"></div>
              <div className="h-3 bg-gray-200 rounded flex-1 ml-1"></div>
            </div>
            <div className="flex">
              <div className="h-3 bg-gray-300 rounded w-16"></div>
              <div className="h-3 bg-gray-200 rounded flex-1 ml-1"></div>
            </div>
            <div className="flex">
              <div className="h-3 bg-gray-300 rounded w-16"></div>
              <div className="h-3 bg-gray-200 rounded flex-1 ml-1"></div>
            </div>
            <div className="flex">
              <div className="h-3 bg-gray-300 rounded w-16"></div>
              <div className="h-3 bg-gray-200 rounded flex-1 ml-1"></div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-between items-end mt-auto">
            <div className="h-2 bg-gray-300 rounded w-1/3"></div>
            <div className="space-y-1 text-right">
              <div className="h-px bg-gray-300 w-16"></div>
              <div className="h-2 bg-gray-300 rounded w-12"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (layout === "layout2" || layout === "layout3") {
    return (
      <div className="w-[204px] h-[324px] bg-white shadow-lg overflow-hidden font-sans text-black flex flex-col animate-pulse">
        {/* Header with background */}
        <div className="relative h-[140px] flex-shrink-0 bg-gray-300">
          {/* Logo placeholder */}
          <div className="absolute bg-gray-400 w-[32px] h-[32px] top-3 left-8 rounded-full"></div>
          {/* Profile image placeholder */}
          <div className="absolute w-[68px] h-[68px] -translate-y-[83px] translate-x-[79px] rounded-full bg-gray-400"></div>
        </div>

        {/* Student info */}
        <div className="px-3 mt-0 text-center flex-grow flex flex-col justify-center space-y-2">
          <div className="h-4 bg-gray-300 rounded w-3/4 mx-auto"></div>
          <div className="space-y-1">
            <div className="h-3 bg-gray-200 rounded w-full"></div>
            <div className="h-3 bg-gray-200 rounded w-full"></div>
            <div className="h-3 bg-gray-200 rounded w-full"></div>
            {layout === "layout2" && (
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="h-[60px] bg-gray-300 flex items-center justify-between px-3">
          <div className="h-3 bg-gray-400 rounded w-1/3"></div>
          <div className="h-8 w-12 bg-gray-400 rounded"></div>
        </div>
      </div>
    );
  }

  // Layout1 loading card
  return (
    <div className="h-[324px] w-[204px] bg-white rounded-sm overflow-hidden shadow-lg flex flex-col animate-pulse">
      {/* Header */}
      <div className="bg-gray-300 px-3 py-2 text-center space-y-1">
        <div className="h-6 bg-gray-400 rounded w-12 mx-auto"></div>
        <div className="h-4 bg-gray-400 rounded w-3/4 mx-auto"></div>
        <div className="h-3 bg-gray-400 rounded w-1/2 mx-auto"></div>
        <div className="h-3 bg-gray-400 rounded w-2/3 mx-auto"></div>
      </div>

      {/* Student info */}
      <div className="bg-white px-3 pt-1 pb-0 relative flex-grow overflow-hidden flex flex-col">
        <div className="flex-grow flex flex-col justify-center">
          {/* Profile image placeholder */}
          <div className="mx-auto w-20 h-24 bg-gray-300 border-2 border-gray-400 rounded-lg mt-2"></div>

          {/* Info lines */}
          <div className="text-left mt-3 space-y-1">
            <div className="flex gap-1">
              <div className="h-3 bg-gray-300 rounded w-12"></div>
              <div className="h-3 bg-gray-200 rounded flex-1"></div>
            </div>
            <div className="flex gap-1">
              <div className="h-3 bg-gray-300 rounded w-12"></div>
              <div className="h-3 bg-gray-200 rounded flex-1"></div>
            </div>
            <div className="flex gap-1">
              <div className="h-3 bg-gray-300 rounded w-12"></div>
              <div className="h-3 bg-gray-200 rounded flex-1"></div>
            </div>
            <div className="flex gap-1">
              <div className="h-3 bg-gray-300 rounded w-16"></div>
              <div className="h-3 bg-gray-200 rounded flex-1"></div>
            </div>
            <div className="flex gap-1">
              <div className="h-3 bg-gray-300 rounded w-14"></div>
              <div className="h-3 bg-gray-200 rounded flex-1"></div>
            </div>
            <div className="flex gap-1">
              <div className="h-3 bg-gray-300 rounded w-12"></div>
              <div className="h-3 bg-gray-200 rounded flex-1"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-300 px-3 py-2 flex justify-between items-center">
        <div className="h-3 bg-gray-400 rounded w-1/3"></div>
        <div className="h-6 w-16 bg-gray-400 rounded"></div>
      </div>
    </div>
  );
};

export default LoadingCard;
