// Interface for ID Card field visibility configuration
export interface IDCardFieldVisibility {
  name: boolean;
  class: boolean;
  section: boolean;
  roll_number: boolean;
  parents_name: boolean;
  contact_no: boolean;
  department: boolean;
  designation: boolean;
  address: boolean;
  date_of_birth: boolean;
}

// Types for Layout1 components
export interface IdCardProps {
  id?: number;
  schoolName: string;
  motto: string;
  subname: string;
  contact: string;
  validity: string;
  logo: string;
  image: string;
  studentName: string;
  studentclass?: string;
  roll: string;
  idNumber: string;
  address: string;
  phone: string;
  principalSignature: string;
  personType: "student" | "staff" | "non_teaching";
  parentName?: string;
  department?: string;
  fieldVisibilityConfig?: IDCardFieldVisibility;
}

export interface StudentData {
  studentName: string;
  studentclass?: string;
  roll: string;
  address: string;
  phone: string;
  personType: "student" | "staff" | "non_teaching";
  parentName?: string;
  department?: string;
  fieldVisibilityConfig?: IDCardFieldVisibility;
}

export interface HeaderProps {
  logo: string;
  motto: string;
  schoolName: string;
  subname: string;
  contact: string;
}

export interface FooterProps {
  validity: string;
  principalSignature: string;
}
