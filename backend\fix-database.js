// Fix database schema by adding missing validity_date column
const sequelize = require('./src/config/database');

async function fixDatabase() {
  try {
    console.log('Fixing database schema...');
    
    // Add the missing validity_date column to schools table
    await sequelize.query(`
      ALTER TABLE schools 
      ADD COLUMN validity_date DATE;
    `);
    
    console.log('✅ Added validity_date column to schools table');
    
    // Test the fix by querying schools
    const { School } = require('./src/models');
    const schools = await School.findAll();
    console.log('✅ Database fix successful - found', schools.length, 'schools');
    
    if (schools.length === 0) {
      console.log('Creating default school...');
      const school = await School.create({
        name: "BIRTA GLOBAL SCHOOL",
        address: "Aduwa Bridge, Atthi Sadan Road, Birtamode",
        phone: "+977-9852672234",
        email: "<EMAIL>",
        validity_date: "2025-12-31"
      });
      console.log('✅ Default school created with ID:', school.id);
    }
    
    console.log('Database fix completed successfully!');
    process.exit(0);
    
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('✅ Column already exists, checking if database works...');
      
      try {
        const { School } = require('./src/models');
        const schools = await School.findAll();
        console.log('✅ Database is working - found', schools.length, 'schools');
        process.exit(0);
      } catch (testError) {
        console.log('❌ Database still has issues:', testError.message);
        process.exit(1);
      }
    } else {
      console.log('❌ Failed to fix database:', error.message);
      console.log('Full error:', error);
      process.exit(1);
    }
  }
}

fixDatabase();
