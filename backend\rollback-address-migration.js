const sequelize = require("./src/config/database");
const migration = require("./src/migrations/add-address-to-persons.js");

async function rollbackAddressMigration() {
  try {
    console.log("Starting address migration rollback...");
    
    // Authenticate database connection
    await sequelize.authenticate();
    console.log("Database connection established successfully.");

    // Check if address column exists before rollback
    console.log("Checking current table structure...");
    const tableDescription = await sequelize.getQueryInterface().describeTable('persons');
    
    if (!tableDescription.address) {
      console.log("Address column does not exist. Nothing to rollback.");
      await sequelize.close();
      return;
    }

    console.log("Address column found. Proceeding with rollback...");

    // Run the Sequelize migration rollback
    console.log("Running add-address-to-persons migration rollback...");
    await migration.down(sequelize.getQueryInterface(), sequelize);
    
    console.log("Address migration rollback completed successfully.");
    
    // Verify the rollback by checking table structure
    console.log("Verifying rollback...");
    const updatedTableDescription = await sequelize.getQueryInterface().describeTable('persons');
    
    if (!updatedTableDescription.address) {
      console.log("✓ Address column successfully removed from persons table");
    } else {
      console.log("✗ Address column still exists after rollback");
    }

    await sequelize.close();
    console.log("Rollback process completed.");
  } catch (error) {
    console.error("Rollback failed:", error);
    console.error("Error details:", error.message);
    if (error.sql) {
      console.error("SQL:", error.sql);
    }
    
    try {
      await sequelize.close();
    } catch (closeError) {
      console.error("Error closing database connection:", closeError);
    }
    
    process.exit(1);
  }
}

rollbackAddressMigration();
