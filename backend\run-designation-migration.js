const sequelize = require('./src/config/database');
const migration = require('./src/migrations/add-designation-to-persons');

async function runMigration() {
  try {
    console.log('Starting migration: add-designation-to-persons');
    
    // Connect to database
    await sequelize.authenticate();
    console.log('Database connection established successfully.');
    
    // Run the migration
    await migration.up(sequelize.getQueryInterface(), sequelize);
    
    console.log('Migration completed successfully');
    
    // Close database connection
    await sequelize.close();
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    await sequelize.close();
    process.exit(1);
  }
}

runMigration();
