import { BgColorType, StudentData } from "./types";

interface CardFooterProps {
  bgColor: BgColorType;
  data: StudentData;
}

const CardFooter = ({ bgColor, data }: CardFooterProps) => {
  const gradientClasses = {
    green: "bg-gradient-to-r from-[#5BAE44] to-[#379140]",
    blue: "bg-gradient-to-r from-[#1998BB] to-[#2C6088]",
    orange: "bg-gradient-to-r from-[#F06024] to-[#EF4F26]",
  };

  return (
    <div
      className={`w-full ${gradientClasses[bgColor]} text-white py-1 px-2 text-center text-[8px] font-medium mt-0`}
    >
      <div className="mb-0 text-[10px] font-bold tracking-wide uppercase whitespace-nowrap overflow-hidden text-ellipsis">
        {data.schoolName}
      </div>
      <div className="text-[7px] font-semibold tracking-wide whitespace-nowrap overflow-hidden text-ellipsis">
        {data.location}
      </div>
      <div>Contact No. : {data.contact}</div>
    </div>
  );
};

export default CardFooter;
